-- ============================================================================
-- FIX USERS/INDIVIDUALS COLUMN SEPARATION
-- Move individual-specific columns from users table to individuals table
-- ============================================================================

-- Step 1: Add new columns to individuals table for data that should be moved
ALTER TABLE individuals
ADD COLUMN personal_skills JSONB DEFAULT '[]',
ADD COLUMN learning_preferences JSONB DEFAULT '{}',
ADD COLUMN industry_interests TEXT[];

-- Step 2: Migrate existing data from users to individuals
-- First, ensure all users with individual-specific data have an individuals record
INSERT INTO individuals (user_id, personal_skills, learning_preferences, industry_interests)
SELECT
    u.id,
    u.personal_skills,
    u.learning_preferences,
    u.industry_interests
FROM users u
WHERE u.role IN ('individual', 'org_member')
  AND NOT EXISTS (SELECT 1 FROM individuals i WHERE i.user_id = u.id)
  AND (
    u.personal_skills != '[]'::jsonb OR
    u.learning_preferences != '{}'::jsonb OR
    u.industry_interests IS NOT NULL
  );

-- Step 3: Update existing individuals records with data from users
UPDATE individuals
SET
    personal_skills = COALESCE(u.personal_skills, individuals.personal_skills),
    learning_preferences = COALESCE(u.learning_preferences, individuals.learning_preferences),
    industry_interests = COALESCE(u.industry_interests, individuals.industry_interests)
FROM users u
WHERE individuals.user_id = u.id;

-- Step 4: Remove individual-specific columns from users table
ALTER TABLE users
DROP COLUMN personal_skills,
DROP COLUMN learning_preferences,
DROP COLUMN industry_interests,
DROP COLUMN phone_number; -- This should be in individuals.contact_info

-- Step 5: Add phone_number to individuals contact_info (for existing data)
-- Note: This would need to be done manually for existing phone numbers
-- UPDATE individuals SET contact_info = jsonb_set(contact_info, '{phone}', '"existing_phone"');