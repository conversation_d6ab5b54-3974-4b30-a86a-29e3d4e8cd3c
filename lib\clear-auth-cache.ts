/**
 * Comprehensive Authentication Cache Clearing Utility
 * Clears all authentication-related cached data
 */

import { supabase } from '@/lib/supabase';
import { invalidateAllCaches } from '@/lib/cache-manager';
import { clearRateLimit } from '@/lib/auth-rate-limit';

/**
 * Clear all browser storage related to authentication
 */
export function clearBrowserStorage(): void {
  try {
    // Clear localStorage
    const keysToRemove: string[] = [];
    for (let i = 0; i < localStorage.length; i++) {
      const key = localStorage.key(i);
      if (key && (
        key.includes('supabase') ||
        key.includes('auth') ||
        key.includes('session') ||
        key.includes('user') ||
        key.includes('bpo-') ||
        key.includes('sb-')
      )) {
        keysToRemove.push(key);
      }
    }
    keysToRemove.forEach(key => localStorage.removeItem(key));
    
    // Clear sessionStorage
    const sessionKeysToRemove: string[] = [];
    for (let i = 0; i < sessionStorage.length; i++) {
      const key = sessionStorage.key(i);
      if (key && (
        key.includes('supabase') ||
        key.includes('auth') ||
        key.includes('session') ||
        key.includes('user') ||
        key.includes('bpo-') ||
        key.includes('sb-')
      )) {
        sessionKeysToRemove.push(key);
      }
    }
    sessionKeysToRemove.forEach(key => sessionStorage.removeItem(key));
    
    console.log('✅ Browser storage cleared');
  } catch (error) {
    console.error('Error clearing browser storage:', error);
  }
}

/**
 * Clear all cookies related to authentication
 */
export function clearAuthCookies(): void {
  try {
    // Get all cookies
    const cookies = document.cookie.split(';');
    
    // Clear Supabase and auth-related cookies
    cookies.forEach(cookie => {
      const eqPos = cookie.indexOf('=');
      const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
      
      if (name.includes('supabase') || 
          name.includes('auth') || 
          name.includes('session') ||
          name.includes('sb-') ||
          name.includes('bpo-')) {
        // Clear cookie by setting it to expire in the past
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=${window.location.hostname}`;
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/`;
        document.cookie = `${name}=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/; domain=.${window.location.hostname}`;
      }
    });
    
    console.log('✅ Auth cookies cleared');
  } catch (error) {
    console.error('Error clearing cookies:', error);
  }
}

/**
 * Clear application caches
 */
export function clearApplicationCaches(): void {
  try {
    // Clear all cache managers
    invalidateAllCaches();
    
    // Clear rate limiting cache
    clearRateLimit(); // Clear for anonymous
    clearRateLimit('<EMAIL>'); // Clear for current user
    clearRateLimit('<EMAIL>'); // Clear for new user
    
    console.log('✅ Application caches cleared');
  } catch (error) {
    console.error('Error clearing application caches:', error);
  }
}

/**
 * Sign out from Supabase
 */
export async function signOutFromSupabase(): Promise<void> {
  try {
    await supabase.auth.signOut();
    console.log('✅ Signed out from Supabase');
  } catch (error) {
    console.error('Error signing out from Supabase:', error);
  }
}

/**
 * Clear browser cache and service workers
 */
export async function clearBrowserCache(): Promise<void> {
  try {
    // Clear cache storage if available
    if ('caches' in window) {
      const cacheNames = await caches.keys();
      await Promise.all(
        cacheNames.map(cacheName => caches.delete(cacheName))
      );
      console.log('✅ Browser cache cleared');
    }
    
    // Unregister service workers
    if ('serviceWorker' in navigator) {
      const registrations = await navigator.serviceWorker.getRegistrations();
      await Promise.all(
        registrations.map(registration => registration.unregister())
      );
      console.log('✅ Service workers unregistered');
    }
  } catch (error) {
    console.error('Error clearing browser cache:', error);
  }
}

/**
 * Comprehensive authentication cache clearing
 * Call this function to clear all authentication-related data
 */
export async function clearAllAuthCache(): Promise<void> {
  console.log('🧹 Starting comprehensive auth cache clearing...');
  
  try {
    // 1. Sign out from Supabase first
    await signOutFromSupabase();
    
    // 2. Clear browser storage
    clearBrowserStorage();
    
    // 3. Clear cookies
    clearAuthCookies();
    
    // 4. Clear application caches
    clearApplicationCaches();
    
    // 5. Clear browser cache and service workers
    await clearBrowserCache();
    
    console.log('✅ All authentication cache cleared successfully!');
    
    // 6. Force a page reload to ensure clean state
    setTimeout(() => {
      window.location.href = '/login';
    }, 1000);
    
  } catch (error) {
    console.error('❌ Error during cache clearing:', error);
    // Still try to redirect to login
    setTimeout(() => {
      window.location.href = '/login';
    }, 1000);
  }
}

/**
 * Quick logout and cache clear
 */
export async function quickLogoutAndClear(): Promise<void> {
  await clearAllAuthCache();
}
