const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testDatabaseInsert() {
  console.log('🧪 Testing Database Insert for Files Table...\n');

  try {
    // 1. First, let's check if we have any prospects to test with
    console.log('1️⃣ Checking for existing prospects...');
    const { data: prospects, error: prospectsError } = await supabase
      .from('prospects')
      .select('id, user_id')
      .limit(1);

    if (prospectsError) {
      console.error('❌ Error fetching prospects:', prospectsError);
      return;
    }

    if (!prospects || prospects.length === 0) {
      console.log('❌ No prospects found in database');
      return;
    }

    const testProspect = prospects[0];
    console.log('✅ Found test prospect:', testProspect);

    // 2. Test the exact same data structure we're using in the upload API
    console.log('\n2️⃣ Testing file insert with same data structure as upload API...');
    
    const testFileData = {
      prospect_id: testProspect.id,
      file_type: 'certificate',
      file_category: 'uploaded',
      title: 'Test Certificate',
      file_url: 'https://example.com/test-file.pdf',
      original_filename: 'test-file.pdf',
      file_size: 12345,
      mime_type: 'application/pdf',
      issued_at: new Date().toISOString()
    };

    console.log('📋 Test data:', testFileData);

    const { data: insertResult, error: insertError } = await supabase
      .from('files')
      .insert(testFileData)
      .select()
      .single();

    if (insertError) {
      console.error('❌ Insert failed:', insertError);
      console.error('❌ Error details:', {
        message: insertError.message,
        details: insertError.details,
        hint: insertError.hint,
        code: insertError.code
      });
    } else {
      console.log('✅ Insert successful:', insertResult);
      
      // Clean up the test record
      await supabase.from('files').delete().eq('id', insertResult.id);
      console.log('🧹 Test record cleaned up');
    }

    // 3. Test with different file types
    console.log('\n3️⃣ Testing with document type...');
    
    const testDocumentData = {
      prospect_id: testProspect.id,
      file_type: 'document',
      file_category: 'uploaded',
      title: 'Test Document',
      file_url: 'https://example.com/test-doc.pdf',
      original_filename: 'test-doc.pdf',
      file_size: 54321,
      mime_type: 'application/pdf',
      issued_at: new Date().toISOString()
    };

    const { data: docResult, error: docError } = await supabase
      .from('files')
      .insert(testDocumentData)
      .select()
      .single();

    if (docError) {
      console.error('❌ Document insert failed:', docError);
    } else {
      console.log('✅ Document insert successful:', docResult);
      await supabase.from('files').delete().eq('id', docResult.id);
      console.log('🧹 Document test record cleaned up');
    }

    // 4. Check table constraints
    console.log('\n4️⃣ Checking table constraints...');
    
    const { data: constraints, error: constraintsError } = await supabase
      .rpc('get_table_constraints', { table_name: 'files' })
      .catch(() => {
        // If the function doesn't exist, we'll query the information schema directly
        return supabase
          .from('information_schema.table_constraints')
          .select('*')
          .eq('table_name', 'files');
      });

    if (constraints) {
      console.log('📋 Table constraints:', constraints);
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testDatabaseInsert();
