const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function testUploadAPI() {
  console.log('🧪 Testing Upload API Components...\n');

  try {
    // 1. Test authentication
    console.log('1️⃣ Testing authentication...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    if (userError) {
      console.log('❌ Auth error (expected for service key):', userError.message);
    } else {
      console.log('✅ Auth working');
    }

    // 2. Test users table access
    console.log('\n2️⃣ Testing users table...');
    const { data: users, error: usersError } = await supabase
      .from('users')
      .select('id, role')
      .eq('role', 'prospect')
      .limit(1);

    if (usersError) {
      console.error('❌ Users table error:', usersError.message);
    } else {
      console.log('✅ Users table accessible');
      console.log('📋 Sample prospect user:', users[0]?.id || 'No prospects found');
    }

    // 3. Test prospects table access
    console.log('\n3️⃣ Testing prospects table...');
    const { data: prospects, error: prospectsError } = await supabase
      .from('prospects')
      .select('id, user_id')
      .limit(1);

    if (prospectsError) {
      console.error('❌ Prospects table error:', prospectsError.message);
    } else {
      console.log('✅ Prospects table accessible');
      console.log('📋 Sample prospect:', prospects[0]?.id || 'No prospects found');
    }

    // 4. Test files table access
    console.log('\n4️⃣ Testing files table...');
    const { data: files, error: filesError } = await supabase
      .from('files')
      .select('*')
      .limit(1);

    if (filesError) {
      console.error('❌ Files table error:', filesError.message);
    } else {
      console.log('✅ Files table accessible');
      console.log('📋 Files count:', files.length);
    }

    // 5. Test storage bucket access
    console.log('\n5️⃣ Testing storage bucket...');
    const { data: buckets, error: bucketsError } = await supabase.storage.listBuckets();

    if (bucketsError) {
      console.error('❌ Storage error:', bucketsError.message);
    } else {
      console.log('✅ Storage accessible');
      console.log('📋 Available buckets:');
      buckets.forEach(bucket => {
        console.log(`  - ${bucket.name} (${bucket.public ? 'public' : 'private'})`);
      });

      // Check if 'files' bucket exists
      const filesBucket = buckets.find(b => b.name === 'files');
      if (filesBucket) {
        console.log('✅ Files bucket exists');
      } else {
        console.log('❌ Files bucket not found');
      }
    }

    // 6. Test file upload to storage
    console.log('\n6️⃣ Testing file upload...');
    const testFile = new Blob(['test content for upload'], { type: 'text/plain' });
    const testFileName = `test-upload-${Date.now()}.txt`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('files')
      .upload(testFileName, testFile);

    if (uploadError) {
      console.error('❌ Upload error:', uploadError.message);
    } else {
      console.log('✅ Upload successful');
      console.log('📁 File path:', uploadData.path);

      // Clean up test file
      await supabase.storage.from('files').remove([testFileName]);
      console.log('🧹 Test file cleaned up');
    }

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Run the test
testUploadAPI();
