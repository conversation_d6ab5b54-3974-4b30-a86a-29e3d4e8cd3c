import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  return handleRequest(request)
}

export async function POST(request: NextRequest) {
  return handleRequest(request)
}

async function handleRequest(request: NextRequest) {
  try {
    // Create service role client to bypass all RLS
    const supabaseService = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log('🔧 Creating test data with service role...')

    // Test data
    const prospectId = 'f08d34cc-6222-4a94-8476-ada6cad4abc3'
    const jobId = '9f021ecf-8d64-47e8-9e64-d2c5a563fe5d'
    const bpoUserId = '8cb815d2-c488-467e-a62e-83075741bdc2'

    // Step 1: Create application
    console.log('🔧 Creating application...')
    const { data: application, error: appError } = await supabaseService
      .from('applications')
      .insert({
        job_id: jobId,
        prospect_id: prospectId,
        status: 'submitted',
        submitted_at: new Date().toISOString()
      })
      .select()
      .single()

    console.log('🔧 Application result:', { data: application, error: appError })

    if (appError) {
      return NextResponse.json({
        error: 'Failed to create application',
        details: appError
      }, { status: 500 })
    }

    // Step 2: Create interview
    console.log('🔧 Creating interview...')
    const placeholderDate = new Date()
    placeholderDate.setDate(placeholderDate.getDate() + 7)

    const { data: interview, error: interviewError } = await supabaseService
      .from('interviews')
      .insert({
        application_id: application.id,
        bpo_user_id: bpoUserId,
        scheduled_at: placeholderDate.toISOString(),
        duration_minutes: 60,
        location: 'virtual',
        meeting_link: 'https://meet.google.com/test-link',
        notes: 'Test interview invitation',
        status: 'pending_scheduling',
        feedback: {
          interview_type: 'video_call',
          invitation_message: 'Test invitation message'
        }
      })
      .select()
      .single()

    console.log('🔧 Interview result:', { data: interview, error: interviewError })

    if (interviewError) {
      return NextResponse.json({
        error: 'Failed to create interview',
        details: interviewError
      }, { status: 500 })
    }

    // Step 3: Update application status
    console.log('🔧 Updating application status...')
    const { error: updateError } = await supabaseService
      .from('applications')
      .update({
        status: 'interview_scheduled'
      })
      .eq('id', application.id)

    console.log('🔧 Update result:', { error: updateError })

    // Step 4: Verify data was created
    console.log('🔧 Verifying created data...')
    
    const { data: verifyApp, error: verifyAppError } = await supabaseService
      .from('applications')
      .select('*')
      .eq('id', application.id)
      .single()

    const { data: verifyInterview, error: verifyInterviewError } = await supabaseService
      .from('interviews')
      .select('*')
      .eq('id', interview.id)
      .single()

    console.log('🔧 Verification results:', {
      application: { data: verifyApp, error: verifyAppError },
      interview: { data: verifyInterview, error: verifyInterviewError }
    })

    return NextResponse.json({
      success: true,
      message: 'Test data created successfully',
      data: {
        application: verifyApp,
        interview: verifyInterview
      }
    })

  } catch (error) {
    console.error('🔧 Error creating test data:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
