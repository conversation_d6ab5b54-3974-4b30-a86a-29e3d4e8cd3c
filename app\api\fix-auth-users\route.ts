import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function POST(request: NextRequest) {
  try {
    console.log('🔥 FIXING AUTH USERS: Creating users in Supabase Auth...');
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    
    // Use service role key for admin operations
    const supabase = createClient(supabaseUrl, serviceKey);

    // Get existing users from our public.users table
    const { data: existingUsers, error: fetchError } = await supabase
      .from('users')
      .select('id, email, full_name, role');
    
    if (fetchError) {
      throw new Error(`Failed to fetch existing users: ${fetchError.message}`);
    }

    console.log(`🔥 Found ${existingUsers?.length || 0} users in public.users table`);

    const results = [];

    for (const user of existingUsers || []) {
      console.log(`🔥 Creating auth user for: ${user.email}`);
      
      try {
        // Create user in Supabase Auth with the SAME ID
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          user_id: user.id, // Use the same ID from our public.users table
          email: user.email,
          password: 'BeeMO5317', // Default password for all test users
          email_confirm: true, // Auto-confirm email
          user_metadata: {
            full_name: user.full_name,
            role: user.role
          }
        });

        if (authError) {
          console.log(`🔥 ❌ Error creating auth user for ${user.email}:`, authError.message);
          results.push({
            email: user.email,
            success: false,
            error: authError.message
          });
        } else {
          console.log(`🔥 ✅ Successfully created auth user for ${user.email}`);
          results.push({
            email: user.email,
            success: true,
            authId: authData.user?.id,
            publicId: user.id
          });
        }
      } catch (err) {
        console.log(`🔥 ❌ Exception creating auth user for ${user.email}:`, err);
        results.push({
          email: user.email,
          success: false,
          error: err instanceof Error ? err.message : 'Unknown error'
        });
      }

      // Add a small delay to avoid rate limiting
      await new Promise(resolve => setTimeout(resolve, 100));
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    console.log(`🔥 COMPLETED: ${successCount} success, ${errorCount} errors`);

    return NextResponse.json({
      success: true,
      message: 'Auth user creation completed',
      results: {
        total: existingUsers?.length || 0,
        successful: successCount,
        failed: errorCount,
        details: results
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('🔥 AUTH FIX FAILED:', error);
    return NextResponse.json({
      success: false,
      error: 'Auth fix failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
