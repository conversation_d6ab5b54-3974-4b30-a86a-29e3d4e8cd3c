'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

interface User {
  id: string;
  email: string;
  role: string;
  full_name: string;
  isPlatformAdmin: boolean;
  isOrgOwner: boolean;
  isOrgAdmin: boolean;
  organizationMemberships: any[];
}

export default function DashboardPage() {
  const router = useRouter();
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const checkAuthAndRedirect = async () => {
      try {
        console.log('[DASHBOARD] 🔍 Checking authentication...');
        const response = await fetch('/api/luna-auth-test');
        const data = await response.json();
        
        console.log('[DASHBOARD] 📊 Auth response:', data);

        if (!data.success || !data.user) {
          console.log('[DASHBOARD] ❌ Not authenticated, redirecting to login');
          router.push('/login');
          return;
        }

        const userData = data.user;
        setUser(userData);

        // Route to appropriate dashboard based on user role
        console.log('[DASHBOARD] 🔄 Routing based on role:', userData.role);
        
        if (userData.isPlatformAdmin) {
          console.log('[DASHBOARD] 🔄 Redirecting to platform admin dashboard');
          router.push('/dashboard/platform-admin');
        } else if (userData.isOrgOwner) {
          console.log('[DASHBOARD] 🔄 Redirecting to organization owner dashboard');
          router.push('/dashboard/org-owner');
        } else if (userData.isOrgAdmin) {
          console.log('[DASHBOARD] 🔄 Redirecting to organization admin dashboard');
          router.push('/dashboard/org-admin');
        } else if (userData.role === 'org_member') {
          console.log('[DASHBOARD] 🔄 Redirecting to organization member dashboard');
          router.push('/dashboard/org-member');
        } else if (userData.role === 'individual') {
          console.log('[DASHBOARD] 🔄 Redirecting to individual dashboard');
          router.push('/dashboard/individual');
        } else {
          console.log('[DASHBOARD] ❓ Unknown role, staying on main dashboard');
        }

      } catch (error) {
        console.error('[DASHBOARD] ❌ Error checking auth:', error);
        router.push('/login');
      } finally {
        setLoading(false);
      }
    };

    checkAuthAndRedirect();
  }, [router]);

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-indigo-600 mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white shadow rounded-lg p-6">
          <h1 className="text-2xl font-bold text-gray-900 mb-6">
            Luna Platform Dashboard
          </h1>
          
          {user && (
            <div className="space-y-4">
              <div>
                <h2 className="text-lg font-medium text-gray-900">Welcome, {user.full_name}!</h2>
                <p className="text-sm text-gray-600">Email: {user.email}</p>
                <p className="text-sm text-gray-600">Role: {user.role}</p>
              </div>
              
              <div className="bg-blue-50 p-4 rounded-md">
                <p className="text-sm text-blue-700">
                  You should be automatically redirected to your role-specific dashboard.
                  If not, please check the browser console for routing information.
                </p>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
