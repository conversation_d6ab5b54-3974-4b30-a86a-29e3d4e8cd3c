import { NextRequest } from 'next/server';
import { 
  createApiSuccessResponse, 
  handleApiError,
  createApiErrorResponse 
} from '@/lib/api-error-handler';
import { createError, ErrorType } from '@/lib/utils';

/**
 * Test API endpoint for demonstrating different error types
 * GET /api/test-errors?type=<error_type>
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const errorType = searchParams.get('type');

    // Simulate different error scenarios
    switch (errorType) {
      case 'auth':
        // Authentication error
        const authError = createError(
          ErrorType.AUTHENTICATION,
          'User authentication failed',
          'Please log in to continue',
          { context: 'test-errors', requestedAction: 'access protected resource' }
        );
        return createApiErrorResponse(authError, 401);

      case 'authorization':
        // Authorization error
        const authzError = createError(
          ErrorType.AUTHORIZATION,
          'User lacks required permissions',
          'You don\'t have permission to perform this action',
          { context: 'test-errors', requiredRole: 'admin' }
        );
        return createApiErrorResponse(authzError, 403);

      case 'validation':
        // Validation error
        const validationError = createError(
          ErrorType.VALIDATION,
          'Invalid request parameters',
          'Please check your input and try again',
          { 
            context: 'test-errors', 
            missingFields: ['email', 'password'],
            providedFields: ['username']
          }
        );
        return createApiErrorResponse(validationError, 400);

      case 'database':
        // Database error (simulate Supabase error)
        const dbError = {
          code: '23505',
          message: 'duplicate key value violates unique constraint "users_email_key"',
          details: 'Key (email)=(<EMAIL>) already exists.',
          hint: 'Use a different email address'
        };
        return handleApiError(dbError, 'test-errors database simulation');

      case 'network':
        // Network error
        const networkError = createError(
          ErrorType.NETWORK,
          'Network connection failed',
          'Unable to connect to external service. Please try again later.',
          { context: 'test-errors', service: 'external-api' }
        );
        return createApiErrorResponse(networkError, 502);

      case 'rate-limit':
        // Rate limit error
        const rateLimitError = createError(
          ErrorType.RATE_LIMIT,
          'Rate limit exceeded: 100 requests per hour',
          'Too many requests. Please wait before trying again.',
          { 
            context: 'test-errors', 
            limit: 100, 
            window: '1 hour',
            retryAfter: 3600
          }
        );
        return createApiErrorResponse(rateLimitError, 429);

      case 'file-upload':
        // File upload error
        const fileError = createError(
          ErrorType.FILE_UPLOAD,
          'File size exceeds maximum limit',
          'Please choose a file smaller than 10MB',
          { 
            context: 'test-errors', 
            maxSize: '10MB',
            actualSize: '15MB',
            fileType: 'image/jpeg'
          }
        );
        return createApiErrorResponse(fileError, 400);

      case 'not-found':
        // Not found error
        const notFoundError = createError(
          ErrorType.NOT_FOUND,
          'Resource not found',
          'The requested item could not be found',
          { context: 'test-errors', resourceType: 'user', resourceId: '123' }
        );
        return createApiErrorResponse(notFoundError, 404);

      case 'server-error':
        // Internal server error
        const serverError = createError(
          ErrorType.SERVER_ERROR,
          'Internal server error occurred',
          'Something went wrong on our end. Please try again later.',
          { 
            context: 'test-errors', 
            errorCode: 'INTERNAL_ERROR_001',
            timestamp: new Date().toISOString()
          }
        );
        return createApiErrorResponse(serverError, 500);

      case 'throw-error':
        // Throw an actual error to test error handling
        throw new Error('This is a thrown error for testing purposes');

      case 'success':
        // Success response
        return createApiSuccessResponse({
          message: 'Test completed successfully',
          errorType: 'none',
          timestamp: new Date().toISOString(),
          testData: {
            randomNumber: Math.floor(Math.random() * 1000),
            status: 'healthy'
          }
        });

      default:
        // Unknown error type
        const unknownError = createError(
          ErrorType.VALIDATION,
          `Unknown error type: ${errorType}`,
          'Please specify a valid error type',
          { 
            context: 'test-errors', 
            providedType: errorType,
            validTypes: [
              'auth', 'authorization', 'validation', 'database', 
              'network', 'rate-limit', 'file-upload', 'not-found', 
              'server-error', 'throw-error', 'success'
            ]
          }
        );
        return createApiErrorResponse(unknownError, 400);
    }

  } catch (error) {
    // This will test the automatic error handling
    return handleApiError(error, 'test-errors catch block');
  }
}

/**
 * Test POST endpoint for form submission errors
 */
export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    
    // Simulate validation
    if (!body.email) {
      const validationError = createError(
        ErrorType.VALIDATION,
        'Email is required',
        'Please provide an email address',
        { context: 'test-errors POST', field: 'email' }
      );
      return createApiErrorResponse(validationError, 400);
    }

    if (!body.email.includes('@')) {
      const validationError = createError(
        ErrorType.VALIDATION,
        'Invalid email format',
        'Please provide a valid email address',
        { context: 'test-errors POST', field: 'email', value: body.email }
      );
      return createApiErrorResponse(validationError, 400);
    }

    // Simulate success
    return createApiSuccessResponse({
      message: 'Form submitted successfully',
      submittedData: body,
      processedAt: new Date().toISOString()
    });

  } catch (error) {
    return handleApiError(error, 'test-errors POST');
  }
}

/**
 * Test PUT endpoint for update errors
 */
export async function PUT(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      const validationError = createError(
        ErrorType.VALIDATION,
        'ID parameter is required',
        'Please provide a valid ID',
        { context: 'test-errors PUT', parameter: 'id' }
      );
      return createApiErrorResponse(validationError, 400);
    }

    // Simulate not found
    if (id === '404') {
      const notFoundError = createError(
        ErrorType.NOT_FOUND,
        `Resource with ID ${id} not found`,
        'The item you\'re trying to update doesn\'t exist',
        { context: 'test-errors PUT', resourceId: id }
      );
      return createApiErrorResponse(notFoundError, 404);
    }

    // Simulate success
    return createApiSuccessResponse({
      message: 'Resource updated successfully',
      resourceId: id,
      updatedAt: new Date().toISOString()
    });

  } catch (error) {
    return handleApiError(error, 'test-errors PUT');
  }
}

/**
 * Test DELETE endpoint for deletion errors
 */
export async function DELETE(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    if (!id) {
      const validationError = createError(
        ErrorType.VALIDATION,
        'ID parameter is required',
        'Please provide a valid ID',
        { context: 'test-errors DELETE', parameter: 'id' }
      );
      return createApiErrorResponse(validationError, 400);
    }

    // Simulate constraint violation
    if (id === 'constraint') {
      const constraintError = createError(
        ErrorType.VALIDATION,
        'Cannot delete resource due to foreign key constraint',
        'This item cannot be deleted because it\'s being used elsewhere',
        { 
          context: 'test-errors DELETE', 
          resourceId: id,
          constraint: 'foreign_key_constraint'
        }
      );
      return createApiErrorResponse(constraintError, 400);
    }

    // Simulate success
    return createApiSuccessResponse({
      message: 'Resource deleted successfully',
      deletedId: id,
      deletedAt: new Date().toISOString()
    });

  } catch (error) {
    return handleApiError(error, 'test-errors DELETE');
  }
}
