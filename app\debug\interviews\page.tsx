'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'

export default function DebugInterviews() {
  const [debugData, setDebugData] = useState(null)
  const [loading, setLoading] = useState(false)
  const [creating, setCreating] = useState(false)
  const [creatingTable, setCreatingTable] = useState(false)

  const fetchDebugData = async () => {
    try {
      setLoading(true)
      const response = await fetch('/api/debug/interviews')
      const data = await response.json()
      setDebugData(data)
    } catch (error) {
      console.error('Error fetching debug data:', error)
    } finally {
      setLoading(false)
    }
  }

  const createTestInterview = async () => {
    try {
      setCreating(true)
      const response = await fetch('/api/debug/create-test-interview', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        alert('Test interview created successfully!')
        fetchDebugData() // Refresh the data
      } else {
        alert('Error creating test interview: ' + data.error)
      }
    } catch (error) {
      console.error('Error creating test interview:', error)
      alert('Error creating test interview')
    } finally {
      setCreating(false)
    }
  }

  const createScheduleTable = async () => {
    try {
      setCreatingTable(true)
      const response = await fetch('/api/debug/create-schedule-table', {
        method: 'POST'
      })
      const data = await response.json()

      if (data.success) {
        alert('Schedule table created successfully!')
        fetchDebugData() // Refresh the data
      } else {
        alert('Error creating schedule table: ' + data.error)
      }
    } catch (error) {
      console.error('Error creating schedule table:', error)
      alert('Error creating schedule table')
    } finally {
      setCreatingTable(false)
    }
  }

  useEffect(() => {
    fetchDebugData()
  }, [])

  if (loading) {
    return (
      <div className="p-8">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2">Loading debug data...</p>
        </div>
      </div>
    )
  }

  return (
    <div className="p-8 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-2xl font-bold">Database Debug - Interviews</h1>
        <div className="space-x-2">
          <Button onClick={createTestInterview} disabled={creating}>
            {creating ? 'Creating...' : 'Create Test Interview'}
          </Button>
          <Button onClick={createScheduleTable} disabled={creatingTable} variant="outline">
            {creatingTable ? 'Creating...' : 'Create Schedule Table'}
          </Button>
          <Button onClick={fetchDebugData} disabled={loading}>
            Refresh
          </Button>
        </div>
      </div>

      {debugData && (
        <div className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>User Info</CardTitle>
            </CardHeader>
            <CardContent>
              <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto">
                {JSON.stringify({ user: debugData.debug?.user }, null, 2)}
              </pre>
            </CardContent>
          </Card>

          {debugData.debug?.errors?.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="text-red-600">Errors</CardTitle>
              </CardHeader>
              <CardContent>
                <pre className="text-sm bg-red-50 p-4 rounded overflow-auto">
                  {JSON.stringify(debugData.debug.errors, null, 2)}
                </pre>
              </CardContent>
            </Card>
          )}

          {Object.entries(debugData.debug?.tables || {}).map(([tableName, tableData]) => (
            <Card key={tableName}>
              <CardHeader>
                <CardTitle>
                  {tableName} ({tableData.count} records)
                  {tableData.error && <span className="text-red-500 ml-2">ERROR</span>}
                </CardTitle>
              </CardHeader>
              <CardContent>
                {tableData.error && (
                  <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded">
                    <strong>Error:</strong> {JSON.stringify(tableData.error)}
                  </div>
                )}
                <pre className="text-sm bg-gray-100 p-4 rounded overflow-auto max-h-96">
                  {JSON.stringify(tableData.sample, null, 2)}
                </pre>
              </CardContent>
            </Card>
          ))}
        </div>
      )}
    </div>
  )
}
