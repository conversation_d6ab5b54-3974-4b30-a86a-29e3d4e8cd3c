'use client'

import { useState, useEffect } from 'react'
import { useR<PERSON><PERSON> } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { ArrowLeft, Eye, Calendar, CheckCircle, XCircle, Clock, Search, Filter, Users } from 'lucide-react'
import Link from 'next/link'

interface Application {
  id: string
  status: string
  submitted_at: string
  reviewed_at?: string
  prospects: {
    id: string
    user_id: string
    users: {
      full_name: string
      email: string
    }
  }
}

interface JobPosting {
  id: string
  title: string
  status: string
  bpos: {
    name: string
  }
}

export default function VacancyApplicationsPage({ params }: { params: Promise<{ id: string }> }) {
  const router = useRouter()
  const { toast } = useToast()
  const [loading, setLoading] = useState(true)
  const [applications, setApplications] = useState<Application[]>([])
  const [jobPosting, setJobPosting] = useState<JobPosting | null>(null)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [vacancyId, setVacancyId] = useState<string>('')

  const supabase = createClientComponentClient()

  useEffect(() => {
    const resolveParams = async () => {
      const resolvedParams = await params
      setVacancyId(resolvedParams.id)
    }
    resolveParams()
  }, [params])

  useEffect(() => {
    if (vacancyId) {
      fetchApplications()
    }
  }, [vacancyId])

  const fetchApplications = async () => {
    try {
      setLoading(true)

      // Get current user's BPO ID
      const { data: { session } } = await supabase.auth.getSession()
      if (!session) {
        router.push('/login')
        return
      }

      // Get BPO ID for current user
      const { data: bpoData } = await supabase
        .from('bpo_teams')
        .select('bpo_id')
        .eq('user_id', session.user.id)
        .single()

      if (!bpoData) {
        toast({
          title: "Error",
          description: "BPO team membership not found",
          variant: "destructive"
        })
        return
      }

      // Fetch job posting details
      const { data: jobData, error: jobError } = await supabase
        .from('job_postings')
        .select(`
          id,
          title,
          status,
          bpos!inner(
            name
          )
        `)
        .eq('id', vacancyId)
        .eq('bpo_id', bpoData.bpo_id)
        .single()

      if (jobError || !jobData) {
        toast({
          title: "Error",
          description: "Job posting not found or access denied",
          variant: "destructive"
        })
        router.push('/bpo/vacancies')
        return
      }

      setJobPosting(jobData)

      // Fetch applications for this specific job posting
      const { data: applicationsData, error } = await supabase
        .from('applications')
        .select(`
          id,
          status,
          submitted_at,
          reviewed_at,
          prospects!inner(
            id,
            user_id,
            users!inner(
              full_name,
              email
            )
          )
        `)
        .eq('job_id', vacancyId)
        .order('submitted_at', { ascending: false })

      if (error) {
        console.error('Error fetching applications:', error)
        toast({
          title: "Error",
          description: "Failed to fetch applications",
          variant: "destructive"
        })
        return
      }

      setApplications(applicationsData || [])

    } catch (error) {
      console.error('Error:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    } finally {
      setLoading(false)
    }
  }

  const getStatusBadge = (status: string) => {
    const statusConfig = {
      submitted: { label: 'Submitted', variant: 'secondary' as const, icon: Clock },
      reviewing: { label: 'Reviewing', variant: 'default' as const, icon: Eye },
      accepted: { label: 'Accepted', variant: 'default' as const, icon: CheckCircle },
      rejected: { label: 'Rejected', variant: 'destructive' as const, icon: XCircle },
      interview_scheduled: { label: 'Interview Scheduled', variant: 'default' as const, icon: Calendar }
    }

    const config = statusConfig[status as keyof typeof statusConfig] || statusConfig.submitted
    const Icon = config.icon

    return (
      <Badge variant={config.variant} className="flex items-center gap-1">
        <Icon className="h-3 w-3" />
        {config.label}
      </Badge>
    )
  }

  const filteredApplications = applications.filter(app => {
    const matchesSearch = app.prospects.users.full_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         app.prospects.users.email.toLowerCase().includes(searchQuery.toLowerCase())
    
    const matchesStatus = statusFilter === 'all' || app.status === statusFilter
    
    return matchesSearch && matchesStatus
  })

  const handleViewProfile = (prospectId: string) => {
    router.push(`/prospect/public/${prospectId}`)
  }

  const handleUpdateStatus = async (applicationId: string, newStatus: string) => {
    try {
      const { error } = await supabase
        .from('applications')
        .update({ 
          status: newStatus,
          reviewed_at: new Date().toISOString()
        })
        .eq('id', applicationId)

      if (error) {
        toast({
          title: "Error",
          description: "Failed to update application status",
          variant: "destructive"
        })
        return
      }

      toast({
        title: "Success",
        description: "Application status updated successfully"
      })

      fetchApplications() // Refresh the list
    } catch (error) {
      console.error('Error updating status:', error)
      toast({
        title: "Error",
        description: "An unexpected error occurred",
        variant: "destructive"
      })
    }
  }

  const getStatusStats = () => {
    const total = applications.length
    const submitted = applications.filter(app => app.status === 'submitted').length
    const reviewing = applications.filter(app => app.status === 'reviewing').length
    const accepted = applications.filter(app => app.status === 'accepted').length
    const rejected = applications.filter(app => app.status === 'rejected').length

    return { total, submitted, reviewing, accepted, rejected }
  }

  const stats = getStatusStats()

  if (loading) {
    return (
      <div className="p-4 sm:p-6 lg:p-8">
        <div className="animate-pulse space-y-4">
          <div className="h-8 bg-gray-200 rounded w-1/4"></div>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
            {[...Array(5)].map((_, i) => (
              <div key={i} className="h-24 bg-gray-200 rounded"></div>
            ))}
          </div>
          <div className="h-96 bg-gray-200 rounded"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="p-4 sm:p-6 lg:p-8 space-y-8">
      {/* Header */}
      <div className="flex items-center gap-4 mb-6">
        <Link href="/bpo/vacancies">
          <Button variant="ghost" size="sm">
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Vacancies
          </Button>
        </Link>
      </div>

      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Applications</h1>
          <p className="text-gray-500 dark:text-gray-400 mt-1">
            {jobPosting ? `Applications for "${jobPosting.title}"` : 'Loading...'}
          </p>
        </div>
      </div>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-5 gap-4">
        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-blue-500 rounded-md p-3">
                <Users className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Total Applications
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {stats.total}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-yellow-500 rounded-md p-3">
                <Clock className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Submitted
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {stats.submitted}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-orange-500 rounded-md p-3">
                <Eye className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Reviewing
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {stats.reviewing}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-green-500 rounded-md p-3">
                <CheckCircle className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Accepted
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {stats.accepted}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="pt-6">
            <div className="flex items-center">
              <div className="flex-shrink-0 bg-red-500 rounded-md p-3">
                <XCircle className="h-5 w-5 text-white" />
              </div>
              <div className="ml-5 w-0 flex-1">
                <dl>
                  <dt className="text-sm font-medium text-gray-500 dark:text-gray-400 truncate">
                    Rejected
                  </dt>
                  <dd className="flex items-baseline">
                    <div className="text-2xl font-semibold text-gray-900 dark:text-white">
                      {stats.rejected}
                    </div>
                  </dd>
                </dl>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Filters */}
      <div className="flex flex-col sm:flex-row gap-4">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
          <Input
            placeholder="Search by candidate name or email..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-10"
          />
        </div>
        <Select value={statusFilter} onValueChange={setStatusFilter}>
          <SelectTrigger className="w-full sm:w-48">
            <Filter className="h-4 w-4 mr-2" />
            <SelectValue placeholder="Filter by status" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All Statuses</SelectItem>
            <SelectItem value="submitted">Submitted</SelectItem>
            <SelectItem value="reviewing">Reviewing</SelectItem>
            <SelectItem value="accepted">Accepted</SelectItem>
            <SelectItem value="rejected">Rejected</SelectItem>
            <SelectItem value="interview_scheduled">Interview Scheduled</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {/* Applications Table */}
      <Card>
        <CardContent className="p-0">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Candidate</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Applied</TableHead>
                <TableHead>Reviewed</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredApplications.map((application) => (
                <TableRow key={application.id}>
                  <TableCell>
                    <div className="flex items-center gap-3">
                      <Avatar>
                        <AvatarImage src={`/avatars/${application.prospects.id}.png`} />
                        <AvatarFallback>
                          {application.prospects.users.full_name.split(' ').map(n => n[0]).join('')}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <p className="font-medium">{application.prospects.users.full_name}</p>
                        <p className="text-sm text-gray-500">{application.prospects.users.email}</p>
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    {getStatusBadge(application.status)}
                  </TableCell>
                  <TableCell>
                    {new Date(application.submitted_at).toLocaleDateString()}
                  </TableCell>
                  <TableCell>
                    {application.reviewed_at 
                      ? new Date(application.reviewed_at).toLocaleDateString()
                      : '-'
                    }
                  </TableCell>
                  <TableCell className="text-right">
                    <div className="flex items-center justify-end gap-2">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewProfile(application.prospects.id)}
                      >
                        <Eye className="h-3.5 w-3.5 mr-1" />
                        View Profile
                      </Button>
                      {application.status === 'submitted' && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleUpdateStatus(application.id, 'reviewing')}
                        >
                          Start Review
                        </Button>
                      )}
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
          
          {filteredApplications.length === 0 && (
            <div className="text-center py-12">
              <p className="text-gray-500 dark:text-gray-400">No applications found</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
