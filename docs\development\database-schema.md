# 🗄️ Database Schema Documentation

Complete documentation of the BPO Training Platform database schema, including tables, relationships, indexes, and security policies.

## 📊 Schema Overview

The database uses **PostgreSQL** with **Row Level Security (RLS)** for fine-grained access control. The schema is designed for scalability, performance, and security.

### Database Statistics
- **Tables**: 15 core tables
- **Views**: 8 materialized views
- **Functions**: 12 security functions
- **Policies**: 45+ RLS policies
- **Indexes**: 35+ optimized indexes

## 🏗️ Core Tables

### Users & Authentication

#### `users` - Core user information
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  email TEXT UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  role user_role NOT NULL DEFAULT 'prospect',
  status user_status NOT NULL DEFAULT 'pending_activation',
  avatar_url TEXT,
  last_login TIMESTAMP WITH TIME ZONE,
  email_verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);
CREATE INDEX idx_users_created_at ON users(created_at);

-- RLS Policies
ALTER TABLE users ENABLE ROW LEVEL SECURITY;

CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "Platform admins can view all users" ON users
  FOR SELECT USING (is_platform_admin());
```

#### `prospects` - Job seeker profiles
```sql
CREATE TABLE prospects (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID UNIQUE NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  contact_info JSONB DEFAULT '{}',
  education JSONB DEFAULT '[]',
  experience JSONB DEFAULT '[]',
  skills JSONB DEFAULT '[]',
  intro_video_url TEXT,
  resume_url TEXT,
  profile_image TEXT,
  profile_visibility BOOLEAN DEFAULT false,
  training_status training_status DEFAULT 'not_started',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_prospects_user_id ON prospects(user_id);
CREATE INDEX idx_prospects_training_status ON prospects(training_status);
CREATE INDEX idx_prospects_profile_visibility ON prospects(profile_visibility);

-- Full-text search index
CREATE INDEX idx_prospects_search ON prospects 
  USING gin(to_tsvector('english', 
    COALESCE((contact_info->>'skills'), '') || ' ' ||
    COALESCE((education->>0->>'field_of_study'), '') || ' ' ||
    COALESCE((experience->>0->>'position'), '')
  ));
```

### BPO Companies

#### `bpos` - BPO company information
```sql
CREATE TABLE bpos (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  name TEXT NOT NULL,
  description TEXT,
  logo_url TEXT,
  industry TEXT,
  size_range TEXT,
  founded_year INTEGER,
  website_url TEXT,
  location JSONB DEFAULT '{}',
  contact_email TEXT,
  phone TEXT,
  created_by UUID NOT NULL REFERENCES users(id),
  reviews JSONB DEFAULT '[]',
  workplace_stats JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_bpos_name ON bpos(name);
CREATE INDEX idx_bpos_industry ON bpos(industry);
CREATE INDEX idx_bpos_created_by ON bpos(created_by);
CREATE INDEX idx_bpos_created_at ON bpos(created_at);

-- Full-text search
CREATE INDEX idx_bpos_search ON bpos 
  USING gin(to_tsvector('english', name || ' ' || COALESCE(description, '')));
```

#### `bpo_team_members` - Team membership
```sql
CREATE TABLE bpo_team_members (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bpo_id UUID NOT NULL REFERENCES bpos(id) ON DELETE CASCADE,
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  role bpo_role NOT NULL DEFAULT 'member',
  permissions JSONB DEFAULT '{}',
  invited_at TIMESTAMP WITH TIME ZONE,
  invited_by UUID REFERENCES users(id),
  accepted_at TIMESTAMP WITH TIME ZONE,
  is_placeholder BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(bpo_id, user_id)
);

-- Indexes
CREATE INDEX idx_bpo_team_members_bpo_id ON bpo_team_members(bpo_id);
CREATE INDEX idx_bpo_team_members_user_id ON bpo_team_members(user_id);
CREATE INDEX idx_bpo_team_members_role ON bpo_team_members(role);
```

### Training System

#### `training_modules` - Training content
```sql
CREATE TABLE training_modules (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  title TEXT NOT NULL,
  description TEXT,
  cover_image_url TEXT,
  duration_minutes INTEGER,
  required_order INTEGER,
  status module_status DEFAULT 'draft',
  requires_assessment BOOLEAN DEFAULT false,
  prerequisites JSONB DEFAULT '[]',
  learning_objectives JSONB DEFAULT '[]',
  tags JSONB DEFAULT '[]',
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_training_modules_status ON training_modules(status);
CREATE INDEX idx_training_modules_required_order ON training_modules(required_order);
CREATE INDEX idx_training_modules_created_by ON training_modules(created_by);
CREATE INDEX idx_training_modules_created_at ON training_modules(created_at);

-- Full-text search
CREATE INDEX idx_training_modules_search ON training_modules 
  USING gin(to_tsvector('english', title || ' ' || COALESCE(description, '')));
```

#### `lessons` - Individual lessons
```sql
CREATE TABLE lessons (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  module_id UUID NOT NULL REFERENCES training_modules(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  order_index INTEGER NOT NULL,
  duration_minutes INTEGER,
  lesson_type lesson_type DEFAULT 'video',
  video_url TEXT,
  media_url TEXT,
  content JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(module_id, order_index)
);

-- Indexes
CREATE INDEX idx_lessons_module_id ON lessons(module_id);
CREATE INDEX idx_lessons_order_index ON lessons(order_index);
CREATE INDEX idx_lessons_lesson_type ON lessons(lesson_type);
```

#### `activities` - Learning activities
```sql
CREATE TABLE activities (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  lesson_id UUID NOT NULL REFERENCES lessons(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  type activity_type NOT NULL,
  content JSONB NOT NULL DEFAULT '{}',
  content_url TEXT,
  passing_criteria JSONB DEFAULT '{}',
  order_index INTEGER NOT NULL,
  duration_minutes INTEGER,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(lesson_id, order_index)
);

-- Indexes
CREATE INDEX idx_activities_lesson_id ON activities(lesson_id);
CREATE INDEX idx_activities_type ON activities(type);
CREATE INDEX idx_activities_order_index ON activities(order_index);
```

#### `progress` - Learning progress tracking
```sql
CREATE TABLE progress (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  prospect_id UUID NOT NULL REFERENCES prospects(id) ON DELETE CASCADE,
  activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
  status progress_status NOT NULL DEFAULT 'not_started',
  score INTEGER CHECK (score >= 0 AND score <= 100),
  attempts INTEGER DEFAULT 0,
  completed_at TIMESTAMP WITH TIME ZONE,
  time_spent_seconds INTEGER DEFAULT 0,
  is_module_assessment BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(prospect_id, activity_id)
);

-- Indexes
CREATE INDEX idx_progress_prospect_id ON progress(prospect_id);
CREATE INDEX idx_progress_activity_id ON progress(activity_id);
CREATE INDEX idx_progress_status ON progress(status);
CREATE INDEX idx_progress_completed_at ON progress(completed_at);
CREATE INDEX idx_progress_is_module_assessment ON progress(is_module_assessment);
```

### Job Board

#### `job_postings` - Job listings
```sql
CREATE TABLE job_postings (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  bpo_id UUID NOT NULL REFERENCES bpos(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT NOT NULL,
  requirements JSONB DEFAULT '[]',
  responsibilities JSONB DEFAULT '[]',
  salary_range JSONB DEFAULT '{}',
  employment_type employment_type NOT NULL,
  location_type location_type NOT NULL,
  experience_level experience_level NOT NULL,
  status posting_status DEFAULT 'draft',
  application_deadline TIMESTAMP WITH TIME ZONE,
  required_training_modules JSONB DEFAULT '[]',
  created_by UUID NOT NULL REFERENCES users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes
CREATE INDEX idx_job_postings_bpo_id ON job_postings(bpo_id);
CREATE INDEX idx_job_postings_status ON job_postings(status);
CREATE INDEX idx_job_postings_employment_type ON job_postings(employment_type);
CREATE INDEX idx_job_postings_location_type ON job_postings(location_type);
CREATE INDEX idx_job_postings_experience_level ON job_postings(experience_level);
CREATE INDEX idx_job_postings_created_at ON job_postings(created_at);
CREATE INDEX idx_job_postings_deadline ON job_postings(application_deadline);

-- Full-text search
CREATE INDEX idx_job_postings_search ON job_postings 
  USING gin(to_tsvector('english', title || ' ' || description));
```

#### `applications` - Job applications
```sql
CREATE TABLE applications (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  job_id UUID NOT NULL REFERENCES job_postings(id) ON DELETE CASCADE,
  prospect_id UUID NOT NULL REFERENCES prospects(id) ON DELETE CASCADE,
  status application_status DEFAULT 'submitted',
  cover_letter TEXT,
  additional_notes TEXT,
  resume_url TEXT,
  portfolio_url TEXT,
  expected_salary_min INTEGER,
  expected_salary_max INTEGER,
  availability_start_date DATE,
  is_remote_preferred BOOLEAN DEFAULT false,
  submitted_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  reviewed_at TIMESTAMP WITH TIME ZONE,
  reviewed_by UUID REFERENCES users(id),
  rejection_reason TEXT,
  interview_scheduled_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  
  UNIQUE(job_id, prospect_id)
);

-- Indexes
CREATE INDEX idx_applications_job_id ON applications(job_id);
CREATE INDEX idx_applications_prospect_id ON applications(prospect_id);
CREATE INDEX idx_applications_status ON applications(status);
CREATE INDEX idx_applications_submitted_at ON applications(submitted_at);
CREATE INDEX idx_applications_reviewed_by ON applications(reviewed_by);
```

## 🔐 Security Functions

### Core Security Functions

```sql
-- Check if user is platform admin
CREATE OR REPLACE FUNCTION is_platform_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Check if user is BPO admin for specific BPO
CREATE OR REPLACE FUNCTION is_bpo_admin(bpo_uuid UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM bpo_team_members btm
    JOIN users u ON u.id = btm.user_id
    WHERE btm.user_id = auth.uid()
    AND btm.bpo_id = bpo_uuid
    AND btm.role = 'admin'
    AND u.status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Get user's BPO memberships
CREATE OR REPLACE FUNCTION get_user_bpo_memberships()
RETURNS TABLE(bpo_id UUID, role bpo_role) AS $$
BEGIN
  RETURN QUERY
  SELECT btm.bpo_id, btm.role
  FROM bpo_team_members btm
  JOIN users u ON u.id = btm.user_id
  WHERE btm.user_id = auth.uid()
  AND u.status = 'active'
  AND btm.is_placeholder = false;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

## 📈 Performance Optimizations

### Materialized Views

```sql
-- Training progress summary
CREATE MATERIALIZED VIEW training_progress_summary AS
SELECT 
  p.id as prospect_id,
  tm.id as module_id,
  tm.title as module_title,
  COUNT(a.id) as total_activities,
  COUNT(CASE WHEN pr.status = 'completed' THEN 1 END) as completed_activities,
  ROUND(
    (COUNT(CASE WHEN pr.status = 'completed' THEN 1 END)::FLOAT / 
     NULLIF(COUNT(a.id), 0)) * 100, 2
  ) as completion_percentage,
  MAX(pr.completed_at) as last_activity_date
FROM prospects p
CROSS JOIN training_modules tm
LEFT JOIN lessons l ON l.module_id = tm.id
LEFT JOIN activities a ON a.lesson_id = l.id
LEFT JOIN progress pr ON pr.activity_id = a.id AND pr.prospect_id = p.id
WHERE tm.status = 'published'
GROUP BY p.id, tm.id, tm.title;

-- Refresh function
CREATE OR REPLACE FUNCTION refresh_training_progress_summary()
RETURNS void AS $$
BEGIN
  REFRESH MATERIALIZED VIEW CONCURRENTLY training_progress_summary;
END;
$$ LANGUAGE plpgsql;
```

### Database Triggers

```sql
-- Update timestamps automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Apply to all tables with updated_at
CREATE TRIGGER update_users_updated_at 
  BEFORE UPDATE ON users 
  FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Progress tracking trigger
CREATE OR REPLACE FUNCTION update_training_progress()
RETURNS TRIGGER AS $$
BEGIN
  -- Refresh materialized view when progress changes
  PERFORM refresh_training_progress_summary();
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_update_training_progress
  AFTER INSERT OR UPDATE OR DELETE ON progress
  FOR EACH STATEMENT EXECUTE FUNCTION update_training_progress();
```

## 🔍 Query Examples

### Common Queries

```sql
-- Get user's training progress
SELECT 
  tps.module_title,
  tps.completion_percentage,
  tps.completed_activities,
  tps.total_activities,
  tps.last_activity_date
FROM training_progress_summary tps
WHERE tps.prospect_id = $1
ORDER BY tps.completion_percentage DESC, tps.module_title;

-- Search job postings
SELECT 
  jp.*,
  b.name as bpo_name,
  b.logo_url as bpo_logo
FROM job_postings jp
JOIN bpos b ON b.id = jp.bpo_id
WHERE jp.status = 'published'
AND jp.application_deadline > NOW()
AND to_tsvector('english', jp.title || ' ' || jp.description) 
    @@ plainto_tsquery('english', $1)
ORDER BY jp.created_at DESC
LIMIT 20;

-- Get BPO analytics
SELECT 
  COUNT(DISTINCT jp.id) as total_jobs,
  COUNT(DISTINCT a.id) as total_applications,
  COUNT(DISTINCT CASE WHEN a.status = 'accepted' THEN a.id END) as hired_candidates,
  AVG(EXTRACT(EPOCH FROM (a.reviewed_at - a.submitted_at))/3600) as avg_review_time_hours
FROM bpos b
LEFT JOIN job_postings jp ON jp.bpo_id = b.id
LEFT JOIN applications a ON a.job_id = jp.id
WHERE b.id = $1
AND jp.created_at >= NOW() - INTERVAL '30 days';
```

---

**Next**: Check out the [API Reference](api-reference.md) for endpoint documentation.
