import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';

export async function POST(request: NextRequest) {
  try {
    console.log('[CREATE-AUTH] 🚀 Starting auth user creation...');
    
    const supabase = createAdminClient();
    console.log('[CREATE-AUTH] ✅ Admin client created');

    // Define our test users
    const testUsers = [
      {
        id: '11111111-1111-1111-1111-111111111111',
        email: '<EMAIL>',
        password: '<PERSON><PERSON>5317',
        full_name: 'Luna Platform Admin',
        role: 'platform_admin'
      },
      {
        id: '00000000-0000-0000-0000-000000000002',
        email: '<EMAIL>',
        password: '<PERSON><PERSON>5317',
        full_name: '<PERSON>',
        role: 'individual'
      },
      {
        id: '00000000-0000-0000-0000-000000000003',
        email: '<EMAIL>',
        password: '<PERSON><PERSON>5317',
        full_name: '<PERSON>',
        role: 'individual'
      },
      {
        id: '00000000-0000-0000-0000-000000000004',
        email: '<EMAIL>',
        password: '<PERSON><PERSON>5317',
        full_name: '<PERSON>',
        role: 'individual'
      },
      {
        id: '00000000-0000-0000-0000-000000000005',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'David Kim',
        role: 'org_owner'
      },
      {
        id: '00000000-0000-0000-0000-000000000006',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'Dr. Lisa Wang',
        role: 'org_owner'
      },
      {
        id: '00000000-0000-0000-0000-000000000007',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'Jennifer Martinez',
        role: 'org_admin'
      },
      {
        id: '00000000-0000-0000-0000-000000000008',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'Robert Thompson',
        role: 'org_admin'
      },
      {
        id: '00000000-0000-0000-0000-000000000009',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'John Smith',
        role: 'org_member'
      },
      {
        id: '00000000-0000-0000-0000-000000000010',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'Emma Davis',
        role: 'org_member'
      },
      {
        id: '00000000-0000-0000-0000-000000000011',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'Carlos Gonzalez',
        role: 'org_member'
      },
      {
        id: '00000000-0000-0000-0000-000000000012',
        email: '<EMAIL>',
        password: 'BeeMO5317',
        full_name: 'Maria Garcia',
        role: 'individual'
      }
    ];

    const results = [];

    for (const user of testUsers) {
      console.log(`[CREATE-AUTH] 🔍 Creating auth user: ${user.email}`);
      
      try {
        // Create user in Supabase Auth
        const { data: authData, error: authError } = await supabase.auth.admin.createUser({
          user_id: user.id,
          email: user.email,
          password: user.password,
          email_confirm: true, // Auto-confirm email
          user_metadata: {
            full_name: user.full_name,
            role: user.role
          }
        });

        if (authError) {
          console.log(`[CREATE-AUTH] ❌ Error creating ${user.email}:`, authError.message);
          results.push({
            email: user.email,
            success: false,
            error: authError.message
          });
        } else {
          console.log(`[CREATE-AUTH] ✅ Successfully created ${user.email}`);
          results.push({
            email: user.email,
            success: true,
            id: authData.user?.id
          });
        }
      } catch (err) {
        console.log(`[CREATE-AUTH] ❌ Unexpected error creating ${user.email}:`, err);
        results.push({
          email: user.email,
          success: false,
          error: err instanceof Error ? err.message : 'Unknown error'
        });
      }
    }

    const successCount = results.filter(r => r.success).length;
    const errorCount = results.filter(r => !r.success).length;

    console.log(`[CREATE-AUTH] 🏁 Completed: ${successCount} success, ${errorCount} errors`);

    return NextResponse.json({
      success: true,
      message: 'Auth user creation completed',
      results: {
        total: testUsers.length,
        successful: successCount,
        failed: errorCount,
        details: results
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[CREATE-AUTH] ❌ Unexpected error during auth user creation:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during auth user creation',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
