'use client';

import { useLunaAuth, useCurrentContext } from '@/hooks/use-luna-auth';
import { ContextSwitcher } from '@/components/luna/context-switcher';
import { UserIcon, BuildingOfficeIcon, CogIcon } from '@heroicons/react/24/outline';

export default function LunaTestPage() {
  const { user, loading, error, signOut } = useLunaAuth();
  const { context, isIndividualContext, isOrganizationContext, currentOrganizationId, availableOrganizations } = useCurrentContext();

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-4">Authentication Error</h1>
          <p className="text-gray-600 mb-4">{error}</p>
          <a href="/login" className="text-blue-600 hover:underline">
            Go to Login
          </a>
        </div>
      </div>
    );
  }

  if (!user) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Not Authenticated</h1>
          <p className="text-gray-600 mb-4">Please log in to access Luna.</p>
          <a href="/login" className="bg-blue-600 text-white px-4 py-2 rounded-md hover:bg-blue-700">
            Login
          </a>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <header className="bg-white shadow">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div className="flex items-center">
              <h1 className="text-3xl font-bold text-gray-900">Luna Platform</h1>
              <span className="ml-4 px-2 py-1 bg-blue-100 text-blue-800 text-sm rounded-full">
                Test Environment
              </span>
            </div>
            <div className="flex items-center space-x-4">
              <ContextSwitcher />
              <button
                onClick={signOut}
                className="bg-red-600 text-white px-4 py-2 rounded-md hover:bg-red-700"
              >
                Sign Out
              </button>
            </div>
          </div>
        </div>
      </header>

      <main className="max-w-7xl mx-auto py-6 sm:px-6 lg:px-8">
        <div className="px-4 py-6 sm:px-0">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            {/* User Information */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  User Information
                </h3>
                <dl className="grid grid-cols-1 gap-x-4 gap-y-4 sm:grid-cols-2">
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Name</dt>
                    <dd className="mt-1 text-sm text-gray-900">{user.full_name}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Email</dt>
                    <dd className="mt-1 text-sm text-gray-900">{user.email}</dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Role</dt>
                    <dd className="mt-1 text-sm text-gray-900">
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                        {user.role}
                      </span>
                    </dd>
                  </div>
                  <div>
                    <dt className="text-sm font-medium text-gray-500">Timezone</dt>
                    <dd className="mt-1 text-sm text-gray-900">{user.timezone || 'UTC'}</dd>
                  </div>
                </dl>

                {/* Permissions */}
                <div className="mt-6">
                  <h4 className="text-sm font-medium text-gray-500 mb-2">Permissions</h4>
                  <div className="flex flex-wrap gap-2">
                    {user.isPlatformAdmin && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-red-100 text-red-800">
                        Platform Admin
                      </span>
                    )}
                    {user.isOrgOwner && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-purple-100 text-purple-800">
                        Org Owner
                      </span>
                    )}
                    {user.isOrgAdmin && (
                      <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-orange-100 text-orange-800">
                        Org Admin
                      </span>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Current Context */}
            <div className="bg-white overflow-hidden shadow rounded-lg">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Current Context
                </h3>
                
                {context ? (
                  <div className="space-y-4">
                    <div className="flex items-center">
                      {isOrganizationContext ? (
                        <BuildingOfficeIcon className="h-8 w-8 text-blue-600 mr-3" />
                      ) : (
                        <UserIcon className="h-8 w-8 text-green-600 mr-3" />
                      )}
                      <div>
                        <p className="text-lg font-medium text-gray-900">
                          {isOrganizationContext ? 'Organization Mode' : 'Individual Mode'}
                        </p>
                        {isOrganizationContext && currentOrganizationId && (
                          <p className="text-sm text-gray-500">
                            Organization ID: {currentOrganizationId}
                          </p>
                        )}
                      </div>
                    </div>

                    <div className="bg-gray-50 rounded-md p-4">
                      <h4 className="text-sm font-medium text-gray-700 mb-2">Context Details</h4>
                      <pre className="text-xs text-gray-600 overflow-auto">
                        {JSON.stringify(context, null, 2)}
                      </pre>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-500">No context information available</p>
                )}
              </div>
            </div>

            {/* Organization Memberships */}
            <div className="bg-white overflow-hidden shadow rounded-lg lg:col-span-2">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Organization Memberships
                </h3>
                
                {availableOrganizations.length > 0 ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                    {availableOrganizations.map((org) => (
                      <div key={org.organization_id} className="border border-gray-200 rounded-lg p-4">
                        <div className="flex items-center justify-between">
                          <h4 className="text-sm font-medium text-gray-900">
                            {org.organization_name}
                          </h4>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                            org.role === 'owner' ? 'bg-purple-100 text-purple-800' :
                            org.role === 'admin' ? 'bg-orange-100 text-orange-800' :
                            'bg-blue-100 text-blue-800'
                          }`}>
                            {org.role}
                          </span>
                        </div>
                        <p className="text-xs text-gray-500 mt-1">
                          Status: {org.status}
                        </p>
                        <p className="text-xs text-gray-500">
                          ID: {org.organization_id}
                        </p>
                      </div>
                    ))}
                  </div>
                ) : (
                  <p className="text-gray-500">No organization memberships</p>
                )}
              </div>
            </div>

            {/* Raw User Data */}
            <div className="bg-white overflow-hidden shadow rounded-lg lg:col-span-2">
              <div className="px-4 py-5 sm:p-6">
                <h3 className="text-lg leading-6 font-medium text-gray-900 mb-4">
                  Raw User Data (Debug)
                </h3>
                <div className="bg-gray-50 rounded-md p-4">
                  <pre className="text-xs text-gray-600 overflow-auto max-h-96">
                    {JSON.stringify(user, null, 2)}
                  </pre>
                </div>
              </div>
            </div>

          </div>
        </div>
      </main>
    </div>
  );
}
