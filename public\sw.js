/**
 * Service Worker for Advanced Caching
 * Implements cache-first, network-first, and stale-while-revalidate strategies
 */

const CACHE_NAME = 'bpo-training-v1'
const STATIC_CACHE = 'bpo-static-v1'
const API_CACHE = 'bpo-api-v1'
const IMAGE_CACHE = 'bpo-images-v1'

// Cache strategies
const CACHE_STRATEGIES = {
  CACHE_FIRST: 'cache-first',
  NETWORK_FIRST: 'network-first',
  STALE_WHILE_REVALIDATE: 'stale-while-revalidate',
  NETWORK_ONLY: 'network-only',
  CACHE_ONLY: 'cache-only'
}

// Route configurations
const ROUTE_CONFIGS = [
  {
    pattern: /\.(js|css|woff2?|ttf|eot)$/,
    strategy: CACHE_STRATEGIES.CACHE_FIRST,
    cacheName: STATIC_CACHE,
    maxAge: 365 * 24 * 60 * 60 * 1000, // 1 year
    maxEntries: 100
  },
  {
    pattern: /\.(png|jpg|jpeg|gif|webp|svg|ico)$/,
    strategy: CACHE_STRATEGIES.STALE_WHILE_REVALIDATE,
    cacheName: IMAGE_CACHE,
    maxAge: 30 * 24 * 60 * 60 * 1000, // 30 days
    maxEntries: 200
  },
  {
    pattern: /^https:\/\/.*\.supabase\.co\/rest\/v1\//,
    strategy: CACHE_STRATEGIES.NETWORK_FIRST,
    cacheName: API_CACHE,
    maxAge: 5 * 60 * 1000, // 5 minutes
    maxEntries: 50
  },
  {
    pattern: /\/api\//,
    strategy: CACHE_STRATEGIES.NETWORK_FIRST,
    cacheName: API_CACHE,
    maxAge: 2 * 60 * 1000, // 2 minutes
    maxEntries: 50
  }
]

// =============================================================================
// UTILITY FUNCTIONS
// =============================================================================

function isExpired(response, maxAge) {
  if (!response) return true
  
  const dateHeader = response.headers.get('date')
  if (!dateHeader) return true
  
  const date = new Date(dateHeader)
  return Date.now() - date.getTime() > maxAge
}

function createResponse(data, status = 200) {
  return new Response(JSON.stringify(data), {
    status,
    headers: {
      'Content-Type': 'application/json',
      'Cache-Control': 'no-cache'
    }
  })
}

async function cleanupCache(cacheName, maxEntries) {
  const cache = await caches.open(cacheName)
  const keys = await cache.keys()
  
  if (keys.length > maxEntries) {
    const keysToDelete = keys.slice(0, keys.length - maxEntries)
    await Promise.all(keysToDelete.map(key => cache.delete(key)))
  }
}

// =============================================================================
// CACHE STRATEGIES
// =============================================================================

async function cacheFirst(request, config) {
  const cache = await caches.open(config.cacheName)
  const cachedResponse = await cache.match(request)
  
  if (cachedResponse && !isExpired(cachedResponse, config.maxAge)) {
    return cachedResponse
  }
  
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
      await cleanupCache(config.cacheName, config.maxEntries)
    }
    return networkResponse
  } catch (error) {
    return cachedResponse || createResponse({ error: 'Network error' }, 503)
  }
}

async function networkFirst(request, config) {
  const cache = await caches.open(config.cacheName)
  
  try {
    const networkResponse = await fetch(request)
    if (networkResponse.ok) {
      cache.put(request, networkResponse.clone())
      await cleanupCache(config.cacheName, config.maxEntries)
    }
    return networkResponse
  } catch (error) {
    const cachedResponse = await cache.match(request)
    return cachedResponse || createResponse({ error: 'Network error' }, 503)
  }
}

async function staleWhileRevalidate(request, config) {
  const cache = await caches.open(config.cacheName)
  const cachedResponse = await cache.match(request)
  
  // Always try to fetch from network in background
  const networkPromise = fetch(request).then(response => {
    if (response.ok) {
      cache.put(request, response.clone())
      cleanupCache(config.cacheName, config.maxEntries)
    }
    return response
  }).catch(() => null)
  
  // Return cached response immediately if available
  if (cachedResponse) {
    return cachedResponse
  }
  
  // If no cache, wait for network
  return networkPromise || createResponse({ error: 'No cache available' }, 503)
}

// =============================================================================
// REQUEST HANDLER
// =============================================================================

async function handleRequest(event) {
  const { request } = event
  const url = new URL(request.url)
  
  // Skip non-GET requests
  if (request.method !== 'GET') {
    return fetch(request)
  }
  
  // Skip chrome-extension and other protocols
  if (!url.protocol.startsWith('http')) {
    return fetch(request)
  }
  
  // Find matching route configuration
  const config = ROUTE_CONFIGS.find(route => route.pattern.test(request.url))
  
  if (!config) {
    return fetch(request)
  }
  
  // Apply appropriate caching strategy
  switch (config.strategy) {
    case CACHE_STRATEGIES.CACHE_FIRST:
      return cacheFirst(request, config)
    
    case CACHE_STRATEGIES.NETWORK_FIRST:
      return networkFirst(request, config)
    
    case CACHE_STRATEGIES.STALE_WHILE_REVALIDATE:
      return staleWhileRevalidate(request, config)
    
    case CACHE_STRATEGIES.NETWORK_ONLY:
      return fetch(request)
    
    case CACHE_STRATEGIES.CACHE_ONLY:
      const cache = await caches.open(config.cacheName)
      return cache.match(request) || createResponse({ error: 'Not in cache' }, 404)
    
    default:
      return fetch(request)
  }
}

// =============================================================================
// SERVICE WORKER EVENTS
// =============================================================================

self.addEventListener('install', event => {
  console.log('Service Worker installing...')
  
  event.waitUntil(
    caches.open(STATIC_CACHE).then(cache => {
      // Pre-cache critical resources
      return cache.addAll([
        '/',
        '/manifest.json',
        '/favicon.ico'
      ])
    }).then(() => {
      return self.skipWaiting()
    })
  )
})

self.addEventListener('activate', event => {
  console.log('Service Worker activating...')
  
  event.waitUntil(
    caches.keys().then(cacheNames => {
      return Promise.all(
        cacheNames.map(cacheName => {
          // Delete old caches
          if (cacheName !== CACHE_NAME && 
              cacheName !== STATIC_CACHE && 
              cacheName !== API_CACHE && 
              cacheName !== IMAGE_CACHE) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    }).then(() => {
      return self.clients.claim()
    })
  )
})

self.addEventListener('fetch', event => {
  event.respondWith(handleRequest(event))
})

// Handle background sync for offline actions
self.addEventListener('sync', event => {
  if (event.tag === 'background-sync') {
    event.waitUntil(
      // Handle offline actions when back online
      handleBackgroundSync()
    )
  }
})

// Handle push notifications
self.addEventListener('push', event => {
  if (event.data) {
    const data = event.data.json()
    
    event.waitUntil(
      self.registration.showNotification(data.title, {
        body: data.body,
        icon: '/icon-192x192.png',
        badge: '/badge-72x72.png',
        data: data.data
      })
    )
  }
})

// Handle notification clicks
self.addEventListener('notificationclick', event => {
  event.notification.close()
  
  event.waitUntil(
    clients.openWindow(event.notification.data?.url || '/')
  )
})

// =============================================================================
// BACKGROUND SYNC HANDLER
// =============================================================================

async function handleBackgroundSync() {
  try {
    // Get pending actions from IndexedDB or localStorage
    const pendingActions = await getPendingActions()
    
    for (const action of pendingActions) {
      try {
        await fetch(action.url, {
          method: action.method,
          headers: action.headers,
          body: action.body
        })
        
        // Remove successful action
        await removePendingAction(action.id)
      } catch (error) {
        console.error('Failed to sync action:', error)
      }
    }
  } catch (error) {
    console.error('Background sync failed:', error)
  }
}

// Placeholder functions for pending actions
async function getPendingActions() {
  // In a real implementation, retrieve from IndexedDB
  return []
}

async function removePendingAction(id) {
  // In a real implementation, remove from IndexedDB
}

// =============================================================================
// CACHE MANAGEMENT
// =============================================================================

// Periodic cache cleanup
setInterval(async () => {
  for (const config of ROUTE_CONFIGS) {
    await cleanupCache(config.cacheName, config.maxEntries)
  }
}, 60 * 60 * 1000) // Every hour

// Message handling for cache management
self.addEventListener('message', event => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
  
  if (event.data && event.data.type === 'CLEAR_CACHE') {
    event.waitUntil(
      caches.keys().then(cacheNames => {
        return Promise.all(
          cacheNames.map(cacheName => caches.delete(cacheName))
        )
      })
    )
  }
  
  if (event.data && event.data.type === 'GET_CACHE_SIZE') {
    event.waitUntil(
      getCacheSize().then(size => {
        event.ports[0].postMessage({ size })
      })
    )
  }
})

async function getCacheSize() {
  const cacheNames = await caches.keys()
  let totalSize = 0
  
  for (const cacheName of cacheNames) {
    const cache = await caches.open(cacheName)
    const keys = await cache.keys()
    
    for (const key of keys) {
      const response = await cache.match(key)
      if (response) {
        const blob = await response.blob()
        totalSize += blob.size
      }
    }
  }
  
  return totalSize
}
