-- ============================================================================
-- LUNA PLATFORM - ROW LEVEL SECURITY (RLS) POLICIES
-- Comprehensive security policies for multi-tenant data isolation
-- ============================================================================

-- Enable RLS on all tables
ALTER TABLE users ENABLE ROW LEVEL SECURITY;
ALTER TABLE organizations ENABLE ROW LEVEL SECURITY;
ALTER TABLE organization_memberships ENABLE ROW LEVEL SECURITY;
ALTER TABLE user_contexts ENABLE ROW LEVEL SECURITY;
ALTER TABLE individuals ENABLE ROW LEVEL SECURITY;
ALTER TABLE training_modules ENABLE ROW LEVEL SECURITY;
ALTER TABLE lessons ENABLE ROW LEVEL SECURITY;
ALTER TABLE activities ENABLE ROW LEVEL SECURITY;
ALTER TABLE progress_records ENABLE ROW LEVEL SECURITY;

-- ============================================================================
-- HELPER FUNCTIONS FOR RLS POLICIES
-- ============================================================================

-- Function to check if user is platform admin
CREATE OR REPLACE FUNCTION is_platform_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'platform_admin'
    AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to get user's organization memberships
CREATE OR REPLACE FUNCTION get_user_organizations()
RETURNS TABLE(organization_id UUID, role org_member_role) AS $$
BEGIN
  RETURN QUERY
  SELECT om.organization_id, om.role
  FROM organization_memberships om
  WHERE om.user_id = auth.uid()
    AND om.status = 'active';
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is organization admin
CREATE OR REPLACE FUNCTION is_org_admin(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM organization_memberships
    WHERE user_id = auth.uid()
      AND organization_id = org_id
      AND role IN ('owner', 'admin')
      AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Function to check if user is organization member
CREATE OR REPLACE FUNCTION is_org_member(org_id UUID)
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM organization_memberships
    WHERE user_id = auth.uid()
      AND organization_id = org_id
      AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- ============================================================================
-- USERS TABLE POLICIES
-- ============================================================================

-- Users can view their own profile and basic info of org members
CREATE POLICY "users_select_policy" ON users
  FOR SELECT USING (
    -- Own profile
    id = auth.uid()
    OR
    -- Platform admin can see all
    is_platform_admin()
    OR
    -- Organization members can see basic info of other members
    id IN (
      SELECT DISTINCT om2.user_id
      FROM organization_memberships om1
      JOIN organization_memberships om2 ON om1.organization_id = om2.organization_id
      WHERE om1.user_id = auth.uid()
        AND om1.status = 'active'
        AND om2.status = 'active'
    )
  );

-- Users can only update their own profile
CREATE POLICY "users_update_policy" ON users
  FOR UPDATE USING (
    id = auth.uid()
    OR
    is_platform_admin()
  );

-- Only platform admins can insert users (registration handled by auth)
CREATE POLICY "users_insert_policy" ON users
  FOR INSERT WITH CHECK (
    is_platform_admin()
    OR
    auth.uid() = id -- Allow self-registration
  );

-- Only platform admins can delete users
CREATE POLICY "users_delete_policy" ON users
  FOR DELETE USING (
    is_platform_admin()
  );

-- ============================================================================
-- ORGANIZATIONS TABLE POLICIES
-- ============================================================================

-- Users can view organizations they're members of
CREATE POLICY "organizations_select_policy" ON organizations
  FOR SELECT USING (
    -- Platform admin can see all
    is_platform_admin()
    OR
    -- Organization members can see their organization
    id IN (
      SELECT organization_id FROM organization_memberships
      WHERE user_id = auth.uid() AND status = 'active'
    )
    OR
    -- Public organizations (if we add this feature)
    status = 'active' -- For now, all active orgs are discoverable
  );

-- Only organization owners/admins can update organization details
CREATE POLICY "organizations_update_policy" ON organizations
  FOR UPDATE USING (
    is_platform_admin()
    OR
    is_org_admin(id)
  );

-- Any authenticated user can create an organization
CREATE POLICY "organizations_insert_policy" ON organizations
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
    AND
    created_by = auth.uid()
  );

-- Only platform admins or organization owners can delete organizations
CREATE POLICY "organizations_delete_policy" ON organizations
  FOR DELETE USING (
    is_platform_admin()
    OR
    (created_by = auth.uid() AND is_org_admin(id))
  );

-- ============================================================================
-- ORGANIZATION MEMBERSHIPS TABLE POLICIES
-- ============================================================================

-- Users can view their own memberships and org admins can view all org memberships
CREATE POLICY "memberships_select_policy" ON organization_memberships
  FOR SELECT USING (
    -- Own memberships
    user_id = auth.uid()
    OR
    -- Platform admin can see all
    is_platform_admin()
    OR
    -- Organization admins can see all memberships in their org
    is_org_admin(organization_id)
  );

-- Organization admins can update memberships, users can update their own status
CREATE POLICY "memberships_update_policy" ON organization_memberships
  FOR UPDATE USING (
    -- Platform admin can update all
    is_platform_admin()
    OR
    -- Organization admins can update memberships in their org
    is_org_admin(organization_id)
    OR
    -- Users can update their own membership status (accept/decline invitations)
    (user_id = auth.uid() AND status = 'pending')
  );

-- Organization admins can create memberships (invitations)
CREATE POLICY "memberships_insert_policy" ON organization_memberships
  FOR INSERT WITH CHECK (
    is_platform_admin()
    OR
    is_org_admin(organization_id)
  );

-- Organization admins can delete memberships
CREATE POLICY "memberships_delete_policy" ON organization_memberships
  FOR DELETE USING (
    is_platform_admin()
    OR
    is_org_admin(organization_id)
    OR
    user_id = auth.uid() -- Users can remove themselves
  );

-- ============================================================================
-- USER CONTEXTS TABLE POLICIES
-- ============================================================================

-- Users can only access their own context
CREATE POLICY "contexts_select_policy" ON user_contexts
  FOR SELECT USING (
    user_id = auth.uid()
    OR
    is_platform_admin()
  );

-- Users can only update their own context
CREATE POLICY "contexts_update_policy" ON user_contexts
  FOR UPDATE USING (
    user_id = auth.uid()
    OR
    is_platform_admin()
  );

-- Users can create their own context
CREATE POLICY "contexts_insert_policy" ON user_contexts
  FOR INSERT WITH CHECK (
    user_id = auth.uid()
  );

-- Users can delete their own context
CREATE POLICY "contexts_delete_policy" ON user_contexts
  FOR DELETE USING (
    user_id = auth.uid()
    OR
    is_platform_admin()
  );

-- ============================================================================
-- INDIVIDUALS TABLE POLICIES
-- ============================================================================

-- Users can view their own profile and org members can view based on visibility settings
CREATE POLICY "individuals_select_policy" ON individuals
  FOR SELECT USING (
    -- Own profile
    user_id = auth.uid()
    OR
    -- Platform admin can see all
    is_platform_admin()
    OR
    -- Organization members can see profiles based on visibility
    (
      profile_visibility = 'organization'
      AND
      user_id IN (
        SELECT DISTINCT om2.user_id
        FROM organization_memberships om1
        JOIN organization_memberships om2 ON om1.organization_id = om2.organization_id
        WHERE om1.user_id = auth.uid()
          AND om1.status = 'active'
          AND om2.status = 'active'
      )
    )
    OR
    -- Public profiles
    profile_visibility = 'public'
  );

-- Users can only update their own individual profile
CREATE POLICY "individuals_update_policy" ON individuals
  FOR UPDATE USING (
    user_id = auth.uid()
    OR
    is_platform_admin()
  );

-- Users can create their own individual profile
CREATE POLICY "individuals_insert_policy" ON individuals
  FOR INSERT WITH CHECK (
    user_id = auth.uid()
  );

-- Users can delete their own individual profile
CREATE POLICY "individuals_delete_policy" ON individuals
  FOR DELETE USING (
    user_id = auth.uid()
    OR
    is_platform_admin()
  );

-- ============================================================================
-- TRAINING MODULES TABLE POLICIES
-- ============================================================================

-- Users can view published modules and their own drafts
CREATE POLICY "modules_select_policy" ON training_modules
  FOR SELECT USING (
    -- Published modules are visible to all authenticated users
    (status = 'published' AND auth.uid() IS NOT NULL)
    OR
    -- Platform admin can see all
    is_platform_admin()
    OR
    -- Module creators can see their own modules
    created_by = auth.uid()
    OR
    -- Organization members can see org-specific modules
    (
      organization_id IS NOT NULL
      AND
      is_org_member(organization_id)
    )
  );

-- Only module creators, org admins, or platform admins can update modules
CREATE POLICY "modules_update_policy" ON training_modules
  FOR UPDATE USING (
    is_platform_admin()
    OR
    created_by = auth.uid()
    OR
    (organization_id IS NOT NULL AND is_org_admin(organization_id))
  );

-- Authenticated users can create modules
CREATE POLICY "modules_insert_policy" ON training_modules
  FOR INSERT WITH CHECK (
    auth.uid() IS NOT NULL
    AND
    created_by = auth.uid()
  );

-- Only creators, org admins, or platform admins can delete modules
CREATE POLICY "modules_delete_policy" ON training_modules
  FOR DELETE USING (
    is_platform_admin()
    OR
    created_by = auth.uid()
    OR
    (organization_id IS NOT NULL AND is_org_admin(organization_id))
  );

-- ============================================================================
-- LESSONS TABLE POLICIES
-- ============================================================================

-- Users can view lessons if they can view the parent module
CREATE POLICY "lessons_select_policy" ON lessons
  FOR SELECT USING (
    module_id IN (
      SELECT id FROM training_modules
      WHERE (
        (status = 'published' AND auth.uid() IS NOT NULL)
        OR
        is_platform_admin()
        OR
        created_by = auth.uid()
        OR
        (organization_id IS NOT NULL AND is_org_member(organization_id))
      )
    )
  );

-- Users can update lessons if they can update the parent module
CREATE POLICY "lessons_update_policy" ON lessons
  FOR UPDATE USING (
    module_id IN (
      SELECT id FROM training_modules
      WHERE (
        is_platform_admin()
        OR
        created_by = auth.uid()
        OR
        (organization_id IS NOT NULL AND is_org_admin(organization_id))
      )
    )
  );

-- Users can create lessons if they can update the parent module
CREATE POLICY "lessons_insert_policy" ON lessons
  FOR INSERT WITH CHECK (
    module_id IN (
      SELECT id FROM training_modules
      WHERE (
        is_platform_admin()
        OR
        created_by = auth.uid()
        OR
        (organization_id IS NOT NULL AND is_org_admin(organization_id))
      )
    )
  );

-- Users can delete lessons if they can update the parent module
CREATE POLICY "lessons_delete_policy" ON lessons
  FOR DELETE USING (
    module_id IN (
      SELECT id FROM training_modules
      WHERE (
        is_platform_admin()
        OR
        created_by = auth.uid()
        OR
        (organization_id IS NOT NULL AND is_org_admin(organization_id))
      )
    )
  );

-- ============================================================================
-- ACTIVITIES TABLE POLICIES
-- ============================================================================

-- Users can view activities if they can view the parent lesson
CREATE POLICY "activities_select_policy" ON activities
  FOR SELECT USING (
    lesson_id IN (
      SELECT l.id FROM lessons l
      JOIN training_modules tm ON l.module_id = tm.id
      WHERE (
        (tm.status = 'published' AND auth.uid() IS NOT NULL)
        OR
        is_platform_admin()
        OR
        tm.created_by = auth.uid()
        OR
        (tm.organization_id IS NOT NULL AND is_org_member(tm.organization_id))
      )
    )
  );

-- Similar policies for activities update, insert, delete
CREATE POLICY "activities_update_policy" ON activities
  FOR UPDATE USING (
    lesson_id IN (
      SELECT l.id FROM lessons l
      JOIN training_modules tm ON l.module_id = tm.id
      WHERE (
        is_platform_admin()
        OR
        tm.created_by = auth.uid()
        OR
        (tm.organization_id IS NOT NULL AND is_org_admin(tm.organization_id))
      )
    )
  );

CREATE POLICY "activities_insert_policy" ON activities
  FOR INSERT WITH CHECK (
    lesson_id IN (
      SELECT l.id FROM lessons l
      JOIN training_modules tm ON l.module_id = tm.id
      WHERE (
        is_platform_admin()
        OR
        tm.created_by = auth.uid()
        OR
        (tm.organization_id IS NOT NULL AND is_org_admin(tm.organization_id))
      )
    )
  );

CREATE POLICY "activities_delete_policy" ON activities
  FOR DELETE USING (
    lesson_id IN (
      SELECT l.id FROM lessons l
      JOIN training_modules tm ON l.module_id = tm.id
      WHERE (
        is_platform_admin()
        OR
        tm.created_by = auth.uid()
        OR
        (tm.organization_id IS NOT NULL AND is_org_admin(tm.organization_id))
      )
    )
  );

-- ============================================================================
-- PROGRESS RECORDS TABLE POLICIES
-- ============================================================================

-- Users can view their own progress and org admins can view org member progress
CREATE POLICY "progress_select_policy" ON progress_records
  FOR SELECT USING (
    -- Own progress
    user_id = auth.uid()
    OR
    -- Platform admin can see all
    is_platform_admin()
    OR
    -- Organization admins can see progress of their org members
    (
      organization_id IS NOT NULL
      AND
      is_org_admin(organization_id)
      AND
      user_id IN (
        SELECT user_id FROM organization_memberships
        WHERE organization_id = progress_records.organization_id
          AND status = 'active'
      )
    )
  );

-- Users can update their own progress
CREATE POLICY "progress_update_policy" ON progress_records
  FOR UPDATE USING (
    user_id = auth.uid()
    OR
    is_platform_admin()
  );

-- Users can create their own progress records
CREATE POLICY "progress_insert_policy" ON progress_records
  FOR INSERT WITH CHECK (
    user_id = auth.uid()
  );

-- Users can delete their own progress records
CREATE POLICY "progress_delete_policy" ON progress_records
  FOR DELETE USING (
    user_id = auth.uid()
    OR
    is_platform_admin()
  );

-- ============================================================================
-- GRANT PERMISSIONS
-- ============================================================================

-- Grant usage on schema
GRANT USAGE ON SCHEMA public TO authenticated;
GRANT USAGE ON SCHEMA public TO anon;

-- Grant permissions on tables
GRANT ALL ON ALL TABLES IN SCHEMA public TO authenticated;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO anon;

-- Grant permissions on sequences
GRANT ALL ON ALL SEQUENCES IN SCHEMA public TO authenticated;

-- ============================================================================
-- POLICY VALIDATION QUERIES
-- ============================================================================

-- Test queries to validate RLS policies work correctly
-- (These should be run after setting up test data)

/*
-- Test 1: User can only see their own profile
SELECT * FROM users WHERE id = auth.uid();

-- Test 2: User can see organizations they're a member of
SELECT o.* FROM organizations o
JOIN organization_memberships om ON o.id = om.organization_id
WHERE om.user_id = auth.uid() AND om.status = 'active';

-- Test 3: User cannot see other users' progress
SELECT * FROM progress_records WHERE user_id != auth.uid();

-- Test 4: Organization admin can see member progress
SELECT pr.* FROM progress_records pr
JOIN organization_memberships om ON pr.user_id = om.user_id
WHERE om.organization_id = 'test-org-id'
  AND om.user_id = auth.uid()
  AND om.role IN ('owner', 'admin');
*/
