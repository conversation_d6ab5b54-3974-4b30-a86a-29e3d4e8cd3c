import { NextRequest, NextResponse } from 'next/server';
import { clearRateLimit, checkRateLimit, cleanupExpiredEntries } from '@/lib/auth-rate-limit';

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const email = searchParams.get('email');
    const action = searchParams.get('action');

    switch (action) {
      case 'check':
        const status = checkRateLimit(email || undefined);
        return NextResponse.json({
          success: true,
          data: status,
          message: `Rate limit status for ${email || 'anonymous'}`
        });

      case 'clear':
        clearRateLimit(email || undefined);
        return NextResponse.json({
          success: true,
          message: `Rate limit cleared for ${email || 'anonymous'}`
        });

      case 'cleanup':
        cleanupExpiredEntries();
        return NextResponse.json({
          success: true,
          message: 'Expired rate limit entries cleaned up'
        });

      default:
        return NextResponse.json({
          success: false,
          message: 'Invalid action. Use: check, clear, or cleanup'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Rate limit API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { email, action } = body;

    switch (action) {
      case 'clear':
        clearRateLimit(email);
        return NextResponse.json({
          success: true,
          message: `Rate limit cleared for ${email || 'anonymous'}`
        });

      case 'clear-all':
        // This would require a more comprehensive implementation
        // For now, just cleanup expired entries
        cleanupExpiredEntries();
        return NextResponse.json({
          success: true,
          message: 'Expired rate limit entries cleaned up'
        });

      default:
        return NextResponse.json({
          success: false,
          message: 'Invalid action. Use: clear or clear-all'
        }, { status: 400 });
    }
  } catch (error) {
    console.error('Rate limit API error:', error);
    return NextResponse.json({
      success: false,
      message: 'Internal server error'
    }, { status: 500 });
  }
}
