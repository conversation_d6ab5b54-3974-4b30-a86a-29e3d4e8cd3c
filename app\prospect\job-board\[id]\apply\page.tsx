import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'
import JobApplicationPage from '@/components/job-application-page'

export default async function JobApplication({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = createServerComponentClient({ cookies })

  // Fetch the specific job posting
  const { data: jobPosting, error } = await supabase
    .from("job_postings")
    .select(`
      *,
      bpos(id, name, logo_url)
    `)
    .eq("id", id)
    .eq("status", "published")
    .single()
  
  if (error || !jobPosting) {
    notFound()
  }

  // Format job posting data for the component
  const formattedJob = {
    id: jobPosting.id,
    title: jobPosting.title,
    company: jobPosting.bpos?.name || "Unknown Company",
    companyLogo: jobPosting.bpos?.logo_url || null,
    jobType: jobPosting.job_type,
    location: typeof jobPosting.location === 'object' && jobPosting.location !== null
      ? (() => {
          const loc = jobPosting.location as any
          if (loc.city && loc.country) return `${loc.city}, ${loc.country}`
          if (loc.city) return loc.city
          if (loc.country) return loc.country
          return "Remote"
        })()
      : "Remote"
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
      <JobApplicationPage job={formattedJob} />
    </div>
  )
}
