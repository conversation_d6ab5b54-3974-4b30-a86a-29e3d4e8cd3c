-- ============================================================================
-- LUNA PLATFORM - PHASE 1 DATABASE SCHEMA
-- Complete database schema for Luna's multi-tenant architecture
-- ============================================================================

-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- ============================================================================
-- ENUM TYPES FOR LUNA PLATFORM
-- ============================================================================

-- User roles for Luna platform
CREATE TYPE user_role AS ENUM (
  'platform_admin',    -- Luna platform administrators
  'individual',        -- Standalone individual users
  'org_owner',         -- Organization owners
  'org_admin',         -- Organization administrators
  'org_member'         -- Organization team members
);

-- User account status
CREATE TYPE user_status AS ENUM (
  'active', 
  'inactive', 
  'pending_activation', 
  'suspended'
);

-- Organization status
CREATE TYPE org_status AS ENUM (
  'active', 
  'suspended', 
  'trial', 
  'cancelled'
);

-- Organization member roles
CREATE TYPE org_member_role AS ENUM (
  'owner',      -- Full organization control
  'admin',      -- Administrative privileges
  'manager',    -- Team management
  'member',     -- Standard access
  'viewer'      -- Read-only access
);

-- Membership status
CREATE TYPE membership_status AS ENUM (
  'pending',    -- Invitation sent, not accepted
  'active',     -- Active membership
  'inactive',   -- Temporarily disabled
  'removed'     -- Permanently removed
);

-- User context types
CREATE TYPE context_type AS ENUM (
  'individual', 
  'organization'
);

-- Profile visibility settings
CREATE TYPE profile_visibility AS ENUM (
  'private', 
  'organization', 
  'public'
);

-- Learning status
CREATE TYPE learning_status AS ENUM (
  'not_started', 
  'in_progress', 
  'completed', 
  'paused'
);

-- Training module status
CREATE TYPE module_status AS ENUM (
  'draft', 
  'published', 
  'archived'
);

-- Progress status
CREATE TYPE progress_status AS ENUM (
  'not_started', 
  'in_progress', 
  'completed', 
  'failed'
);

-- ============================================================================
-- CORE TABLES
-- ============================================================================

-- Enhanced Users Table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'individual',
  status user_status DEFAULT 'active',
  avatar_url TEXT,
  
  -- Individual account data
  personal_skills JSONB DEFAULT '[]',
  learning_preferences JSONB DEFAULT '{}',
  industry_interests TEXT[],
  timezone VARCHAR(50) DEFAULT 'UTC',
  
  -- Authentication
  last_login TIMESTAMPTZ,
  email_verified BOOLEAN DEFAULT false,
  phone_number VARCHAR(20),
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organizations Table (Multi-Tenant Entities)
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  industry VARCHAR(100),
  size_range VARCHAR(50),
  logo_url TEXT,
  website_url TEXT,
  
  -- Multi-tenancy settings
  subdomain VARCHAR(50) UNIQUE,
  custom_domain VARCHAR(255),
  branding_config JSONB DEFAULT '{}',
  
  -- Subscription & limits
  subscription_tier VARCHAR(50) DEFAULT 'basic',
  max_members INTEGER DEFAULT 50,
  features_enabled JSONB DEFAULT '{}',
  
  -- Contact information
  contact_email VARCHAR(255),
  contact_phone VARCHAR(20),
  address JSONB DEFAULT '{}',
  
  -- Ownership and status
  created_by UUID NOT NULL REFERENCES users(id),
  status org_status DEFAULT 'active',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organization Memberships (Bridge Table)
CREATE TABLE organization_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- Membership details
  role org_member_role NOT NULL,
  permissions JSONB DEFAULT '{}',
  status membership_status DEFAULT 'pending',
  
  -- Invitation tracking
  invited_by UUID REFERENCES users(id),
  invited_at TIMESTAMPTZ,
  invitation_token VARCHAR(255),
  accepted_at TIMESTAMPTZ,
  
  -- Custom settings
  custom_title VARCHAR(255),
  department VARCHAR(100),
  start_date DATE,
  end_date DATE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, organization_id)
);

-- User Contexts (Context Switching)
CREATE TABLE user_contexts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Current context
  active_context context_type NOT NULL DEFAULT 'individual',
  active_organization_id UUID REFERENCES organizations(id),
  
  -- Session management
  last_context_switch TIMESTAMPTZ DEFAULT NOW(),
  session_data JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Individuals Table (Enhanced from prospects)
CREATE TABLE individuals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Profile information
  contact_info JSONB DEFAULT '{}',
  education JSONB[] DEFAULT '{}',
  experience JSONB[] DEFAULT '{}',
  skills JSONB[] DEFAULT '{}',
  
  -- Media assets
  profile_image_url TEXT,
  intro_video_url TEXT,
  resume_url TEXT,
  portfolio_url TEXT,
  
  -- Privacy & visibility
  profile_visibility profile_visibility DEFAULT 'private',
  searchable_by_orgs BOOLEAN DEFAULT false,
  
  -- Learning status
  learning_status learning_status DEFAULT 'not_started',
  current_learning_path UUID,
  
  -- Career information
  career_goals JSONB DEFAULT '{}',
  preferred_industries TEXT[],
  job_search_status VARCHAR(50),
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- ============================================================================
-- TRAINING AND LEARNING TABLES
-- ============================================================================

-- Enhanced Training Modules
CREATE TABLE training_modules (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  cover_image_url TEXT,
  
  -- Content details
  duration_minutes INTEGER,
  difficulty_level INTEGER DEFAULT 1, -- 1-5 scale
  learning_objectives JSONB DEFAULT '[]',
  prerequisites JSONB DEFAULT '[]',
  
  -- Skills and competencies
  skills_taught JSONB DEFAULT '[]',
  competency_level INTEGER DEFAULT 1,
  industry_focus TEXT[],
  
  -- Module configuration
  status module_status DEFAULT 'draft',
  is_featured BOOLEAN DEFAULT false,
  requires_enrollment BOOLEAN DEFAULT false,
  
  -- Pricing and access
  price_cents INTEGER DEFAULT 0,
  currency VARCHAR(3) DEFAULT 'USD',
  access_level VARCHAR(50) DEFAULT 'public', -- 'public', 'organization', 'premium'
  
  -- Metadata
  created_by UUID NOT NULL REFERENCES users(id),
  organization_id UUID REFERENCES organizations(id), -- If org-specific content
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Lessons (unchanged structure, but enhanced)
CREATE TABLE lessons (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  module_id UUID NOT NULL REFERENCES training_modules(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Lesson content
  content_type VARCHAR(50) NOT NULL, -- 'video', 'text', 'interactive', 'quiz'
  content_url TEXT,
  content_data JSONB DEFAULT '{}',
  
  -- Lesson structure
  order_index INTEGER NOT NULL,
  duration_minutes INTEGER,
  is_required BOOLEAN DEFAULT true,
  
  -- Skills development
  skills_practiced JSONB DEFAULT '[]',
  learning_outcomes JSONB DEFAULT '[]',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Activities (enhanced for skills tracking)
CREATE TABLE activities (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  lesson_id UUID NOT NULL REFERENCES lessons(id) ON DELETE CASCADE,
  title VARCHAR(255) NOT NULL,
  description TEXT,
  
  -- Activity configuration
  activity_type VARCHAR(50) NOT NULL, -- 'quiz', 'assignment', 'discussion', 'practice'
  activity_data JSONB NOT NULL DEFAULT '{}',
  
  -- Completion criteria
  passing_score INTEGER DEFAULT 70,
  max_attempts INTEGER DEFAULT 3,
  time_limit_minutes INTEGER,
  
  -- Skills assessment
  skills_assessed JSONB DEFAULT '[]',
  competency_weight DECIMAL(3,2) DEFAULT 1.0,
  
  -- Structure
  order_index INTEGER NOT NULL,
  is_required BOOLEAN DEFAULT true,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Progress Records (enhanced for multi-tenant)
CREATE TABLE progress_records (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  activity_id UUID NOT NULL REFERENCES activities(id) ON DELETE CASCADE,
  organization_id UUID REFERENCES organizations(id), -- Track org context
  
  -- Progress details
  status progress_status NOT NULL DEFAULT 'not_started',
  score INTEGER CHECK (score >= 0 AND score <= 100),
  attempts INTEGER DEFAULT 0,
  
  -- Timing
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  time_spent_seconds INTEGER DEFAULT 0,
  
  -- Skills development tracking
  skills_gained JSONB DEFAULT '{}',
  competency_improvements JSONB DEFAULT '{}',
  
  -- Assessment data
  responses JSONB DEFAULT '{}',
  feedback JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, activity_id, organization_id)
);

-- ============================================================================
-- UTILITY FUNCTIONS
-- ============================================================================

-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for timestamp updates
CREATE TRIGGER update_users_timestamp
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_organizations_timestamp
    BEFORE UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_memberships_timestamp
    BEFORE UPDATE ON organization_memberships
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_contexts_timestamp
    BEFORE UPDATE ON user_contexts
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_individuals_timestamp
    BEFORE UPDATE ON individuals
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_modules_timestamp
    BEFORE UPDATE ON training_modules
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

-- ============================================================================
-- INDEXES FOR PERFORMANCE
-- ============================================================================

-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);

-- Organizations table indexes
CREATE INDEX idx_organizations_slug ON organizations(slug);
CREATE INDEX idx_organizations_subdomain ON organizations(subdomain);
CREATE INDEX idx_organizations_industry ON organizations(industry);
CREATE INDEX idx_organizations_created_by ON organizations(created_by);

-- Memberships table indexes
CREATE INDEX idx_memberships_user_id ON organization_memberships(user_id);
CREATE INDEX idx_memberships_org_id ON organization_memberships(organization_id);
CREATE INDEX idx_memberships_status ON organization_memberships(status);
CREATE INDEX idx_memberships_role ON organization_memberships(role);

-- Context table indexes
CREATE INDEX idx_contexts_user_id ON user_contexts(user_id);
CREATE INDEX idx_contexts_active_org ON user_contexts(active_organization_id);

-- Training content indexes
CREATE INDEX idx_modules_status ON training_modules(status);
CREATE INDEX idx_modules_created_by ON training_modules(created_by);
CREATE INDEX idx_modules_org_id ON training_modules(organization_id);
CREATE INDEX idx_lessons_module_id ON lessons(module_id);
CREATE INDEX idx_activities_lesson_id ON activities(lesson_id);
CREATE INDEX idx_progress_user_id ON progress_records(user_id);
CREATE INDEX idx_progress_activity_id ON progress_records(activity_id);

-- ============================================================================
-- INITIAL DATA SETUP
-- ============================================================================

-- Create default platform admin user (to be updated with real data)
INSERT INTO users (email, full_name, role, status, email_verified) 
VALUES ('<EMAIL>', 'Luna Platform Admin', 'platform_admin', 'active', true);

-- ============================================================================
-- SCHEMA VALIDATION
-- ============================================================================

-- Verify all tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_schema = 'public' 
  AND table_type = 'BASE TABLE'
ORDER BY table_name;
