-- ============================================================================
-- LUNA PLATFORM - SEED TEST DATA
-- Comprehensive test data including all user account types
-- Default password for all accounts: BeeMO5317
-- Note: Auth users will need to be created via Supabase Auth API or dashboard
-- ============================================================================

-- ============================================================================
-- USERS - Core Account Data
-- ============================================================================

-- Platform Admin
INSERT INTO users (id, email, full_name, role, status, email_verified, timezone) VALUES
('********-1111-1111-1111-********1111', '<EMAIL>', 'Luna Platform Admin', 'platform_admin', 'active', true, 'UTC');

-- Individual Users (Freelancers, Job Seekers)
INSERT INTO users (id, email, full_name, role, status, email_verified, timezone) VALUES
('********-0000-0000-0000-********0002', '<EMAIL>', 'Sarah Johnson', 'individual', 'active', true, 'America/New_York'),
('********-0000-0000-0000-********0003', '<EMAIL>', 'Mike Chen', 'individual', 'active', true, 'America/Los_Angeles'),
('********-0000-0000-0000-********0004', '<EMAIL>', 'Alex Rodriguez', 'individual', 'active', true, 'America/Chicago');

-- Organization Owners
INSERT INTO users (id, email, full_name, role, status, email_verified, timezone) VALUES
('********-0000-0000-0000-************', '<EMAIL>', 'Jennifer Smith', 'org_owner', 'active', true, 'America/New_York'),
('********-0000-0000-0000-********0006', '<EMAIL>', 'David Kim', 'org_owner', 'active', true, 'America/Los_Angeles');

-- Organization Admins
INSERT INTO users (id, email, full_name, role, status, email_verified, timezone) VALUES
('********-0000-0000-0000-********0007', '<EMAIL>', 'Lisa Wang', 'org_admin', 'active', true, 'America/New_York'),
('********-0000-0000-0000-********0008', '<EMAIL>', 'Robert Johnson', 'org_admin', 'active', true, 'America/Los_Angeles');

-- Organization Members
INSERT INTO users (id, email, full_name, role, status, email_verified, timezone) VALUES
('********-0000-0000-0000-********0009', '<EMAIL>', 'John Davis', 'org_member', 'active', true, 'America/New_York'),
('********-0000-0000-0000-********0010', '<EMAIL>', 'Emma Wilson', 'org_member', 'active', true, 'America/Los_Angeles'),
('********-0000-0000-0000-********0011', '<EMAIL>', 'Carlos Martinez', 'org_member', 'active', true, 'America/New_York');

-- Hybrid Users (Individual + Organization Member)
INSERT INTO users (id, email, full_name, role, status, email_verified, timezone) VALUES
('********-0000-0000-0000-********0012', '<EMAIL>', 'Maria Garcia', 'individual', 'active', true, 'America/Chicago');

-- ============================================================================
-- ORGANIZATIONS
-- ============================================================================

INSERT INTO organizations (
  id, name, slug, description, industry, size_range,
  subdomain, subscription_tier, max_members, created_by, status
) VALUES
(
  '10000000-0000-0000-0000-********0001',
  'TechCorp Solutions',
  'techcorp',
  'Leading software development company specializing in enterprise solutions',
  'Technology',
  '50-200',
  'techcorp',
  'professional',
  200,
  '********-0000-0000-0000-************',
  'active'
),
(
  '10000000-0000-0000-0000-********0002',
  'HealthPlus Medical',
  'healthplus',
  'Comprehensive healthcare services and medical training',
  'Healthcare',
  '100-500',
  'healthplus',
  'enterprise',
  500,
  '********-0000-0000-0000-********0006',
  'active'
),
(
  '10000000-0000-0000-0000-********0003',
  'EduLearn Academy',
  'edulearn',
  'Online education platform for professional development',
  'Education',
  '10-50',
  'edulearn',
  'basic',
  50,
  '********-0000-0000-0000-************',
  'trial'
);

-- ============================================================================
-- ORGANIZATION MEMBERSHIPS
-- ============================================================================

INSERT INTO organization_memberships (
  id, user_id, organization_id, role, status, invited_by,
  invited_at, accepted_at, custom_title, department
) VALUES
-- TechCorp memberships
(
  '20000000-0000-0000-0000-********0001',
  '********-0000-0000-0000-************',
  '10000000-0000-0000-0000-********0001',
  'owner',
  'active',
  NULL,
  NOW(),
  NOW(),
  'Chief Executive Officer',
  'Executive'
),
(
  '20000000-0000-0000-0000-********0002',
  '********-0000-0000-0000-********0007',
  '10000000-0000-0000-0000-********0001',
  'admin',
  'active',
  '********-0000-0000-0000-************',
  NOW() - INTERVAL '7 days',
  NOW() - INTERVAL '6 days',
  'HR Director',
  'Human Resources'
),
(
  '20000000-0000-0000-0000-********0003',
  '********-0000-0000-0000-********0009',
  '10000000-0000-0000-0000-********0001',
  'member',
  'active',
  '********-0000-0000-0000-********0007',
  NOW() - INTERVAL '14 days',
  NOW() - INTERVAL '13 days',
  'Senior Developer',
  'Engineering'
),
(
  '20000000-0000-0000-0000-********0004',
  '********-0000-0000-0000-********0011',
  '10000000-0000-0000-0000-********0001',
  'member',
  'active',
  '********-0000-0000-0000-********0007',
  NOW() - INTERVAL '10 days',
  NOW() - INTERVAL '9 days',
  'Customer Support Specialist',
  'Support'
),
-- HealthPlus memberships
(
  '20000000-0000-0000-0000-************',
  '********-0000-0000-0000-********0006',
  '10000000-0000-0000-0000-********0002',
  'owner',
  'active',
  NULL,
  NOW(),
  NOW(),
  'Founder & Medical Director',
  'Executive'
),
(
  '20000000-0000-0000-0000-********0006',
  '********-0000-0000-0000-********0008',
  '10000000-0000-0000-0000-********0002',
  'admin',
  'active',
  '********-0000-0000-0000-********0006',
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '4 days',
  'Training Coordinator',
  'Education'
),
(
  '20000000-0000-0000-0000-********0007',
  '********-0000-0000-0000-********0010',
  '10000000-0000-0000-0000-********0002',
  'member',
  'active',
  '********-0000-0000-0000-********0008',
  NOW() - INTERVAL '12 days',
  NOW() - INTERVAL '11 days',
  'Registered Nurse',
  'Clinical'
),
-- Hybrid user membership (Maria as freelancer for TechCorp)
(
  '20000000-0000-0000-0000-********0008',
  '********-0000-0000-0000-********0012',
  '10000000-0000-0000-0000-********0001',
  'member',
  'active',
  '********-0000-0000-0000-************',
  NOW() - INTERVAL '3 days',
  NOW() - INTERVAL '2 days',
  'Freelance Consultant',
  'Consulting'
);

-- ============================================================================
-- INDIVIDUAL PROFILES
-- ============================================================================

INSERT INTO individuals (
  id, user_id, contact_info, education, experience, skills,
  personal_skills, learning_preferences, industry_interests,
  profile_visibility, searchable_by_orgs, learning_status,
  career_goals, preferred_industries, job_search_status
) VALUES
-- Sarah Johnson - Full Stack Developer
(
  '30000000-0000-0000-0000-********0001',
  '********-0000-0000-0000-********0002',
  '{"phone": "******-0102", "linkedin": "linkedin.com/in/sarahjohnson", "portfolio": "sarahdev.com"}',
  ARRAY['{"degree": "BS Computer Science", "school": "MIT", "year": 2020, "gpa": 3.8}']::jsonb[],
  ARRAY['{"title": "Frontend Developer", "company": "StartupXYZ", "duration": "2020-2023", "description": "Built responsive web applications using React and TypeScript"}']::jsonb[],
  ARRAY['{"name": "JavaScript", "level": 9, "years": 4}', '{"name": "React", "level": 8, "years": 3}', '{"name": "Node.js", "level": 7, "years": 2}']::jsonb[],
  '["JavaScript", "React", "Node.js", "TypeScript", "Python", "AWS"]',
  '{"preferred_time": "evening", "learning_style": "hands-on", "pace": "fast"}',
  ARRAY['Technology', 'Fintech', 'E-commerce'],
  'public',
  true,
  'in_progress',
  '{"short_term": "Master full-stack development", "long_term": "Lead a development team", "salary_target": 120000}',
  ARRAY['Technology', 'Fintech'],
  'actively_looking'
),
-- Mike Chen - UX/UI Designer
(
  '30000000-0000-0000-0000-********0002',
  '********-0000-0000-0000-********0003',
  '{"phone": "******-0103", "behance": "behance.net/mikechen", "dribbble": "dribbble.com/mikechen"}',
  ARRAY['{"degree": "BFA Graphic Design", "school": "Art Center", "year": 2019, "gpa": 3.9}']::jsonb[],
  ARRAY['{"title": "UX Designer", "company": "DesignCo", "duration": "2019-2024", "description": "Designed user experiences for mobile and web applications"}']::jsonb[],
  ARRAY['{"name": "Figma", "level": 9, "years": 5}', '{"name": "Adobe Creative Suite", "level": 8, "years": 6}', '{"name": "User Research", "level": 7, "years": 3}']::jsonb[],
  '["Figma", "Adobe XD", "Sketch", "Prototyping", "User Research", "Design Systems"]',
  '{"preferred_time": "morning", "learning_style": "visual", "pace": "moderate"}',
  ARRAY['Design', 'Technology', 'Media'],
  'organization',
  false,
  'completed',
  '{"short_term": "Learn advanced prototyping", "long_term": "Start own design agency", "salary_target": 95000}',
  ARRAY['Design', 'Technology'],
  'open_to_opportunities'
),
-- Alex Rodriguez - Data Analyst
(
  '30000000-0000-0000-0000-********0003',
  '********-0000-0000-0000-********0004',
  '{"phone": "******-0104", "github": "github.com/alexdata", "kaggle": "kaggle.com/alexrodriguez"}',
  ARRAY['{"degree": "MS Data Science", "school": "Stanford", "year": 2021, "gpa": 3.7}']::jsonb[],
  ARRAY['{"title": "Data Analyst", "company": "DataCorp", "duration": "2021-2024", "description": "Analyzed large datasets and created predictive models"}']::jsonb[],
  ARRAY['{"name": "Python", "level": 8, "years": 3}', '{"name": "SQL", "level": 9, "years": 4}', '{"name": "Tableau", "level": 7, "years": 2}']::jsonb[],
  '["Python", "SQL", "Tableau", "Machine Learning", "Statistics", "Excel"]',
  '{"preferred_time": "afternoon", "learning_style": "analytical", "pace": "thorough"}',
  ARRAY['Data Science', 'Finance', 'Healthcare'],
  'private',
  true,
  'not_started',
  '{"short_term": "Master machine learning", "long_term": "Become Chief Data Officer", "salary_target": 110000}',
  ARRAY['Data Science', 'Finance'],
  'actively_looking'
),
-- Maria Garcia - Freelance Consultant (Hybrid User)
(
  '30000000-0000-0000-0000-********0004',
  '********-0000-0000-0000-********0012',
  '{"phone": "******-0112", "website": "mariagarcia.consulting", "linkedin": "linkedin.com/in/mariagarcia"}',
  ARRAY['{"degree": "MBA", "school": "Wharton", "year": 2018, "gpa": 3.8}', '{"degree": "BS Business", "school": "UCLA", "year": 2015, "gpa": 3.6}']::jsonb[],
  ARRAY['{"title": "Business Consultant", "company": "Independent", "duration": "2018-Present", "description": "Strategic consulting for tech startups and SMBs"}']::jsonb[],
  ARRAY['{"name": "Strategy", "level": 9, "years": 6}', '{"name": "Project Management", "level": 8, "years": 5}', '{"name": "Business Analysis", "level": 8, "years": 4}']::jsonb[],
  '["Strategy", "Project Management", "Business Analysis", "Agile", "Scrum", "Leadership"]',
  '{"preferred_time": "flexible", "learning_style": "collaborative", "pace": "fast"}',
  ARRAY['Consulting', 'Technology', 'Startups'],
  'public',
  true,
  'in_progress',
  '{"short_term": "Expand client base", "long_term": "Build consulting firm", "salary_target": 150000}',
  ARRAY['Consulting', 'Technology'],
  'freelancing'
);

-- ============================================================================
-- USER CONTEXTS
-- ============================================================================

INSERT INTO user_contexts (
  id, user_id, active_context, active_organization_id, session_data
) VALUES
-- Individual users in individual context
('40000000-0000-0000-0000-********0001', '********-0000-0000-0000-********0002', 'individual', NULL, '{"last_page": "/dashboard", "preferences": {"theme": "light"}}'),
('40000000-0000-0000-0000-********0002', '********-0000-0000-0000-********0003', 'individual', NULL, '{"last_page": "/training", "preferences": {"theme": "dark"}}'),
('40000000-0000-0000-0000-********0003', '********-0000-0000-0000-********0004', 'individual', NULL, '{"last_page": "/profile", "preferences": {"theme": "light"}}'),
-- Organization users in organization context
('40000000-0000-0000-0000-********0004', '********-0000-0000-0000-************', 'organization', '10000000-0000-0000-0000-********0001', '{"last_page": "/org/dashboard", "preferences": {"theme": "light"}}'),
('40000000-0000-0000-0000-************', '********-0000-0000-0000-********0006', 'organization', '10000000-0000-0000-0000-********0002', '{"last_page": "/org/members", "preferences": {"theme": "light"}}'),
('40000000-0000-0000-0000-********0006', '********-0000-0000-0000-********0007', 'organization', '10000000-0000-0000-0000-********0001', '{"last_page": "/org/training", "preferences": {"theme": "dark"}}'),
('40000000-0000-0000-0000-********0007', '********-0000-0000-0000-********0008', 'organization', '10000000-0000-0000-0000-********0002', '{"last_page": "/org/analytics", "preferences": {"theme": "light"}}'),
('40000000-0000-0000-0000-********0008', '********-0000-0000-0000-********0009', 'organization', '10000000-0000-0000-0000-********0001', '{"last_page": "/training", "preferences": {"theme": "light"}}'),
('40000000-0000-0000-0000-********0009', '********-0000-0000-0000-********0010', 'organization', '10000000-0000-0000-0000-********0002', '{"last_page": "/training", "preferences": {"theme": "light"}}'),
('40000000-0000-0000-0000-********0010', '********-0000-0000-0000-********0011', 'organization', '10000000-0000-0000-0000-********0001', '{"last_page": "/training", "preferences": {"theme": "dark"}}'),
-- Hybrid user in individual context (can switch to org context)
('40000000-0000-0000-0000-********0011', '********-0000-0000-0000-********0012', 'individual', NULL, '{"last_page": "/freelance-dashboard", "preferences": {"theme": "light"}}');

-- ============================================================================
-- SAMPLE TRAINING MODULES
-- ============================================================================

INSERT INTO training_modules (
  id, title, description, duration_minutes, difficulty_level,
  learning_objectives, skills_taught, status, is_featured,
  created_by, organization_id
) VALUES
-- Public modules (available to all)
(
  '50000000-0000-0000-0000-********0001',
  'Introduction to Web Development',
  'Learn the fundamentals of HTML, CSS, and JavaScript to build modern web applications',
  180,
  1,
  '["Understand HTML structure", "Style with CSS", "Add interactivity with JavaScript"]',
  '["HTML", "CSS", "JavaScript", "Web Development"]',
  'published',
  true,
  '********-1111-1111-1111-********1111',
  NULL
),
(
  '50000000-0000-0000-0000-********0002',
  'Data Analysis with Python',
  'Master data analysis techniques using Python, pandas, and visualization libraries',
  240,
  2,
  '["Import and clean data", "Perform statistical analysis", "Create visualizations"]',
  '["Python", "Pandas", "Data Analysis", "Statistics"]',
  'published',
  true,
  '********-1111-1111-1111-********1111',
  NULL
),
-- Organization-specific modules
(
  '50000000-0000-0000-0000-********0003',
  'TechCorp Development Standards',
  'Internal coding standards and best practices for TechCorp developers',
  90,
  2,
  '["Follow coding standards", "Use internal tools", "Collaborate effectively"]',
  '["Code Quality", "Team Collaboration", "Internal Tools"]',
  'published',
  false,
  '********-0000-0000-0000-************',
  '10000000-0000-0000-0000-********0001'
),
(
  '50000000-0000-0000-0000-********0004',
  'Healthcare Compliance Training',
  'Essential compliance training for healthcare professionals',
  120,
  1,
  '["Understand HIPAA requirements", "Follow safety protocols", "Maintain patient confidentiality"]',
  '["Healthcare Compliance", "HIPAA", "Patient Safety"]',
  'published',
  false,
  '********-0000-0000-0000-********0006',
  '10000000-0000-0000-0000-********0002'
);