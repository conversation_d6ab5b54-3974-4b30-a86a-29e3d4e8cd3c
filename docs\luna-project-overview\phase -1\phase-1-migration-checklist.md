# ✅ Phase 1: Foundation - Migration Checklist

## 🎯 Pre-Migration Setup

### Supabase Database Preparation
- [ ] Create new Supabase project for Luna
- [ ] Configure environment variables for new database
- [ ] Set up database connection in application
- [ ] Verify Supabase CLI access and permissions

### Backup & Safety
- [ ] Create full backup of current BPO database
- [ ] Document current schema structure
- [ ] Export existing user data for reference
- [ ] Set up rollback procedures

## 📅 Week 1: Database Foundation

### Day 1-2: Core Schema Creation
- [ ] Create new user roles enum types
- [ ] Create enhanced `users` table with individual account fields
- [ ] Create `organizations` table with multi-tenancy features
- [ ] Create `organization_memberships` bridge table
- [ ] Create `user_contexts` table for context switching

### Day 3-4: Supporting Tables & Functions
- [ ] Create `individuals` table (transformed from prospects)
- [ ] Update all enum types for Luna terminology
- [ ] Create database functions for membership management
- [ ] Create triggers for automatic timestamp updates
- [ ] Set up database indexes for performance

### Day 5: Security Implementation
- [ ] Enable Row Level Security on all tables
- [ ] Create RLS policies for users table
- [ ] Create RLS policies for organizations table
- [ ] Create RLS policies for memberships table
- [ ] Create RLS policies for individuals table
- [ ] Test policy isolation and permissions

## 📅 Week 2: Authentication & Context System

### Day 1-2: Authentication Updates
- [ ] Update authentication middleware for new user roles
- [ ] Modify user registration flow for individual-first approach
- [ ] Update login flow to handle context selection
- [ ] Create organization creation API endpoints
- [ ] Create organization invitation system

### Day 3-4: Context Switching Logic
- [ ] Implement user context management service
- [ ] Create context switching API endpoints
- [ ] Build context validation middleware
- [ ] Create session management for contexts
- [ ] Implement context-aware data fetching

### Day 5: Membership Management
- [ ] Create organization invitation flow
- [ ] Implement membership acceptance/rejection
- [ ] Build membership role management
- [ ] Create membership status tracking
- [ ] Test invitation and joining workflows

## 📅 Week 3: Portal Restructuring

### Day 1-2: Routing Updates
- [ ] Update Next.js routing structure
- [ ] Rename `/bpo/*` routes to `/org/*`
- [ ] Rename `/prospect/*` routes to `/user/*`
- [ ] Implement organization slug-based routing
- [ ] Create context-aware route protection

### Day 3-4: Portal Transformation
- [ ] Transform BPO dashboard to Organization dashboard
- [ ] Transform Prospect dashboard to User dashboard
- [ ] Update navigation components for new terminology
- [ ] Implement organization branding system
- [ ] Create context switching UI components

### Day 5: Multi-Tenant URL Handling
- [ ] Implement subdomain routing logic
- [ ] Create organization slug validation
- [ ] Set up custom domain support infrastructure
- [ ] Test multi-tenant URL resolution
- [ ] Implement SEO-friendly organization URLs

## 📅 Week 4: Integration & Testing

### Day 1-2: Database Integration
- [ ] Connect application to new Luna database
- [ ] Update all database queries for new schema
- [ ] Test data isolation between organizations
- [ ] Verify RLS policies are working correctly
- [ ] Update TypeScript types for new schema

### Day 3-4: End-to-End Testing
- [ ] Test complete user registration flow
- [ ] Test organization creation and setup
- [ ] Test invitation and membership flows
- [ ] Test context switching functionality
- [ ] Test multi-tenant data separation

### Day 5: Performance & Security
- [ ] Performance testing for context switching
- [ ] Security audit of multi-tenant setup
- [ ] Load testing for organization isolation
- [ ] Optimize database queries and indexes
- [ ] Final security vulnerability scan

## 🔧 Technical Implementation Tasks

### Environment Configuration
- [ ] Update `.env.local` with new Supabase credentials
- [ ] Configure new database connection strings
- [ ] Set up environment variables for multi-tenancy
- [ ] Update deployment configuration

### Code Updates
- [ ] Update `lib/supabase.ts` for new database
- [ ] Update `types/database.types.ts` for new schema
- [ ] Update authentication utilities in `lib/auth.ts`
- [ ] Update middleware for new routing structure
- [ ] Update all API routes for new endpoints

### Component Updates
- [ ] Update dashboard layout components
- [ ] Create context switching components
- [ ] Update navigation for new terminology
- [ ] Create organization setup components
- [ ] Update user profile components

## 🧪 Testing Checklist

### Unit Tests
- [ ] User context switching logic
- [ ] Organization membership validation
- [ ] Permission checking functions
- [ ] Data isolation utilities
- [ ] Authentication flow components

### Integration Tests
- [ ] Complete user onboarding flow
- [ ] Organization creation and setup
- [ ] Team member invitation process
- [ ] Context switching scenarios
- [ ] Multi-tenant data queries

### Security Tests
- [ ] RLS policy enforcement
- [ ] Cross-tenant data access prevention
- [ ] Permission boundary validation
- [ ] Authentication bypass attempts
- [ ] SQL injection prevention

### Performance Tests
- [ ] Context switching response times
- [ ] Database query performance
- [ ] Multi-tenant query isolation
- [ ] Concurrent user handling
- [ ] Memory usage optimization

## 📊 Validation Criteria

### Functional Requirements
- [ ] Users can create individual accounts
- [ ] Users can create organizations
- [ ] Users can invite others to organizations
- [ ] Users can switch between individual and org contexts
- [ ] Organizations are completely isolated
- [ ] All existing features work in new structure

### Performance Requirements
- [ ] Context switching < 500ms
- [ ] Database queries < 200ms average
- [ ] Page load times < 2 seconds
- [ ] Support for 100+ concurrent users
- [ ] Memory usage within acceptable limits

### Security Requirements
- [ ] Zero cross-tenant data leakage
- [ ] All RLS policies enforced
- [ ] Authentication flows secure
- [ ] No privilege escalation possible
- [ ] Audit trails for all actions

## 🚨 Risk Mitigation

### Rollback Plan
- [ ] Database rollback scripts prepared
- [ ] Code rollback procedures documented
- [ ] Environment variable rollback plan
- [ ] User communication plan for issues
- [ ] Emergency contact procedures

### Monitoring
- [ ] Set up error tracking for new features
- [ ] Monitor database performance metrics
- [ ] Track user adoption of new features
- [ ] Monitor security events and anomalies
- [ ] Set up alerts for critical issues

## ✅ Phase 1 Completion Criteria

### Technical Completion
- [ ] All new database tables created and tested
- [ ] Authentication system updated and working
- [ ] Context switching fully functional
- [ ] Multi-tenant routing operational
- [ ] All tests passing with >95% coverage

### User Experience Completion
- [ ] Smooth user onboarding flow
- [ ] Intuitive context switching
- [ ] Clear organization setup process
- [ ] Responsive and fast interface
- [ ] No breaking changes for existing users

### Documentation Completion
- [ ] Updated API documentation
- [ ] User guide for new features
- [ ] Technical documentation updated
- [ ] Deployment procedures documented
- [ ] Troubleshooting guide created

---

*Upon completion of Phase 1, proceed to [Phase 2: Core Features](./phase-2-core-features.md)*
