import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    const debug = {
      user: session.user.id,
      tables: {},
      errors: []
    };

    // Check interviews table
    try {
      const { data: interviews, error: interviewsError } = await supabase
        .from('interviews')
        .select('*')
        .limit(5);
      
      debug.tables.interviews = {
        count: interviews?.length || 0,
        sample: interviews || [],
        error: interviewsError
      };
    } catch (error) {
      debug.errors.push({ table: 'interviews', error: error.message });
    }

    // Check applications table
    try {
      const { data: applications, error: applicationsError } = await supabase
        .from('applications')
        .select('*')
        .limit(5);
      
      debug.tables.applications = {
        count: applications?.length || 0,
        sample: applications || [],
        error: applicationsError
      };
    } catch (error) {
      debug.errors.push({ table: 'applications', error: error.message });
    }

    // Check prospects table
    try {
      const { data: prospects, error: prospectsError } = await supabase
        .from('prospects')
        .select('*')
        .limit(5);
      
      debug.tables.prospects = {
        count: prospects?.length || 0,
        sample: prospects || [],
        error: prospectsError
      };
    } catch (error) {
      debug.errors.push({ table: 'prospects', error: error.message });
    }

    // Check job_postings table
    try {
      const { data: jobPostings, error: jobPostingsError } = await supabase
        .from('job_postings')
        .select('*')
        .limit(5);
      
      debug.tables.job_postings = {
        count: jobPostings?.length || 0,
        sample: jobPostings || [],
        error: jobPostingsError
      };
    } catch (error) {
      debug.errors.push({ table: 'job_postings', error: error.message });
    }

    // Check bpo_teams table
    try {
      const { data: bpoTeams, error: bpoTeamsError } = await supabase
        .from('bpo_teams')
        .select('*')
        .eq('user_id', session.user.id);

      debug.tables.bpo_teams = {
        count: bpoTeams?.length || 0,
        sample: bpoTeams || [],
        error: bpoTeamsError
      };
    } catch (error) {
      debug.errors.push({ table: 'bpo_teams', error: error.message });
    }

    // Check bpo_schedules table
    try {
      const { data: bpoSchedules, error: bpoSchedulesError } = await supabase
        .from('bpo_schedules')
        .select('*')
        .eq('user_id', session.user.id)
        .limit(5);

      debug.tables.bpo_schedules = {
        count: bpoSchedules?.length || 0,
        sample: bpoSchedules || [],
        error: bpoSchedulesError
      };
    } catch (error) {
      debug.errors.push({ table: 'bpo_schedules', error: error.message });
    }

    return NextResponse.json({
      success: true,
      debug
    });

  } catch (error) {
    console.error('Error in debug API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
