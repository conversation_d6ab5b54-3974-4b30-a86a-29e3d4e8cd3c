const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function updateUserName() {
  try {
    // Update the user's full_name
    const { data, error } = await supabase
      .from('users')
      .update({ 
        full_name: 'Jennine Bermudez' // You can change this to the desired name
      })
      .eq('email', '<EMAIL>')
      .select();

    if (error) {
      console.error('Error updating user:', error);
      return;
    }

    console.log('User updated successfully:', data);
  } catch (err) {
    console.error('Unexpected error:', err);
  }
}

updateUserName();
