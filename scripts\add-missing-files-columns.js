const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function addMissingColumns() {
  console.log('🚀 Adding missing columns to files table...\n');

  try {
    // List of columns to add with their SQL definitions
    const columnsToAdd = [
      {
        name: 'assessment_id',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS assessment_id UUID;'
      },
      {
        name: 'file_type',
        sql: "ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_type VARCHAR(50) NOT NULL DEFAULT 'document';"
      },
      {
        name: 'file_category', 
        sql: "ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_category VARCHAR(50) NOT NULL DEFAULT 'uploaded';"
      },
      {
        name: 'title',
        sql: "ALTER TABLE public.files ADD COLUMN IF NOT EXISTS title VARCHAR(255) NOT NULL DEFAULT 'Untitled File';"
      },
      {
        name: 'description',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS description TEXT;'
      },
      {
        name: 'file_url',
        sql: "ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_url TEXT NOT NULL DEFAULT '';"
      },
      {
        name: 'original_filename',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS original_filename VARCHAR(255);'
      },
      {
        name: 'file_size',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS file_size INTEGER DEFAULT 0;'
      },
      {
        name: 'mime_type',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS mime_type VARCHAR(100);'
      },
      {
        name: 'issued_at',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS issued_at TIMESTAMPTZ DEFAULT NOW();'
      },
      {
        name: 'expires_at',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS expires_at TIMESTAMPTZ;'
      },
      {
        name: 'is_verified',
        sql: 'ALTER TABLE public.files ADD COLUMN IF NOT EXISTS is_verified BOOLEAN DEFAULT TRUE;'
      },
      {
        name: 'metadata',
        sql: "ALTER TABLE public.files ADD COLUMN IF NOT EXISTS metadata JSONB DEFAULT '{}';"
      }
    ];

    // Add each column
    for (const column of columnsToAdd) {
      console.log(`📝 Adding column: ${column.name}`);
      
      // Use a raw SQL query through Supabase
      const { error } = await supabase.rpc('exec_sql', { sql: column.sql });
      
      if (error && !error.message.includes('already exists')) {
        console.error(`❌ Error adding ${column.name}:`, error.message);
      } else {
        console.log(`✅ Column ${column.name} added successfully`);
      }
    }

    // Add constraints
    console.log('\n🔗 Adding constraints...');
    
    const constraints = [
      "ALTER TABLE public.files ADD CONSTRAINT IF NOT EXISTS chk_files_type_valid CHECK (file_type IN ('certificate', 'document', 'training_certificate'));",
      "ALTER TABLE public.files ADD CONSTRAINT IF NOT EXISTS chk_files_category_valid CHECK (file_category IN ('uploaded', 'system_generated'));",
      "ALTER TABLE public.files ADD CONSTRAINT IF NOT EXISTS fk_files_assessment_id FOREIGN KEY (assessment_id) REFERENCES public.assessments(id) ON DELETE SET NULL ON UPDATE CASCADE;"
    ];

    for (const constraint of constraints) {
      const { error } = await supabase.rpc('exec_sql', { sql: constraint });
      if (error && !error.message.includes('already exists')) {
        console.error('❌ Constraint error:', error.message);
      } else {
        console.log('✅ Constraint added');
      }
    }

    // Add indexes
    console.log('\n📊 Adding indexes...');
    
    const indexes = [
      'CREATE INDEX IF NOT EXISTS idx_files_type ON public.files(file_type);',
      'CREATE INDEX IF NOT EXISTS idx_files_category ON public.files(file_category);',
      'CREATE INDEX IF NOT EXISTS idx_files_issued_at ON public.files(issued_at);',
      'CREATE INDEX IF NOT EXISTS idx_files_assessment_id ON public.files(assessment_id);'
    ];

    for (const index of indexes) {
      const { error } = await supabase.rpc('exec_sql', { sql: index });
      if (error && !error.message.includes('already exists')) {
        console.error('❌ Index error:', error.message);
      } else {
        console.log('✅ Index added');
      }
    }

    console.log('\n🎉 Files table migration completed successfully!');
    console.log('📝 All missing columns have been added to the files table.');

  } catch (error) {
    console.error('❌ Migration failed:', error);
  }
}

// Run the migration
addMissingColumns();
