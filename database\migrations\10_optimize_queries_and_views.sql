-- Query Optimization and Views Migration
-- Creates optimized views and functions to reduce N+1 queries and improve performance

-- =============================================================================
-- MATERIALIZED VIEWS FOR PERFORMANCE
-- =============================================================================

-- Materialized view for prospect training progress summary
CREATE MATERIALIZED VIEW IF NOT EXISTS public.mv_prospect_training_progress AS
SELECT 
    p.id AS prospect_id,
    p.user_id,
    p.training_status,
    COUNT(DISTINCT tm.id) AS total_modules,
    COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN tm.id END) AS completed_modules,
    COUNT(DISTINCT l.id) AS total_lessons,
    COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN l.id END) AS completed_lessons,
    COUNT(DISTINCT a.id) AS total_activities,
    COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN a.id END) AS completed_activities,
    ROUND(
        CASE 
            WHEN COUNT(DISTINCT a.id) > 0 
            THEN (COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN a.id END) * 100.0) / COUNT(DISTINCT a.id)
            ELSE 0 
        END, 2
    ) AS overall_progress_percentage,
    MAX(pr.completed_at) AS last_activity_completed_at,
    SUM(pr.time_spent_seconds) AS total_time_spent_seconds
FROM public.prospects p
LEFT JOIN public.training_modules tm ON tm.status = 'published'
LEFT JOIN public.lessons l ON l.module_id = tm.id
LEFT JOIN public.activities a ON a.lesson_id = l.id
LEFT JOIN public.progress_records pr ON pr.prospect_id = p.id AND pr.activity_id = a.id
GROUP BY p.id, p.user_id, p.training_status;

-- Create unique index on materialized view
CREATE UNIQUE INDEX IF NOT EXISTS idx_mv_prospect_training_progress_prospect_id 
ON public.mv_prospect_training_progress(prospect_id);

-- =============================================================================
-- OPTIMIZED VIEWS FOR COMMON QUERIES
-- =============================================================================

-- View for BPO dashboard statistics
CREATE OR REPLACE VIEW public.v_bpo_dashboard_stats AS
SELECT 
    b.id AS bpo_id,
    b.name AS bpo_name,
    COUNT(DISTINCT bt.user_id) AS team_member_count,
    COUNT(DISTINCT jp.id) AS total_job_postings,
    COUNT(DISTINCT CASE WHEN jp.status = 'published' THEN jp.id END) AS active_job_postings,
    COUNT(DISTINCT a.id) AS total_applications,
    COUNT(DISTINCT CASE WHEN a.status = 'submitted' THEN a.id END) AS pending_applications,
    COUNT(DISTINCT CASE WHEN a.status = 'reviewing' THEN a.id END) AS reviewing_applications,
    COUNT(DISTINCT CASE WHEN a.status = 'accepted' THEN a.id END) AS accepted_applications,
    COUNT(DISTINCT i.id) AS total_interviews,
    COUNT(DISTINCT CASE WHEN i.status = 'scheduled' THEN i.id END) AS scheduled_interviews
FROM public.bpos b
LEFT JOIN public.bpo_teams bt ON bt.bpo_id = b.id
LEFT JOIN public.job_postings jp ON jp.bpo_id = b.id
LEFT JOIN public.applications a ON a.job_id = jp.id
LEFT JOIN public.interviews i ON i.application_id = a.id
GROUP BY b.id, b.name;

-- View for prospect profile with training progress
CREATE OR REPLACE VIEW public.v_prospect_profiles AS
SELECT 
    p.id,
    p.user_id,
    u.full_name,
    u.email,
    p.contact_info,
    p.education,
    p.experience,
    p.skills,
    p.profile_visibility,
    p.training_status,
    p.profile_image,
    p.intro_video_url,
    p.resume_url,
    mtp.total_modules,
    mtp.completed_modules,
    mtp.overall_progress_percentage,
    mtp.last_activity_completed_at,
    COUNT(DISTINCT c.id) AS certificate_count,
    p.created_at,
    p.updated_at
FROM public.prospects p
JOIN public.users u ON u.id = p.user_id
LEFT JOIN public.mv_prospect_training_progress mtp ON mtp.prospect_id = p.id
LEFT JOIN public.certificates c ON c.prospect_id = p.id
GROUP BY 
    p.id, p.user_id, u.full_name, u.email, p.contact_info, p.education, 
    p.experience, p.skills, p.profile_visibility, p.training_status, 
    p.profile_image, p.intro_video_url, p.resume_url, mtp.total_modules, 
    mtp.completed_modules, mtp.overall_progress_percentage, 
    mtp.last_activity_completed_at, p.created_at, p.updated_at;

-- View for training module details with progress
CREATE OR REPLACE VIEW public.v_training_module_details AS
SELECT 
    tm.id,
    tm.title,
    tm.description,
    tm.cover_image_url,
    tm.duration_minutes,
    tm.required_order,
    tm.status,
    tm.requires_assessment,
    COUNT(DISTINCT l.id) AS lesson_count,
    COUNT(DISTINCT a.id) AS activity_count,
    COUNT(DISTINCT ma.id) AS assessment_count,
    SUM(l.duration_minutes) AS total_lesson_duration,
    tm.created_at,
    tm.updated_at
FROM public.training_modules tm
LEFT JOIN public.lessons l ON l.module_id = tm.id
LEFT JOIN public.activities a ON a.lesson_id = l.id
LEFT JOIN public.module_assessments ma ON ma.module_id = tm.id
GROUP BY 
    tm.id, tm.title, tm.description, tm.cover_image_url, 
    tm.duration_minutes, tm.required_order, tm.status, 
    tm.requires_assessment, tm.created_at, tm.updated_at;

-- =============================================================================
-- OPTIMIZED FUNCTIONS FOR COMMON OPERATIONS
-- =============================================================================

-- Function to get prospect training progress efficiently
CREATE OR REPLACE FUNCTION public.get_prospect_training_progress(prospect_uuid UUID)
RETURNS TABLE (
    module_id UUID,
    module_title TEXT,
    module_order INTEGER,
    total_lessons BIGINT,
    completed_lessons BIGINT,
    total_activities BIGINT,
    completed_activities BIGINT,
    progress_percentage NUMERIC,
    is_completed BOOLEAN,
    last_activity_at TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        tm.id AS module_id,
        tm.title AS module_title,
        tm.required_order AS module_order,
        COUNT(DISTINCT l.id) AS total_lessons,
        COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN l.id END) AS completed_lessons,
        COUNT(DISTINCT a.id) AS total_activities,
        COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN a.id END) AS completed_activities,
        ROUND(
            CASE 
                WHEN COUNT(DISTINCT a.id) > 0 
                THEN (COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN a.id END) * 100.0) / COUNT(DISTINCT a.id)
                ELSE 0 
            END, 2
        ) AS progress_percentage,
        (COUNT(DISTINCT a.id) = COUNT(DISTINCT CASE WHEN pr.status = 'completed' THEN a.id END) 
         AND COUNT(DISTINCT a.id) > 0) AS is_completed,
        MAX(pr.completed_at) AS last_activity_at
    FROM public.training_modules tm
    LEFT JOIN public.lessons l ON l.module_id = tm.id
    LEFT JOIN public.activities a ON a.lesson_id = l.id
    LEFT JOIN public.progress_records pr ON pr.prospect_id = prospect_uuid AND pr.activity_id = a.id
    WHERE tm.status = 'published'
    GROUP BY tm.id, tm.title, tm.required_order
    ORDER BY tm.required_order;
END;
$$;

-- Function to get BPO team members efficiently
CREATE OR REPLACE FUNCTION public.get_bpo_team_members(bpo_uuid UUID)
RETURNS TABLE (
    user_id UUID,
    full_name TEXT,
    email TEXT,
    role bpo_team_role,
    permissions JSONB,
    joined_at TIMESTAMPTZ,
    last_login TIMESTAMPTZ
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        u.id AS user_id,
        u.full_name,
        u.email,
        bt.role,
        bt.permissions,
        bt.accepted_at AS joined_at,
        u.last_login
    FROM public.bpo_teams bt
    JOIN public.users u ON u.id = bt.user_id
    WHERE bt.bpo_id = bpo_uuid
        AND bt.accepted_at IS NOT NULL
    ORDER BY bt.role, u.full_name;
END;
$$;

-- Function to get job applications with candidate details
CREATE OR REPLACE FUNCTION public.get_job_applications_with_candidates(job_uuid UUID)
RETURNS TABLE (
    application_id UUID,
    prospect_id UUID,
    candidate_name TEXT,
    candidate_email TEXT,
    application_status application_status,
    submitted_at TIMESTAMPTZ,
    resume_url TEXT,
    cover_letter TEXT,
    expected_salary_min INTEGER,
    expected_salary_max INTEGER,
    training_progress_percentage NUMERIC,
    certificate_count BIGINT
) 
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        a.id AS application_id,
        p.id AS prospect_id,
        u.full_name AS candidate_name,
        u.email AS candidate_email,
        a.status AS application_status,
        a.submitted_at,
        a.resume_url,
        a.cover_letter,
        a.expected_salary_min,
        a.expected_salary_max,
        COALESCE(mtp.overall_progress_percentage, 0) AS training_progress_percentage,
        COUNT(DISTINCT c.id) AS certificate_count
    FROM public.applications a
    JOIN public.prospects p ON p.id = a.prospect_id
    JOIN public.users u ON u.id = p.user_id
    LEFT JOIN public.mv_prospect_training_progress mtp ON mtp.prospect_id = p.id
    LEFT JOIN public.certificates c ON c.prospect_id = p.id
    WHERE a.job_id = job_uuid
    GROUP BY 
        a.id, p.id, u.full_name, u.email, a.status, a.submitted_at,
        a.resume_url, a.cover_letter, a.expected_salary_min, 
        a.expected_salary_max, mtp.overall_progress_percentage
    ORDER BY a.submitted_at DESC;
END;
$$;

-- =============================================================================
-- REFRESH FUNCTION FOR MATERIALIZED VIEWS
-- =============================================================================

-- Function to refresh materialized views
CREATE OR REPLACE FUNCTION public.refresh_training_progress_cache()
RETURNS VOID
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    REFRESH MATERIALIZED VIEW CONCURRENTLY public.mv_prospect_training_progress;
END;
$$;

-- =============================================================================
-- COMPOSITE INDEXES FOR COMPLEX QUERIES
-- =============================================================================

-- Composite index for prospect training queries
CREATE INDEX IF NOT EXISTS idx_progress_records_prospect_status_completed 
ON public.progress_records(prospect_id, status, completed_at) 
WHERE status = 'completed';

-- Composite index for BPO job and application queries
CREATE INDEX IF NOT EXISTS idx_applications_job_status_submitted 
ON public.applications(job_id, status, submitted_at);

-- Composite index for module lesson ordering
CREATE INDEX IF NOT EXISTS idx_lessons_module_order_active 
ON public.lessons(module_id, order_index, id);

-- Composite index for activity lesson ordering
CREATE INDEX IF NOT EXISTS idx_activities_lesson_order_active 
ON public.activities(lesson_id, order_index, id);

-- Composite index for user role and status queries
CREATE INDEX IF NOT EXISTS idx_users_role_status_active 
ON public.users(role, status, id) 
WHERE status = 'active';

-- =============================================================================
-- PERFORMANCE MONITORING VIEWS
-- =============================================================================

-- View to monitor slow queries (requires pg_stat_statements extension)
CREATE OR REPLACE VIEW public.v_slow_queries AS
SELECT 
    query,
    calls,
    total_time,
    mean_time,
    rows,
    100.0 * shared_blks_hit / nullif(shared_blks_hit + shared_blks_read, 0) AS hit_percent
FROM pg_stat_statements 
WHERE query NOT LIKE '%pg_stat_statements%'
ORDER BY mean_time DESC
LIMIT 20;

-- =============================================================================
-- VERIFICATION AND MAINTENANCE
-- =============================================================================

-- Create a function to analyze table statistics
CREATE OR REPLACE FUNCTION public.analyze_table_stats()
RETURNS TABLE (
    table_name TEXT,
    row_count BIGINT,
    table_size TEXT,
    index_size TEXT,
    total_size TEXT
)
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
    RETURN QUERY
    SELECT 
        schemaname||'.'||tablename AS table_name,
        n_tup_ins - n_tup_del AS row_count,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) AS table_size,
        pg_size_pretty(pg_indexes_size(schemaname||'.'||tablename)) AS index_size,
        pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename) + pg_indexes_size(schemaname||'.'||tablename)) AS total_size
    FROM pg_stat_user_tables 
    WHERE schemaname = 'public'
    ORDER BY pg_total_relation_size(schemaname||'.'||tablename) DESC;
END;
$$;
