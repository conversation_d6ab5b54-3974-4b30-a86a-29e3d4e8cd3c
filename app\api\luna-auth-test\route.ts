import { NextRequest, NextResponse } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth';

export async function GET(request: NextRequest) {
  try {
    console.log('Luna Auth Test - Starting authentication check...');
    
    const authResult = await getAuthenticatedUser();
    
    console.log('Luna Auth Test - Auth result:', {
      hasUser: !!authResult.user,
      error: authResult.error,
      status: authResult.status
    });

    if (authResult.user) {
      console.log('Luna Auth Test - User details:', {
        id: authResult.user.id,
        email: authResult.user.email,
        role: authResult.user.role,
        organizationCount: authResult.user.organizationMemberships.length,
        currentContext: authResult.user.currentContext
      });
    }

    return NextResponse.json({
      success: !!authResult.user,
      user: authResult.user,
      error: authResult.error,
      status: authResult.status,
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('Luna Auth Test - Unexpected error:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during authentication test',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
