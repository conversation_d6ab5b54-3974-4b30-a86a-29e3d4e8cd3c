'use client'

import React, { useState, useEffect, useRef } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Phone,
  PhoneOff,
  Mic,
  MicOff,
  Volume2,
  VolumeX,
  RotateCcw,
  Timer,
  ArrowLeft,
  FileText,
  CheckCircle,
  AlertCircle,
  TrendingUp,
  Star,
  Target,
  Clock,
  X
} from 'lucide-react'
import { cn } from '@/lib/utils'

interface ScriptElement {
  type: 'speech' | 'direction' | 'pause' | 'note'
  content: string
  emphasis?: boolean
}

interface Script {
  id: string
  name: string
  elements: ScriptElement[]
}

interface ProspectInfo {
  name: string
  difficulty: 'Easy' | 'Medium' | 'Hard'
  avatar: string
  background: string
  personality: string
  objections: string[]
}

export default function ColdCallTrainingPage() {
  const [isCallActive, setIsCallActive] = useState(false)
  const [isMuted, setIsMuted] = useState(false)
  const [isVolumeOn, setIsVolumeOn] = useState(true)
  const [callDuration, setCallDuration] = useState(0)
  const [showReport, setShowReport] = useState(false)
  const [selectedDifficulty, setSelectedDifficulty] = useState<'Easy' | 'Medium' | 'Hard'>('Easy')
  const timerRef = useRef<NodeJS.Timeout | null>(null)

  // Prospect information based on difficulty
  const prospects: Record<string, ProspectInfo> = {
    Easy: {
      name: 'Benjamin Gray',
      difficulty: 'Easy',
      avatar: 'from-pink-400 to-pink-600',
      background: 'Small business owner, friendly and open to new ideas',
      personality: 'Polite, asks clarifying questions, generally receptive',
      objections: ['Budget concerns', 'Need to think about it']
    },
    Medium: {
      name: 'Sarah Mitchell',
      difficulty: 'Medium',
      avatar: 'from-blue-400 to-blue-600',
      background: 'Mid-level manager, cautious about new vendors',
      personality: 'Professional but skeptical, asks detailed questions',
      objections: ['Already have a solution', 'Need approval from boss', 'Timing concerns']
    },
    Hard: {
      name: 'Robert Chen',
      difficulty: 'Hard',
      avatar: 'from-red-400 to-red-600',
      background: 'Senior executive, very busy and direct',
      personality: 'Impatient, challenging, tests your knowledge',
      objections: ['Not interested', 'Too expensive', 'Waste of time', 'Competitor preference']
    }
  }

  // Rich text script content - managed by admin
  const scriptElements: ScriptElement[] = [
    { type: 'speech', content: 'Hello [Client Name], my name is [Your Name] and I\'m calling from [Your Business].', emphasis: true },

    { type: 'pause', content: 'Pause for acknowledgment' },

    { type: 'speech', content: 'How are you doing today?' },
    { type: 'direction', content: 'Wait for response - be genuine and listen to their answer' },

    { type: 'speech', content: 'The reason I\'m calling is that [Your Company] helps businesses like yours [specific benefit]. I\'d like to share how we\'ve helped similar companies achieve [mention specific result].' },

    { type: 'pause', content: 'Let them process the information' },

    { type: 'speech', content: 'Do you have 2-3 minutes for me to share how this might benefit your business?' },
    { type: 'direction', content: 'If YES: Continue. If NO: Ask when would be better' },

    { type: 'speech', content: '[Your Company] specializes in [briefly describe what your company does]. We\'ve helped businesses like [mention a relevant company or industry] to [mention specific benefit or outcome].' },

    { type: 'speech', content: 'What challenges are you currently facing with [relevant area]?' },
    { type: 'direction', content: 'Listen actively - this is where you learn about their needs' },

    { type: 'speech', content: 'Based on what you\'ve shared, I think we could definitely help. Would you be open to a brief meeting to discuss this further?' },

    { type: 'note', content: 'Remember: Focus on benefits, not features. Be prepared to handle objections confidently but not pushy.' }
  ]

  const formatTime = (seconds: number) => {
    const mins = Math.floor(seconds / 60)
    const secs = seconds % 60
    return `${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`
  }

  const renderScriptElement = (element: ScriptElement, index: number) => {
    switch (element.type) {
      case 'speech':
        return (
          <p key={index} className={cn(
            "text-sm leading-relaxed",
            element.emphasis && "font-semibold"
          )}>
            {element.content}
          </p>
        )
      case 'direction':
        return (
          <p key={index} className="text-sm text-muted-foreground italic my-2 pl-4 border-l-2 border-muted">
            {element.content}
          </p>
        )
      case 'pause':
        return (
          <div key={index} className="my-4 text-center">
            <div className="text-xs text-muted-foreground bg-muted/50 rounded-full px-3 py-1 inline-block">
              {element.content}
            </div>
          </div>
        )
      case 'note':
        return (
          <div key={index} className="my-3 p-3 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
            <p className="text-sm text-blue-800 dark:text-blue-200 font-medium">
              💡 {element.content}
            </p>
          </div>
        )
      default:
        return null
    }
  }

  const handleStartCall = () => {
    setIsCallActive(true)
    setCallDuration(0)
    // Start timer simulation
    timerRef.current = setInterval(() => {
      setCallDuration(prev => prev + 1)
    }, 1000)
  }

  const handleEndCall = () => {
    setIsCallActive(false)
    if (timerRef.current) {
      clearInterval(timerRef.current)
      timerRef.current = null
    }
    // Show report after call ends
    setShowReport(true)
  }

  // Cleanup timer on unmount
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        clearInterval(timerRef.current)
      }
    }
  }, [])

  return (
    <div className="p-4 sm:p-6 lg:p-8">
      <div className="max-w-7xl mx-auto space-y-6">
        {/* Header with Back Button */}
        <div className="flex items-center gap-4">
          <Button variant="outline" size="sm" asChild>
            <Link href="/prospect/role-call-training">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Training Types
            </Link>
          </Button>
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Cold Call for New Business</h1>
            <p className="text-muted-foreground">Practice making effective cold calls to potential clients</p>
          </div>
        </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Left Side - Script Display */}
        <div className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-2">
                  <FileText className="h-5 w-5 text-muted-foreground" />
                  <CardTitle>Call Script</CardTitle>
                </div>
                {/* Difficulty Selector */}
                <Select value={selectedDifficulty} onValueChange={(value: 'Easy' | 'Medium' | 'Hard') => setSelectedDifficulty(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Easy">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-green-500 rounded-full"></div>
                        Easy
                      </div>
                    </SelectItem>
                    <SelectItem value="Medium">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-yellow-500 rounded-full"></div>
                        Medium
                      </div>
                    </SelectItem>
                    <SelectItem value="Hard">
                      <div className="flex items-center gap-2">
                        <div className="w-2 h-2 bg-red-500 rounded-full"></div>
                        Hard
                      </div>
                    </SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="bg-muted/50 rounded-lg p-6 border">
                <h4 className="font-medium mb-4 text-sm text-muted-foreground">COLD CALL SCRIPT - NEW BUSINESS</h4>
                <div className="space-y-4">
                  {scriptElements.map((element, index) => renderScriptElement(element, index))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Right Side - Call Simulation */}
        <div className="space-y-6">
          <Card>
            <CardContent className="p-8">
              {/* Prospect Info */}
              <div className="text-center mb-8">
                <h3 className="text-xl font-semibold mb-2">{prospects[selectedDifficulty].name}</h3>
                <Badge
                  variant="secondary"
                  className={cn(
                    "mb-4",
                    selectedDifficulty === 'Easy' && "bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300",
                    selectedDifficulty === 'Medium' && "bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300",
                    selectedDifficulty === 'Hard' && "bg-red-100 text-red-700 dark:bg-red-900/30 dark:text-red-300"
                  )}
                >
                  {selectedDifficulty} Level
                </Badge>

                {/* Prospect Background */}
                <div className="mb-6 p-3 bg-muted/50 rounded-lg text-sm">
                  <p className="text-muted-foreground mb-2">{prospects[selectedDifficulty].background}</p>
                  <p className="text-xs text-muted-foreground italic">{prospects[selectedDifficulty].personality}</p>
                </div>

                {/* Avatar */}
                <div className="flex justify-center mb-6">
                  <Avatar className={cn("w-32 h-32 bg-gradient-to-br", prospects[selectedDifficulty].avatar)}>
                    <AvatarFallback className={cn("text-4xl font-bold text-white bg-gradient-to-br", prospects[selectedDifficulty].avatar)}>
                      {prospects[selectedDifficulty].name.split(' ').map(n => n[0]).join('')}
                    </AvatarFallback>
                  </Avatar>
                </div>

                {/* Call Controls */}
                <div className="flex justify-center gap-4 mb-6">
                  <Button
                    variant="outline"
                    size="lg"
                    className={cn(
                      "rounded-full w-16 h-16 border-2 transition-all duration-200",
                      isCallActive 
                        ? "opacity-50 cursor-not-allowed" 
                        : "bg-green-600 border-green-500 hover:bg-green-700 hover:scale-105 text-white"
                    )}
                    onClick={handleStartCall}
                    disabled={isCallActive}
                  >
                    <Phone className="w-6 h-6" />
                  </Button>
                  <Button
                    variant="outline"
                    size="lg"
                    className={cn(
                      "rounded-full w-16 h-16 border-2 transition-all duration-200",
                      !isCallActive
                        ? "opacity-50 cursor-not-allowed"
                        : "bg-red-600 border-red-500 hover:bg-red-700 hover:scale-105 text-white"
                    )}
                    onClick={handleEndCall}
                    disabled={!isCallActive}
                  >
                    <PhoneOff className="w-6 h-6" />
                  </Button>
                </div>

                {/* Call Status */}
                {isCallActive && (
                  <div className="flex items-center justify-center gap-2 mb-4">
                    <div className="flex items-center gap-2">
                      <div className="w-2 h-2 bg-green-500 rounded-full animate-pulse"></div>
                      <Timer className="w-4 h-4 text-green-500" />
                      <span className="text-green-500 font-mono text-lg">{formatTime(callDuration)}</span>
                    </div>
                  </div>
                )}

                {/* Additional Controls */}
                <div className="flex justify-center gap-2 mb-6">
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "hover:bg-muted",
                      isMuted && "text-red-500"
                    )}
                    onClick={() => setIsMuted(!isMuted)}
                  >
                    {isMuted ? <MicOff className="w-4 h-4" /> : <Mic className="w-4 h-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className={cn(
                      "hover:bg-muted",
                      !isVolumeOn && "text-red-500"
                    )}
                    onClick={() => setIsVolumeOn(!isVolumeOn)}
                  >
                    {isVolumeOn ? <Volume2 className="w-4 h-4" /> : <VolumeX className="w-4 h-4" />}
                  </Button>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="hover:bg-muted"
                    onClick={() => {
                      setCallDuration(0)
                      setIsCallActive(false)
                    }}
                  >
                    <RotateCcw className="w-4 h-4" />
                  </Button>
                </div>
              </div>

              {/* Instructions Panel */}
              <Card className="bg-blue-50 dark:bg-blue-950/20 border-blue-200 dark:border-blue-800">
                <CardContent className="p-4">
                  <div className="flex items-center gap-2 mb-3">
                    <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                    <span className="text-sm text-blue-600 dark:text-blue-400 font-medium">Instructions</span>
                  </div>
                  <div className="bg-blue-100 dark:bg-blue-900/30 border border-blue-200 dark:border-blue-700 rounded-lg p-4 mb-4">
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      You are calling potential clients to introduce your business and services.
                      Be professional, confident, and listen actively to their responses.
                    </p>
                  </div>

                  {/* Potential Objections */}
                  <div className="bg-orange-100 dark:bg-orange-900/30 border border-orange-200 dark:border-orange-700 rounded-lg p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <div className="w-2 h-2 bg-orange-500 rounded-full"></div>
                      <span className="text-sm text-orange-700 dark:text-orange-300 font-medium">Potential Objections</span>
                    </div>
                    <ul className="text-sm text-orange-800 dark:text-orange-200 space-y-1">
                      {prospects[selectedDifficulty].objections.map((objection, index) => (
                        <li key={index}>• {objection}</li>
                      ))}
                    </ul>
                  </div>
                </CardContent>
              </Card>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Call Completion Report Modal */}
      {showReport && (
        <div className="fixed inset-0 bg-black/50 flex items-center justify-center p-4 z-50">
          <Card className="w-full max-w-2xl max-h-[90vh] overflow-y-auto">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <div className="p-2 bg-green-100 dark:bg-green-900/30 rounded-lg">
                    <CheckCircle className="h-6 w-6 text-green-600 dark:text-green-400" />
                  </div>
                  <div>
                    <CardTitle className="text-xl">Call Completed!</CardTitle>
                    <p className="text-sm text-muted-foreground">Here's your performance report</p>
                  </div>
                </div>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => {
                    setShowReport(false)
                    setCallDuration(0)
                  }}
                >
                  <X className="h-4 w-4" />
                </Button>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Call Statistics */}
              <div className="grid grid-cols-3 gap-4">
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Clock className="h-5 w-5 mx-auto mb-2 text-blue-600" />
                  <p className="text-2xl font-bold">{formatTime(callDuration)}</p>
                  <p className="text-xs text-muted-foreground">Call Duration</p>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Star className="h-5 w-5 mx-auto mb-2 text-yellow-600" />
                  <p className="text-2xl font-bold">7.5</p>
                  <p className="text-xs text-muted-foreground">Overall Score</p>
                </div>
                <div className="text-center p-4 bg-muted/50 rounded-lg">
                  <Target className="h-5 w-5 mx-auto mb-2 text-green-600" />
                  <p className="text-2xl font-bold">85%</p>
                  <p className="text-xs text-muted-foreground">Script Adherence</p>
                </div>
              </div>

              {/* Performance Areas */}
              <div className="space-y-4">
                <h3 className="font-semibold flex items-center gap-2">
                  <TrendingUp className="h-4 w-4" />
                  Performance Analysis
                </h3>

                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Professional Tone</span>
                    </div>
                    <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">Excellent</Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-green-50 dark:bg-green-950/20 border border-green-200 dark:border-green-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Clear Communication</span>
                    </div>
                    <Badge className="bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-300">Good</Badge>
                  </div>

                  <div className="flex items-center justify-between p-3 bg-yellow-50 dark:bg-yellow-950/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
                    <div className="flex items-center gap-2">
                      <AlertCircle className="h-4 w-4 text-yellow-600" />
                      <span className="text-sm font-medium">Pace & Timing</span>
                    </div>
                    <Badge className="bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-300">Needs Work</Badge>
                  </div>
                </div>
              </div>

              {/* Improvement Suggestions */}
              <div className="space-y-4">
                <h3 className="font-semibold">Areas for Improvement</h3>
                <div className="space-y-3">
                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Speaking Pace</h4>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      Try to slow down slightly when introducing yourself. This gives the prospect time to process your information and creates a more professional impression.
                    </p>
                  </div>

                  <div className="p-4 bg-blue-50 dark:bg-blue-950/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2">Active Listening</h4>
                    <p className="text-sm text-blue-800 dark:text-blue-200">
                      Remember to pause after asking questions to allow the prospect to respond fully. This shows respect and helps build rapport.
                    </p>
                  </div>
                </div>
              </div>

              {/* Action Buttons */}
              <div className="flex gap-3 pt-4">
                <Button
                  className="flex-1"
                  onClick={() => {
                    setShowReport(false)
                    setCallDuration(0)
                  }}
                >
                  Try Another Call
                </Button>
                <Button
                  variant="outline"
                  className="flex-1"
                  asChild
                >
                  <Link href="/prospect/role-call-training">
                    Back to Training Types
                  </Link>
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
      </div>
    </div>
  )
}
