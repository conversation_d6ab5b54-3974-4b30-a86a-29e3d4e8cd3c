import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { notFound } from 'next/navigation'
import { format } from 'date-fns'
import JobDetailsPage from '@/components/job-details-page'

// Type definitions for job posting data
type SkillItem = string | { name: string } | null
type SalaryRange = { min?: number; max?: number } | null
type LocationData = { city?: string; country?: string; remote?: boolean } | null

export default async function JobDetails({ params }: { params: Promise<{ id: string }> }) {
  const { id } = await params
  const supabase = createServerComponentClient({ cookies })

  // Fetch the specific job posting
  const { data: jobPosting, error } = await supabase
    .from("job_postings")
    .select(`
      *,
      bpos(id, name, logo_url, description, industry, size_range, founded_year, website_url)
    `)
    .eq("id", id)
    .eq("status", "published")
    .single()

  if (error || !jobPosting) {
    notFound()
  }

  // Format job posting data for the component
  const createdDate = jobPosting.created_at ? new Date(jobPosting.created_at) : new Date()
  const now = new Date()
  const diffTime = Math.abs(now.getTime() - createdDate.getTime())
  const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
  
  // Parse salary range if it exists
  let salaryDisplay = "Not specified"
  if (jobPosting.salary_range && typeof jobPosting.salary_range === 'object') {
    const salaryRange = jobPosting.salary_range as SalaryRange
    if (salaryRange?.min && salaryRange?.max) {
      salaryDisplay = `$${salaryRange.min.toLocaleString()} - $${salaryRange.max.toLocaleString()}`
    } else if (salaryRange?.min) {
      salaryDisplay = `From $${salaryRange.min.toLocaleString()}`
    } else if (salaryRange?.max) {
      salaryDisplay = `Up to $${salaryRange.max.toLocaleString()}`
    }
  }
  
  // Format location
  let locationDisplay = "Remote"
  if (jobPosting.location && typeof jobPosting.location === 'object') {
    const location = jobPosting.location as LocationData
    if (location?.city && location?.country) {
      locationDisplay = `${location.city}, ${location.country}`
    } else if (location?.city) {
      locationDisplay = location.city
    } else if (location?.country) {
      locationDisplay = location.country
    } else if (location?.remote) {
      locationDisplay = "Remote"
    }
  }
  
  // Format required skills as tags
  const skillTags = Array.isArray(jobPosting.required_skills)
    ? jobPosting.required_skills
        .filter((skill): skill is NonNullable<SkillItem> => skill !== null)
        .map((skill: SkillItem) => {
          if (typeof skill === 'string') return skill;
          if (typeof skill === 'object' && skill !== null && 'name' in skill) {
            return (skill as { name: string }).name || '';
          }
          return '';
        })
        .filter(Boolean)
    : []

  // Format requirements and responsibilities
  const requirements = Array.isArray(jobPosting.requirements)
    ? jobPosting.requirements.filter(Boolean)
    : []

  const responsibilities = Array.isArray(jobPosting.responsibilities)
    ? jobPosting.responsibilities.filter(Boolean)
    : []

  const formattedJob = {
    id: jobPosting.id,
    title: jobPosting.title,
    company: jobPosting.bpos?.name || "Unknown Company",
    companyLogo: jobPosting.bpos?.logo_url || null,
    companyDescription: jobPosting.bpos?.description || "",
    companyWebsite: jobPosting.bpos?.website_url || "",
    companyIndustry: jobPosting.bpos?.industry || "",
    companySize: jobPosting.bpos?.size_range || "",
    companyFounded: jobPosting.bpos?.founded_year || null,
    location: locationDisplay,
    jobType: jobPosting.job_type,
    salary: salaryDisplay,
    postedDate: `Posted ${diffDays} ${diffDays === 1 ? 'day' : 'days'} ago`,
    description: jobPosting.description,
    requirements,
    responsibilities,
    skills: skillTags,
    isNew: diffDays <= 2,
    deadline: jobPosting.application_deadline
      ? format(new Date(jobPosting.application_deadline), 'MMM dd, yyyy')
      : null,
    benefits: jobPosting.benefits || [],
    experienceLevel: jobPosting.experience_level || "Not specified",
    employmentType: jobPosting.employment_type || jobPosting.job_type,
  }
  
  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900/20 dark:to-purple-900/20">
      <JobDetailsPage job={formattedJob} />
    </div>
  )
}
