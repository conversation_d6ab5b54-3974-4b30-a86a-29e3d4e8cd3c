/**
 * Authentication Rate Limiting Utilities
 * Helps manage rate limiting for authentication requests
 */

interface RateLimitState {
  attempts: number;
  lastAttempt: number;
  isBlocked: boolean;
  blockUntil: number;
}

// In-memory store for rate limiting (use Redis in production)
const rateLimitStore = new Map<string, RateLimitState>();

// Configuration - COMPLETELY DISABLED FOR DEVELOPMENT
const RATE_LIMIT_CONFIG = {
  maxAttempts: Number.MAX_SAFE_INTEGER, // Completely unlimited
  windowMs: 15 * 60 * 1000, // 15 minutes
  blockDurationMs: 0, // No blocking
};

/**
 * Get client identifier for rate limiting
 */
function getClientId(email?: string): string {
  // Use email if provided, otherwise use a generic identifier
  // In a real app, you might want to use IP address + user agent
  return email || 'anonymous';
}

/**
 * Check if client is rate limited
 */
export function checkRateLimit(email?: string): {
  isAllowed: boolean;
  remainingAttempts: number;
  resetTime: number | null;
  blockUntil: number | null;
} {
  // DEVELOPMENT MODE: Always allow requests
  if (process.env.DISABLE_RATE_LIMITING === 'true' || process.env.NODE_ENV === 'development') {
    return {
      isAllowed: true,
      remainingAttempts: Number.MAX_SAFE_INTEGER,
      resetTime: null,
      blockUntil: null,
    };
  }

  const clientId = getClientId(email);
  const now = Date.now();

  let state = rateLimitStore.get(clientId);

  // Initialize state if not exists
  if (!state) {
    state = {
      attempts: 0,
      lastAttempt: 0,
      isBlocked: false,
      blockUntil: 0,
    };
  }

  // Check if block period has expired
  if (state.isBlocked && now > state.blockUntil) {
    state.isBlocked = false;
    state.attempts = 0;
    state.blockUntil = 0;
  }

  // If currently blocked
  if (state.isBlocked) {
    return {
      isAllowed: false,
      remainingAttempts: 0,
      resetTime: state.blockUntil,
      blockUntil: state.blockUntil,
    };
  }

  // Reset attempts if window has expired
  if (now - state.lastAttempt > RATE_LIMIT_CONFIG.windowMs) {
    state.attempts = 0;
  }

  const remainingAttempts = Math.max(0, RATE_LIMIT_CONFIG.maxAttempts - state.attempts);

  return {
    isAllowed: remainingAttempts > 0,
    remainingAttempts,
    resetTime: state.lastAttempt + RATE_LIMIT_CONFIG.windowMs,
    blockUntil: null,
  };
}

/**
 * Record a failed authentication attempt
 */
export function recordFailedAttempt(email?: string): {
  isBlocked: boolean;
  blockUntil: number | null;
  remainingAttempts: number;
} {
  // DEVELOPMENT MODE: Never block
  if (process.env.DISABLE_RATE_LIMITING === 'true' || process.env.NODE_ENV === 'development') {
    return {
      isBlocked: false,
      blockUntil: null,
      remainingAttempts: Number.MAX_SAFE_INTEGER,
    };
  }

  const clientId = getClientId(email);
  const now = Date.now();

  let state = rateLimitStore.get(clientId) || {
    attempts: 0,
    lastAttempt: 0,
    isBlocked: false,
    blockUntil: 0,
  };

  // Reset attempts if window has expired
  if (now - state.lastAttempt > RATE_LIMIT_CONFIG.windowMs) {
    state.attempts = 0;
  }

  state.attempts++;
  state.lastAttempt = now;

  // Check if should be blocked
  if (state.attempts >= RATE_LIMIT_CONFIG.maxAttempts) {
    state.isBlocked = true;
    state.blockUntil = now + RATE_LIMIT_CONFIG.blockDurationMs;
  }

  rateLimitStore.set(clientId, state);

  return {
    isBlocked: state.isBlocked,
    blockUntil: state.isBlocked ? state.blockUntil : null,
    remainingAttempts: Math.max(0, RATE_LIMIT_CONFIG.maxAttempts - state.attempts),
  };
}

/**
 * Record a successful authentication attempt
 */
export function recordSuccessfulAttempt(email?: string): void {
  const clientId = getClientId(email);
  
  // Clear the rate limit state on successful login
  rateLimitStore.delete(clientId);
}

/**
 * Clear rate limit for a client (admin function)
 */
export function clearRateLimit(email?: string): void {
  const clientId = getClientId(email);
  rateLimitStore.delete(clientId);
}

/**
 * Get formatted time remaining for block
 */
export function getTimeRemaining(blockUntil: number): string {
  const remaining = Math.max(0, blockUntil - Date.now());
  const minutes = Math.floor(remaining / 60000);
  const seconds = Math.floor((remaining % 60000) / 1000);
  return `${minutes}:${seconds.toString().padStart(2, '0')}`;
}

/**
 * Clean up expired entries (call periodically)
 */
export function cleanupExpiredEntries(): void {
  const now = Date.now();
  
  for (const [clientId, state] of rateLimitStore.entries()) {
    // Remove entries that are no longer blocked and haven't been active recently
    if (!state.isBlocked && (now - state.lastAttempt) > RATE_LIMIT_CONFIG.windowMs * 2) {
      rateLimitStore.delete(clientId);
    }
    // Remove entries where block period has expired
    else if (state.isBlocked && now > state.blockUntil) {
      rateLimitStore.delete(clientId);
    }
  }
}

// Clean up expired entries every 5 minutes
if (typeof window === 'undefined') {
  setInterval(cleanupExpiredEntries, 5 * 60 * 1000);
}
