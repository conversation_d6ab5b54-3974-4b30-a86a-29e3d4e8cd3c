# 🏗️ Phase 1: Foundation

## 🎯 Phase Overview

Establish the foundational architecture for Luna by implementing a multi-tiered account system with fresh database schema, enabling seamless context switching between individual and organizational accounts while maintaining data separation and security.

## ⏱️ Timeline: 3-4 weeks

## 🎯 Core Objectives

### 1. Multi-Tiered Account System
- Implement individual-first account creation
- Enable organization creation and membership management
- Build context switching infrastructure
- Establish role-based permissions

### 2. Fresh Database Schema
- Design Luna-optimized database structure
- Implement multi-tenancy with data isolation
- Create flexible permission system
- Establish audit trails and historical tracking

### 3. Portal Restructuring
- Transform BPO portal → Organization portal
- Transform Prospect portal → User portal
- Implement organization-specific routing
- Add context switching UI

## 🗄️ Database Schema Changes

### 📋 New Core Tables

#### `users` (Enhanced Individual Accounts)
```sql
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'individual',
  status user_status DEFAULT 'active',
  avatar_url TEXT,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  -- Individual account data
  personal_skills JSONB DEFAULT '[]',
  learning_preferences JSONB DEFAULT '{}',
  industry_interests TEXT[],
  timezone VARCHAR(50) DEFAULT 'UTC'
);

-- New role types
CREATE TYPE user_role AS ENUM (
  'platform_admin',    -- Luna platform administrators
  'individual',        -- Standalone individual users
  'org_owner',        -- Organization owners
  'org_admin',        -- Organization administrators
  'org_member'        -- Organization team members
);
```

#### `organizations` (Multi-Tenant Entities)
```sql
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL, -- For custom URLs
  description TEXT,
  industry VARCHAR(100),
  size_range VARCHAR(50),
  logo_url TEXT,
  website_url TEXT,
  
  -- Multi-tenancy settings
  subdomain VARCHAR(50) UNIQUE, -- org.luna.com
  custom_domain VARCHAR(255),   -- custom.company.com
  branding_config JSONB DEFAULT '{}',
  
  -- Subscription & limits
  subscription_tier VARCHAR(50) DEFAULT 'basic',
  max_members INTEGER DEFAULT 50,
  features_enabled JSONB DEFAULT '{}',
  
  -- Ownership
  created_by UUID NOT NULL REFERENCES users(id),
  status org_status DEFAULT 'active',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

CREATE TYPE org_status AS ENUM ('active', 'suspended', 'trial', 'cancelled');
```

#### `organization_memberships` (Bridge Table)
```sql
CREATE TABLE organization_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- Membership details
  role org_member_role NOT NULL,
  permissions JSONB DEFAULT '{}',
  status membership_status DEFAULT 'pending',
  
  -- Invitation tracking
  invited_by UUID REFERENCES users(id),
  invited_at TIMESTAMPTZ,
  accepted_at TIMESTAMPTZ,
  
  -- Audit trail
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, organization_id)
);

CREATE TYPE org_member_role AS ENUM (
  'owner',      -- Full organization control
  'admin',      -- Administrative privileges
  'manager',    -- Team management
  'member',     -- Standard access
  'viewer'      -- Read-only access
);

CREATE TYPE membership_status AS ENUM (
  'pending',    -- Invitation sent, not accepted
  'active',     -- Active membership
  'inactive',   -- Temporarily disabled
  'removed'     -- Permanently removed
);
```

### 🔄 Transformed Tables

#### `individuals` (Renamed from prospects)
```sql
CREATE TABLE individuals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Profile information
  contact_info JSONB DEFAULT '{}',
  education JSONB[] DEFAULT '{}',
  experience JSONB[] DEFAULT '{}',
  skills JSONB[] DEFAULT '{}',
  
  -- Media assets
  profile_image_url TEXT,
  intro_video_url TEXT,
  resume_url TEXT,
  
  -- Privacy & visibility
  profile_visibility profile_visibility DEFAULT 'private',
  searchable_by_orgs BOOLEAN DEFAULT false,
  
  -- Learning status
  learning_status learning_status DEFAULT 'not_started',
  current_learning_path UUID,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);

CREATE TYPE profile_visibility AS ENUM ('private', 'organization', 'public');
CREATE TYPE learning_status AS ENUM ('not_started', 'in_progress', 'completed', 'paused');
```

### 🆕 New Supporting Tables

#### `user_contexts` (Context Switching)
```sql
CREATE TABLE user_contexts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Current context
  active_context context_type NOT NULL DEFAULT 'individual',
  active_organization_id UUID REFERENCES organizations(id),
  
  -- Session management
  last_context_switch TIMESTAMPTZ DEFAULT NOW(),
  session_data JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);

CREATE TYPE context_type AS ENUM ('individual', 'organization');
```

## 🔐 Security & Permissions

### Row Level Security (RLS) Policies

#### Users Table
```sql
-- Users can view their own profile
CREATE POLICY "users_select_own" ON users
  FOR SELECT USING (auth.uid() = id);

-- Users can update their own profile
CREATE POLICY "users_update_own" ON users
  FOR UPDATE USING (auth.uid() = id);
```

#### Organizations Table
```sql
-- Organization members can view their organization
CREATE POLICY "orgs_select_members" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM organization_memberships 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Only owners and admins can update organization
CREATE POLICY "orgs_update_admins" ON organizations
  FOR UPDATE USING (
    id IN (
      SELECT organization_id FROM organization_memberships 
      WHERE user_id = auth.uid() 
      AND status = 'active' 
      AND role IN ('owner', 'admin')
    )
  );
```

## 🛣️ Routing & Portal Changes

### Current → New Portal Structure

| Current Route | New Route | Description |
|---------------|-----------|-------------|
| `/bpo/*` | `/org/*` | Organization portal |
| `/prospect/*` | `/user/*` | Individual user portal |
| `/admin/*` | `/admin/*` | Platform admin (unchanged) |

### Organization-Specific Routing
```typescript
// Multi-tenant URL structure
https://luna.app/org/[orgSlug]/*          // Organization portal
https://[orgSlug].luna.app/*              // Subdomain routing
https://custom.company.com/*              // Custom domain

// Individual user routes
https://luna.app/user/*                   // Individual portal
https://luna.app/dashboard                // Context-aware dashboard
```

### Context Switching Implementation
```typescript
// Context switching component
interface UserContext {
  type: 'individual' | 'organization';
  organizationId?: string;
  organizationSlug?: string;
  permissions: string[];
}

// Context switching logic
const switchContext = async (newContext: UserContext) => {
  await updateUserContext(newContext);
  router.push(getContextRoute(newContext));
};
```

## 🔧 API Changes

### New Authentication Endpoints
```typescript
// Enhanced authentication with context
POST /api/auth/login
POST /api/auth/switch-context
GET  /api/auth/user-contexts
POST /api/auth/create-organization
POST /api/auth/join-organization
```

### Updated Existing Endpoints
```typescript
// Renamed endpoints
/api/prospects/*     → /api/individuals/*
/api/bpo/*          → /api/organizations/*

// Context-aware endpoints
GET /api/dashboard   // Returns data based on current context
GET /api/profile     // Individual or organization profile
```

## 📱 UI/UX Changes

### Context Switching Interface
- Header dropdown for context selection
- Clear visual indicators of current context
- Seamless transition between individual/org views
- Breadcrumb navigation showing context path

### Organization Portal Features
- Custom branding and theming
- Organization-specific dashboard
- Team member management
- Organization settings and configuration

### Individual Portal Features
- Personal learning dashboard
- Skills gap analysis
- Learning path recommendations
- Profile management

## ✅ Phase 1 Deliverables

### Week 1: Database Foundation
- [ ] Create fresh Supabase database
- [ ] Implement new schema with all tables
- [ ] Set up RLS policies
- [ ] Create database functions and triggers

### Week 2: Authentication & Context System
- [ ] Update authentication system
- [ ] Implement context switching logic
- [ ] Create user context management
- [ ] Build organization invitation system

### Week 3: Portal Restructuring
- [ ] Update routing structure
- [ ] Transform BPO portal to Organization portal
- [ ] Transform Prospect portal to User portal
- [ ] Implement multi-tenant URL handling

### Week 4: Integration & Testing
- [ ] Connect new database to application
- [ ] Test context switching functionality
- [ ] Validate multi-tenancy isolation
- [ ] Performance testing and optimization

## 🧪 Testing Strategy

### Unit Tests
- User context switching logic
- Organization membership management
- Permission validation
- Data isolation verification

### Integration Tests
- End-to-end user flows
- Organization creation and joining
- Context switching scenarios
- Multi-tenant data separation

### Security Tests
- RLS policy validation
- Permission boundary testing
- Data leakage prevention
- Authentication flow security

## 📊 Success Metrics

- [ ] Users can seamlessly switch between individual and organization contexts
- [ ] Organizations are completely isolated (no data leakage)
- [ ] Sub-second context switching performance
- [ ] 100% test coverage for new authentication flows
- [ ] Zero security vulnerabilities in multi-tenant setup

---

*Next: [Phase 2: Core Features](./phase-2-core-features.md)*
