'use client'

import { useState } from "react";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { InviteToInterviewDialog } from "@/components/invite-to-interview-dialog";
import Link from "next/link";
import { ArrowLeft, Mail, Phone, MapPin, Calendar, Briefcase, BookOpen, Trophy, FileCheck, BarChart3, Star, Headphones } from "lucide-react";
import { PerformanceReport } from "@/components/role-call/performance-report";
import { DetailedPerformanceAssessment } from "@/components/role-call/detailed-performance-assessment";

export default function CandidateProfileClient({ candidate, prospectId }: { candidate: any, prospectId: string }) {
  const [showInviteDialog, setShowInviteDialog] = useState(false);

  // Helper for initials
  const getInitials = (name: string) => {
    return name
      .split(' ')
      .map((n: string) => n[0] || '')
      .join('')
      .toUpperCase()
      .substring(0, 2);
  };

  // Format date
  const formatDate = (dateString: string) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', { year: 'numeric', month: 'short', day: 'numeric' });
  };

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation Bar */}
      <header className="border-b bg-card/80">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Link href="/prospect/profile">
                <Button variant="ghost" size="icon">
                  <ArrowLeft className="h-5 w-5" />
                </Button>
              </Link>
              <h1 className="ml-4 text-xl font-semibold">Candidate Profile</h1>
            </div>
            <div>
              <Button
                variant="default"
                size="sm"
                onClick={() => setShowInviteDialog(true)}
              >
                <Calendar className="h-4 w-4 mr-2" />
                Invite to Interview
              </Button>
              <Button variant="outline" size="sm" className="ml-2">
                Contact
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Main Content */}
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Profile Info */}
          <div className="lg:col-span-1 space-y-6">
            {/* Profile Card */}
            <Card className="overflow-hidden">
              <div className="bg-gradient-to-r from-blue-500 to-indigo-600 h-24"></div>
              <div className="px-6 pb-6 pt-12 flex flex-col items-center text-center">
                <Avatar className="w-24 h-24 border-4 border-white -mt-20">
                  <AvatarImage src={candidate.avatarUrl} alt={candidate.name} className="object-cover" />
                  <AvatarFallback className="text-2xl bg-gray-200">{getInitials(candidate.name)}</AvatarFallback>
                </Avatar>
                <div className="mt-4">
                  <h2 className="text-xl font-bold">{candidate.name}</h2>
                  <p className="text-muted-foreground">{candidate.title}</p>
                  
                  {/* Contact Information */}
                  <div className="mt-4 space-y-2">
                    {candidate.email && (
                      <div className="flex items-center gap-2 text-sm justify-center">
                        <Mail className="h-4 w-4 text-muted-foreground" />
                        <span>{candidate.email}</span>
                      </div>
                    )}
                    {candidate.phone && (
                      <div className="flex items-center gap-2 text-sm justify-center">
                        <Phone className="h-4 w-4 text-muted-foreground" />
                        <span>{candidate.phone}</span>
                      </div>
                    )}
                    {candidate.location && (
                      <div className="flex items-center gap-2 text-sm justify-center">
                        <MapPin className="h-4 w-4 text-muted-foreground" />
                        <span>{candidate.location}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </Card>

            {/* Agent Rating Card - Minimal Design */}
            <Card className="mt-6 border-l-4 border-amber-500 overflow-hidden shadow">
              <CardContent className="p-4">
                <div className="flex items-center justify-between">
                  <div className="flex items-center">
                    <Star className="h-5 w-5 mr-2 text-amber-500 fill-amber-500" />
                    <h3 className="font-medium text-base">Agent Rating</h3>
                  </div>
                  
                  <div className="bg-amber-50 border border-amber-200 rounded-md px-3 py-1">
                    <span className="text-xl font-bold text-amber-600">
                      {Math.min(5, Math.max(0, Math.round(candidate.agentRating.overall_score / 20)))}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Skills Card */}
            {candidate.skills.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Star className="h-5 w-5 mr-2 text-yellow-500" />
                    Skills
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="flex flex-wrap gap-2">
                    {candidate.skills.map((skill, i) => (
                      <Badge key={i} variant="outline" className="px-3 py-1 rounded-full">
                        {skill.name}
                        {skill.level && <span className="ml-1 opacity-70">• {skill.level}</span>}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Badges */}
            {candidate.badges.length > 0 && (
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg flex items-center">
                    <Trophy className="h-5 w-5 mr-2 text-amber-500" />
                    Earned Badges
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 gap-4">
                    {candidate.badges.map((badge) => (
                      <div key={badge.id} className="flex flex-col items-center text-center p-3 border rounded-lg">
                        <div className="w-12 h-12 rounded-full bg-primary/10 flex items-center justify-center mb-2">
                          <img 
                            src={badge.image_url || "/badges/default.svg"} 
                            alt={badge.title}
                            className="w-8 h-8"
                          />
                        </div>
                        <div className="font-medium text-sm">{badge.title}</div>
                        <div className="text-xs text-muted-foreground mt-1">{formatDate(badge.date_earned)}</div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            )}
          </div>

          {/* Center/Right Columns - Content */}
          <div className="lg:col-span-2 space-y-6">
            {/* Tabs Navigation */}
            <Tabs defaultValue="about" className="w-full">
              <TabsList className="grid grid-cols-6 w-full">
                <TabsTrigger value="about">About</TabsTrigger>
                <TabsTrigger value="experience">Experience</TabsTrigger>
                <TabsTrigger value="education">Education</TabsTrigger>
                <TabsTrigger value="certificates">Certificates</TabsTrigger>
                <TabsTrigger value="assessments">Assessments</TabsTrigger>
                <TabsTrigger value="training">Training</TabsTrigger>
              </TabsList>

              {/* About Tab */}
              <TabsContent value="about" className="space-y-6 pt-4">
                {/* Profile Video */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Introduction Video</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="aspect-video rounded-lg overflow-hidden bg-black">
                      {candidate.introVideoUrl ? (
                        <video 
                          src={candidate.introVideoUrl} 
                          controls 
                          className="w-full h-full object-cover"
                          poster="/video-placeholder.jpg"
                        />
                      ) : (
                        <div className="w-full h-full flex items-center justify-center text-muted-foreground">
                          No introduction video available
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* About Section */}
                {candidate.about && (
                  <Card>
                    <CardHeader>
                      <CardTitle className="text-lg">About Me</CardTitle>
                    </CardHeader>
                    <CardContent>
                      <p className="leading-relaxed whitespace-pre-line">{candidate.about}</p>
                    </CardContent>
                  </Card>
                )}
              </TabsContent>

              {/* Experience Tab */}
              <TabsContent value="experience" className="space-y-6 pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <Briefcase className="h-5 w-5 mr-2 text-gray-500" />
                      Work Experience
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {candidate.experience.length === 0 ? (
                      <p className="text-muted-foreground">No work experience listed.</p>
                    ) : (
                      <div className="space-y-6">
                        {candidate.experience.map((exp, i) => (
                          <div key={i} className="border-l-2 border-primary/20 pl-4 pb-6 relative">
                            <div className="absolute w-3 h-3 bg-primary rounded-full -left-[7px] top-1"></div>
                            <div className="font-medium text-lg">{exp.title}</div>
                            <div className="text-primary font-medium">{exp.company}</div>
                            <div className="flex items-center text-sm text-muted-foreground mb-2">
                              <Calendar className="h-3 w-3 mr-1" />
                              <span>{exp.start} - {exp.end}</span>
                            </div>
                            <p className="text-sm">{exp.description}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Education Tab */}
              <TabsContent value="education" className="space-y-6 pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <BookOpen className="h-5 w-5 mr-2 text-gray-500" />
                      Education
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {candidate.education.length === 0 ? (
                      <p className="text-muted-foreground">No education listed.</p>
                    ) : (
                      <div className="space-y-6">
                        {candidate.education.map((edu, i) => (
                          <div key={i} className="border-l-2 border-primary/20 pl-4 pb-6 relative">
                            <div className="absolute w-3 h-3 bg-primary rounded-full -left-[7px] top-1"></div>
                            <div className="font-medium text-lg">{edu.degree}</div>
                            <div className="text-primary font-medium">{edu.institution}</div>
                            <div className="flex items-center text-sm text-muted-foreground mb-2">
                              <Calendar className="h-3 w-3 mr-1" />
                              <span>{edu.start} - {edu.end}</span>
                            </div>
                            <p className="text-sm">{edu.description}</p>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Certificates Tab */}
              <TabsContent value="certificates" className="space-y-6 pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <FileCheck className="h-5 w-5 mr-2 text-emerald-500" />
                      Certificates
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {candidate.certificates.length === 0 ? (
                      <p className="text-muted-foreground">No certificates earned yet.</p>
                    ) : (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {candidate.certificates.map((cert) => (
                          <div key={cert.id} className="border rounded-lg p-4 flex flex-col">
                            <div className="font-medium">{cert.module_title}</div>
                            <div className="text-sm text-muted-foreground mb-2">
                              Issued on {formatDate(cert.issue_date)}
                            </div>
                            {cert.certificate_url && (
                              <Button variant="outline" size="sm" className="mt-auto self-start" asChild>
                                <a href={cert.certificate_url} target="_blank" rel="noopener noreferrer">
                                  View Certificate
                                </a>
                              </Button>
                            )}
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Assessments Tab */}
              <TabsContent value="assessments" className="space-y-6 pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <BarChart3 className="h-5 w-5 mr-2 text-violet-500" />
                      Assessment Reports
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    {candidate.assessmentReports.length === 0 ? (
                      <p className="text-muted-foreground">No assessment reports available.</p>
                    ) : (
                      <div className="space-y-4">
                        {candidate.assessmentReports.map((report) => (
                          <div key={report.id} className="border rounded-lg p-4">
                            <div className="flex justify-between items-start">
                              <div>
                                <div className="font-medium">{report.title}</div>
                                <div className="text-sm text-muted-foreground">
                                  Completed on {formatDate(report.completion_date)}
                                </div>
                              </div>
                              <Badge variant={
                                report.category === 'Technical' ? 'default' :
                                report.category === 'Soft Skills' ? 'secondary' :
                                report.category === 'Critical Thinking' ? 'outline' :
                                'destructive'
                              }>
                                {report.category}
                              </Badge>
                            </div>
                            <div className="mt-3">
                              <div className="flex justify-between text-sm mb-1">
                                <span>Score</span>
                                <span className="font-medium">{report.score}%</span>
                              </div>
                              <Progress value={report.score} className="h-2" />
                            </div>
                          </div>
                        ))}
                      </div>
                    )}
                  </CardContent>
                </Card>
              </TabsContent>

              {/* Training Tab */}
              <TabsContent value="training" className="space-y-6 pt-4">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg flex items-center">
                      <BookOpen className="h-5 w-5 mr-2 text-blue-500" />
                      Training Report
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-6">
                    <div>
                      <div className="flex justify-between mb-2">
                        <span className="font-medium">Overall Training Progress</span>
                        <span>{candidate.trainingReport.progress_percentage}%</span>
                      </div>
                      <Progress value={candidate.trainingReport.progress_percentage} className="h-2" />
                      <div className="flex justify-between text-sm text-muted-foreground mt-2">
                        <span>Completed: {candidate.trainingReport.completed_modules} modules</span>
                        <span>Total: {candidate.trainingReport.total_modules} modules</span>
                      </div>
                    </div>

                    {/* Role-Call Training Performance */}
                    <DetailedPerformanceAssessment variant="summary" />

                    {/* Detailed Assessment for Recruiters */}
                    <div className="mt-6">
                      <h4 className="font-medium mb-4">Detailed Call Performance Analysis</h4>
                      <DetailedPerformanceAssessment variant="full" />
                    </div>
                  </CardContent>
                </Card>
              </TabsContent>
            </Tabs>
          </div>
        </div>
      </main>

      {/* Invite to Interview Dialog */}
      <InviteToInterviewDialog
        prospectId={prospectId}
        prospectName={candidate.name}
        open={showInviteDialog}
        onOpenChange={setShowInviteDialog}
      />
    </div>
  );
}
