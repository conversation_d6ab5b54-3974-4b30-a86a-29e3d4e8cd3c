import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    console.log('[AUTH-CHECK] 🚀 Starting Supabase Auth check...');
    
    const supabase = createAdminClient();
    console.log('[AUTH-CHECK] ✅ Admin client created');

    // <NAME_EMAIL> exists in Supabase Auth
    console.log('[AUTH-CHECK] 🔍 Checking Supabase Auth users...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    console.log('[AUTH-CHECK] 📊 Supabase Auth users result:', {
      success: !authError,
      error: authError?.message,
      userCount: authUsers?.users?.length || 0,
      users: authUsers?.users?.map(u => ({ id: u.id, email: u.email, created_at: u.created_at }))
    });

    // Check if our test user exists in auth
    const adminUser = authUsers?.users?.find(u => u.email === '<EMAIL>');
    console.log('[AUTH-CHECK] 👤 Admin user in auth:', {
      exists: !!adminUser,
      id: adminUser?.id,
      email: adminUser?.email,
      emailConfirmed: adminUser?.email_confirmed_at,
      lastSignIn: adminUser?.last_sign_in_at
    });

    // Try to sign in with the admin user to test auth
    if (adminUser) {
      console.log('[AUTH-CHECK] 🔍 Testing sign in with admin user...');
      try {
        const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
          email: '<EMAIL>',
          password: 'BeeMO5317'
        });
        
        console.log('[AUTH-CHECK] 📊 Sign in test result:', {
          success: !signInError,
          error: signInError?.message,
          hasUser: !!signInData?.user,
          hasSession: !!signInData?.session
        });
      } catch (signInErr) {
        console.log('[AUTH-CHECK] ❌ Sign in test error:', signInErr);
      }
    }

    return NextResponse.json({
      success: true,
      message: 'Auth check completed',
      results: {
        authUsersCount: authUsers?.users?.length || 0,
        adminUserExists: !!adminUser,
        adminUserId: adminUser?.id,
        adminUserConfirmed: !!adminUser?.email_confirmed_at,
        allUsers: authUsers?.users?.map(u => ({ 
          id: u.id, 
          email: u.email, 
          confirmed: !!u.email_confirmed_at,
          lastSignIn: u.last_sign_in_at 
        }))
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[AUTH-CHECK] ❌ Unexpected error during auth check:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during auth check',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
