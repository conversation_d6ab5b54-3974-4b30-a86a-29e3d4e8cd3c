const { createClient } = require('@supabase/supabase-js');

// Load environment variables
require('dotenv').config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

async function debugStorageBucket() {
  console.log('🔍 Debugging Storage Bucket Configuration...\n');

  try {
    // 1. List all buckets
    console.log('1️⃣ Listing all storage buckets...');
    const { data: buckets, error: listError } = await supabase.storage.listBuckets();
    
    if (listError) {
      console.error('❌ Error listing buckets:', listError);
      return;
    }

    console.log('📋 Available buckets:');
    buckets.forEach(bucket => {
      console.log(`  - ${bucket.name} (${bucket.public ? 'public' : 'private'}, created: ${bucket.created_at})`);
    });

    // 2. Check if 'files' bucket exists
    const filesBucket = buckets.find(b => b.name === 'files');
    if (!filesBucket) {
      console.log('\n❌ "files" bucket does not exist!');
      console.log('💡 Available buckets are:', buckets.map(b => b.name).join(', '));
      return;
    }

    console.log('\n✅ "files" bucket found:', filesBucket);

    // 3. Test file upload to 'files' bucket
    console.log('\n2️⃣ Testing file upload to "files" bucket...');
    
    const testContent = 'Test file content for debugging';
    const testFile = new Blob([testContent], { type: 'text/plain' });
    const testFileName = `debug-test-${Date.now()}.txt`;

    const { data: uploadData, error: uploadError } = await supabase.storage
      .from('files')
      .upload(testFileName, testFile);

    if (uploadError) {
      console.error('❌ Upload test failed:', uploadError);
      console.log('📋 Upload error details:', {
        message: uploadError.message,
        statusCode: uploadError.statusCode,
        error: uploadError.error
      });
    } else {
      console.log('✅ Upload test successful:', uploadData);
      
      // Test getting public URL
      const { data: { publicUrl } } = supabase.storage
        .from('files')
        .getPublicUrl(testFileName);
      
      console.log('🔗 Public URL:', publicUrl);
      
      // Clean up test file
      await supabase.storage.from('files').remove([testFileName]);
      console.log('🧹 Test file cleaned up');
    }

    // 4. Check bucket policies
    console.log('\n3️⃣ Checking bucket policies...');
    try {
      // Try to list files in the bucket (this will show if we have read access)
      const { data: files, error: listFilesError } = await supabase.storage
        .from('files')
        .list('', { limit: 1 });

      if (listFilesError) {
        console.error('❌ Cannot list files in bucket:', listFilesError);
      } else {
        console.log('✅ Can list files in bucket. File count:', files.length);
      }
    } catch (policyError) {
      console.error('❌ Policy check failed:', policyError);
    }

    // 5. Test with different file types
    console.log('\n4️⃣ Testing with image file type...');
    const imageTestFile = new Blob(['fake image data'], { type: 'image/jpeg' });
    const imageTestFileName = `debug-image-${Date.now()}.jpg`;

    const { data: imageUploadData, error: imageUploadError } = await supabase.storage
      .from('files')
      .upload(imageTestFileName, imageTestFile);

    if (imageUploadError) {
      console.error('❌ Image upload test failed:', imageUploadError);
    } else {
      console.log('✅ Image upload test successful');
      await supabase.storage.from('files').remove([imageTestFileName]);
      console.log('🧹 Image test file cleaned up');
    }

  } catch (error) {
    console.error('❌ Debug failed:', error);
  }
}

// Run the debug
debugStorageBucket();
