# BPO Training Platform Environment Variables
# Copy this file to .env.local and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# =============================================================================
# SECURITY KEYS (Generate secure random strings)
# =============================================================================
# Generate with: openssl rand -hex 32
ENCRYPTION_KEY=your_32_character_encryption_key
FIELD_ENCRYPTION_KEY=your_32_character_field_encryption_key
JWT_SECRET=your_jwt_secret_key
API_SECRET_KEY=your_api_secret_key

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your_app_password

# =============================================================================
# FILE UPLOAD CONFIGURATION
# =============================================================================
NEXT_PUBLIC_MAX_FILE_SIZE=10485760  # 10MB in bytes
NEXT_PUBLIC_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.jpg,.jpeg,.png

# =============================================================================
# RATE LIMITING
# =============================================================================
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000  # 15 minutes in milliseconds

# =============================================================================
# ANALYTICS AND MONITORING (Optional)
# =============================================================================
NEXT_PUBLIC_ANALYTICS_ID=your_analytics_id
NEXT_PUBLIC_SENTRY_DSN=your_sentry_dsn

# =============================================================================
# DEVELOPMENT FLAGS
# =============================================================================
DEBUG=true
LOG_LEVEL=debug
NEXT_PUBLIC_RUN_MIGRATIONS=false

# =============================================================================
# REDIS CONFIGURATION (Optional - for production caching)
# =============================================================================
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your_redis_password

# =============================================================================
# AWS CONFIGURATION (Optional - for file storage)
# =============================================================================
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your_s3_bucket_name
