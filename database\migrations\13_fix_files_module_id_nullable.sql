-- =============================================================================
-- MIGRATION: Fix files table module_id to be nullable
-- =============================================================================
-- This migration fixes the module_id column in the files table to be nullable
-- since uploaded files don't necessarily belong to a specific module.

-- Make module_id nullable for uploaded files
ALTER TABLE public.files ALTER COLUMN module_id DROP NOT NULL;

-- Verify the change
SELECT 
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
    AND table_name = 'files'
    AND column_name = 'module_id';

-- Test insert to verify it works
DO $$
DECLARE
    test_prospect_id UUID;
    test_file_id UUID;
BEGIN
    -- Get a test prospect ID
    SELECT id INTO test_prospect_id FROM public.prospects LIMIT 1;
    
    IF test_prospect_id IS NOT NULL THEN
        -- Test insert with null module_id
        INSERT INTO public.files (
            prospect_id,
            module_id,
            file_type,
            file_category,
            title,
            file_url,
            original_filename,
            file_size,
            mime_type,
            issued_at
        ) VALUES (
            test_prospect_id,
            NULL, -- This should now work
            'document',
            'uploaded',
            'Test Upload File',
            'https://example.com/test.pdf',
            'test.pdf',
            12345,
            'application/pdf',
            NOW()
        ) RETURNING id INTO test_file_id;
        
        -- Clean up test record
        DELETE FROM public.files WHERE id = test_file_id;
        
        RAISE NOTICE 'SUCCESS: module_id nullable fix verified';
    ELSE
        RAISE NOTICE 'WARNING: No prospects found for testing';
    END IF;
END $$;
