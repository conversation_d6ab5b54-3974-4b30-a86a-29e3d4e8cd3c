-- Remove all RLS policies and disable RLS for Luna Platform
-- This creates a straightforward authentication system without row-level security

-- =============================================================================
-- DISABLE RLS ON ALL TABLES
-- =============================================================================

-- Disable RLS on users table
ALTER TABLE users DISABLE ROW LEVEL SECURITY;

-- Disable RLS on organizations table  
ALTER TABLE organizations DISABLE ROW LEVEL SECURITY;

-- Disable RLS on organization_memberships table
ALTER TABLE organization_memberships DISABLE ROW LEVEL SECURITY;

-- Disable RLS on individuals table
ALTER TABLE individuals DISABLE ROW LEVEL SECURITY;

-- Disable RLS on user_contexts table
ALTER TABLE user_contexts DISABLE ROW LEVEL SECURITY;

-- Disable RLS on training tables (if they exist)
ALTER TABLE IF EXISTS training_modules DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS lessons DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS activities DISABLE ROW LEVEL SECURITY;
ALTER TABLE IF EXISTS progress_records DISABLE ROW LEVEL SECURITY;

-- =============================================================================
-- DROP ALL EXISTING RLS POLICIES
-- =============================================================================

-- Drop all policies on users table
DROP POLICY IF EXISTS "Users can view own profile" ON users;
DROP POLICY IF EXISTS "Users can update own profile" ON users;
DROP POLICY IF EXISTS "Platform admins can view all users" ON users;
DROP POLICY IF EXISTS "Platform admins can update all users" ON users;
DROP POLICY IF EXISTS "Org admins can view org members" ON users;
DROP POLICY IF EXISTS "users_select_policy" ON users;
DROP POLICY IF EXISTS "users_update_policy" ON users;
DROP POLICY IF EXISTS "users_insert_policy" ON users;
DROP POLICY IF EXISTS "users_delete_policy" ON users;

-- Drop all policies on organizations table
DROP POLICY IF EXISTS "Users can view organizations they belong to" ON organizations;
DROP POLICY IF EXISTS "Org owners can update their organization" ON organizations;
DROP POLICY IF EXISTS "Platform admins can view all organizations" ON organizations;
DROP POLICY IF EXISTS "Platform admins can manage all organizations" ON organizations;
DROP POLICY IF EXISTS "organizations_select_policy" ON organizations;
DROP POLICY IF EXISTS "organizations_update_policy" ON organizations;
DROP POLICY IF EXISTS "organizations_delete_policy" ON organizations;

-- Drop all policies on organization_memberships table
DROP POLICY IF EXISTS "Users can view own memberships" ON organization_memberships;
DROP POLICY IF EXISTS "Org admins can view org memberships" ON organization_memberships;
DROP POLICY IF EXISTS "Org admins can manage org memberships" ON organization_memberships;
DROP POLICY IF EXISTS "Platform admins can view all memberships" ON organization_memberships;
DROP POLICY IF EXISTS "Platform admins can manage all memberships" ON organization_memberships;
DROP POLICY IF EXISTS "memberships_select_policy" ON organization_memberships;
DROP POLICY IF EXISTS "memberships_update_policy" ON organization_memberships;
DROP POLICY IF EXISTS "memberships_insert_policy" ON organization_memberships;
DROP POLICY IF EXISTS "memberships_delete_policy" ON organization_memberships;

-- Drop all policies on individuals table
DROP POLICY IF EXISTS "Users can view own individual profile" ON individuals;
DROP POLICY IF EXISTS "Users can update own individual profile" ON individuals;
DROP POLICY IF EXISTS "Platform admins can view all individual profiles" ON individuals;
DROP POLICY IF EXISTS "Org admins can view searchable profiles" ON individuals;
DROP POLICY IF EXISTS "individuals_select_policy" ON individuals;
DROP POLICY IF EXISTS "individuals_update_policy" ON individuals;
DROP POLICY IF EXISTS "individuals_delete_policy" ON individuals;

-- Drop all policies on user_contexts table
DROP POLICY IF EXISTS "Users can view own context" ON user_contexts;
DROP POLICY IF EXISTS "Users can update own context" ON user_contexts;
DROP POLICY IF EXISTS "Platform admins can view all contexts" ON user_contexts;
DROP POLICY IF EXISTS "contexts_select_policy" ON user_contexts;
DROP POLICY IF EXISTS "contexts_update_policy" ON user_contexts;
DROP POLICY IF EXISTS "contexts_delete_policy" ON user_contexts;

-- Drop all policies on training tables (if they exist)
DROP POLICY IF EXISTS "modules_select_policy" ON training_modules;
DROP POLICY IF EXISTS "modules_update_policy" ON training_modules;
DROP POLICY IF EXISTS "modules_delete_policy" ON training_modules;
DROP POLICY IF EXISTS "lessons_select_policy" ON lessons;
DROP POLICY IF EXISTS "lessons_update_policy" ON lessons;
DROP POLICY IF EXISTS "lessons_insert_policy" ON lessons;
DROP POLICY IF EXISTS "lessons_delete_policy" ON lessons;
DROP POLICY IF EXISTS "activities_select_policy" ON activities;
DROP POLICY IF EXISTS "activities_update_policy" ON activities;
DROP POLICY IF EXISTS "activities_insert_policy" ON activities;
DROP POLICY IF EXISTS "activities_delete_policy" ON activities;
DROP POLICY IF EXISTS "progress_select_policy" ON progress_records;
DROP POLICY IF EXISTS "progress_update_policy" ON progress_records;
DROP POLICY IF EXISTS "progress_delete_policy" ON progress_records;

-- =============================================================================
-- DROP ALL RLS FUNCTIONS (NO LONGER NEEDED)
-- =============================================================================

-- Drop security functions
DROP FUNCTION IF EXISTS is_platform_admin();
DROP FUNCTION IF EXISTS is_org_admin(uuid);
DROP FUNCTION IF EXISTS is_org_member(uuid);
DROP FUNCTION IF EXISTS get_user_organizations();

-- =============================================================================
-- GRANT FULL ACCESS TO AUTHENTICATED USERS
-- =============================================================================

-- Grant full access to users table
GRANT ALL ON users TO authenticated;
GRANT ALL ON users TO anon;

-- Grant full access to organizations table
GRANT ALL ON organizations TO authenticated;
GRANT ALL ON organizations TO anon;

-- Grant full access to organization_memberships table
GRANT ALL ON organization_memberships TO authenticated;
GRANT ALL ON organization_memberships TO anon;

-- Grant full access to individuals table
GRANT ALL ON individuals TO authenticated;
GRANT ALL ON individuals TO anon;

-- Grant full access to user_contexts table
GRANT ALL ON user_contexts TO authenticated;
GRANT ALL ON user_contexts TO anon;

-- =============================================================================
-- COMMENT
-- =============================================================================

COMMENT ON TABLE users IS 'Users table with RLS disabled for straightforward authentication';
COMMENT ON TABLE organizations IS 'Organizations table with RLS disabled';
COMMENT ON TABLE organization_memberships IS 'Organization memberships table with RLS disabled';
COMMENT ON TABLE individuals IS 'Individual profiles table with RLS disabled';
COMMENT ON TABLE user_contexts IS 'User contexts table with RLS disabled';
