/**
 * API Security Utilities
 * Comprehensive security middleware and utilities for API endpoints
 */

import { NextRequest, NextResponse } from 'next/server';
import { createError, ErrorType } from '@/lib/utils';
import { handleApiError } from '@/lib/api-error-handler';

// Rate limiting configuration
interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
}

// Rate limiting store (in-memory for development, use Redis in production)
const rateLimitStore = new Map<string, { count: number; resetTime: number }>();

// Default rate limit configurations - DISABLED FOR DEVELOPMENT
export const RATE_LIMITS = {
  DEFAULT: { windowMs: 15 * 60 * 1000, maxRequests: 10000 }, // Effectively unlimited
  AUTH: { windowMs: 15 * 60 * 1000, maxRequests: 10000 }, // Effectively unlimited
  UPLOAD: { windowMs: 60 * 1000, maxRequests: 10000 }, // Effectively unlimited
  ADMIN: { windowMs: 60 * 1000, maxRequests: 10000 }, // Effectively unlimited
  PUBLIC: { windowMs: 60 * 1000, maxRequests: 10000 }, // Effectively unlimited
} as const;

/**
 * Rate limiting middleware
 */
export function withRateLimit(config: RateLimitConfig = RATE_LIMITS.DEFAULT) {
  return function rateLimitMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      // Check if rate limiting is disabled
      if (process.env.DISABLE_RATE_LIMITING === 'true') {
        return handler(request);
      }

      try {
        // Get client identifier (IP + User-Agent for better uniqueness)
        const clientId = getClientIdentifier(request);
        const now = Date.now();
        
        // Clean up expired entries
        cleanupExpiredEntries(now);
        
        // Get current rate limit data
        const rateLimitData = rateLimitStore.get(clientId) || { count: 0, resetTime: now + config.windowMs };
        
        // Reset if window has expired
        if (now > rateLimitData.resetTime) {
          rateLimitData.count = 0;
          rateLimitData.resetTime = now + config.windowMs;
        }
        
        // Check if limit exceeded
        if (rateLimitData.count >= config.maxRequests) {
          const resetTimeSeconds = Math.ceil((rateLimitData.resetTime - now) / 1000);
          
          const rateLimitError = createError(
            ErrorType.RATE_LIMIT,
            `Rate limit exceeded: ${config.maxRequests} requests per ${config.windowMs / 1000} seconds`,
            `Too many requests. Please wait ${resetTimeSeconds} seconds before trying again.`,
            {
              limit: config.maxRequests,
              windowMs: config.windowMs,
              retryAfter: resetTimeSeconds,
              clientId: clientId.substring(0, 10) + '...' // Partial ID for debugging
            }
          );
          
          return handleApiError(rateLimitError, 'Rate Limit Middleware');
        }
        
        // Increment counter
        rateLimitData.count++;
        rateLimitStore.set(clientId, rateLimitData);
        
        // Execute the handler
        const response = await handler(request);
        
        // Add rate limit headers
        response.headers.set('X-RateLimit-Limit', config.maxRequests.toString());
        response.headers.set('X-RateLimit-Remaining', (config.maxRequests - rateLimitData.count).toString());
        response.headers.set('X-RateLimit-Reset', rateLimitData.resetTime.toString());
        
        return response;
        
      } catch (error) {
        return handleApiError(error, 'Rate Limit Middleware Error');
      }
    };
  };
}

/**
 * Security headers middleware
 */
export function withSecurityHeaders(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async function (request: NextRequest): Promise<NextResponse> {
    try {
      const response = await handler(request);
      
      // Add comprehensive security headers
      const securityHeaders = {
        // Content Security Policy
        'Content-Security-Policy': [
          "default-src 'self'",
          "script-src 'self' 'unsafe-inline' 'unsafe-eval' https://cdn.jsdelivr.net",
          "style-src 'self' 'unsafe-inline' https://fonts.googleapis.com",
          "font-src 'self' https://fonts.gstatic.com",
          "img-src 'self' data: blob: https:",
          "media-src 'self' blob:",
          "connect-src 'self' https://*.supabase.co wss://*.supabase.co",
          "frame-src 'none'",
          "object-src 'none'",
          "base-uri 'self'"
        ].join('; '),
        
        // HTTP Strict Transport Security
        'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload',
        
        // Prevent MIME type sniffing
        'X-Content-Type-Options': 'nosniff',
        
        // Prevent clickjacking
        'X-Frame-Options': 'DENY',
        
        // XSS Protection
        'X-XSS-Protection': '1; mode=block',
        
        // Referrer Policy
        'Referrer-Policy': 'strict-origin-when-cross-origin',
        
        // Permissions Policy
        'Permissions-Policy': [
          'camera=()',
          'microphone=()',
          'geolocation=()',
          'payment=()',
          'usb=()',
          'magnetometer=()',
          'accelerometer=()',
          'gyroscope=()'
        ].join(', '),
        
        // Remove server information
        'Server': 'BPO-Training-Platform',
        
        // API specific headers
        'X-API-Version': '1.0',
        'X-Request-ID': generateRequestId()
      };
      
      // Apply security headers
      Object.entries(securityHeaders).forEach(([key, value]) => {
        response.headers.set(key, value);
      });
      
      return response;
      
    } catch (error) {
      return handleApiError(error, 'Security Headers Middleware Error');
    }
  };
}

/**
 * Request size limit middleware
 */
export function withRequestSizeLimit(maxSizeBytes: number = 1024 * 1024) { // 1MB default
  return function requestSizeLimitMiddleware(
    handler: (request: NextRequest) => Promise<NextResponse>
  ) {
    return async function (request: NextRequest): Promise<NextResponse> {
      try {
        // Check Content-Length header
        const contentLength = request.headers.get('content-length');
        
        if (contentLength && parseInt(contentLength) > maxSizeBytes) {
          const sizeLimitError = createError(
            ErrorType.VALIDATION,
            `Request payload too large: ${contentLength} bytes exceeds limit of ${maxSizeBytes} bytes`,
            `Request too large. Maximum size allowed is ${Math.round(maxSizeBytes / 1024)}KB.`,
            {
              maxSizeBytes,
              actualSizeBytes: parseInt(contentLength),
              maxSizeKB: Math.round(maxSizeBytes / 1024)
            }
          );
          
          return handleApiError(sizeLimitError, 'Request Size Limit Middleware');
        }
        
        return await handler(request);
        
      } catch (error) {
        return handleApiError(error, 'Request Size Limit Middleware Error');
      }
    };
  };
}

/**
 * Input sanitization middleware
 */
export function withInputSanitization(
  handler: (request: NextRequest) => Promise<NextResponse>
) {
  return async function (request: NextRequest): Promise<NextResponse> {
    try {
      // Clone the request to modify it
      const url = new URL(request.url);
      
      // Sanitize query parameters
      const sanitizedParams = new URLSearchParams();
      url.searchParams.forEach((value, key) => {
        sanitizedParams.set(key, sanitizeInput(value));
      });
      
      // Create new URL with sanitized params
      const sanitizedUrl = new URL(url.pathname, url.origin);
      sanitizedUrl.search = sanitizedParams.toString();
      
      // Create new request with sanitized URL
      const sanitizedRequest = new NextRequest(sanitizedUrl, {
        method: request.method,
        headers: request.headers,
        body: request.body
      });
      
      return await handler(sanitizedRequest);
      
    } catch (error) {
      return handleApiError(error, 'Input Sanitization Middleware Error');
    }
  };
}

/**
 * Comprehensive API security wrapper
 */
export function withApiSecurity(
  handler: (request: NextRequest) => Promise<NextResponse>,
  options: {
    rateLimit?: RateLimitConfig;
    maxRequestSize?: number;
    skipSecurityHeaders?: boolean;
    skipInputSanitization?: boolean;
  } = {}
) {
  let securedHandler = handler;
  
  // Apply middleware in reverse order (last applied = first executed)
  if (!options.skipInputSanitization) {
    securedHandler = withInputSanitization(securedHandler);
  }
  
  if (options.maxRequestSize) {
    securedHandler = withRequestSizeLimit(options.maxRequestSize)(securedHandler);
  }
  
  if (!options.skipSecurityHeaders) {
    securedHandler = withSecurityHeaders(securedHandler);
  }
  
  if (options.rateLimit) {
    securedHandler = withRateLimit(options.rateLimit)(securedHandler);
  }
  
  return securedHandler;
}

// Helper functions
function getClientIdentifier(request: NextRequest): string {
  const ip = request.ip || 
    request.headers.get('x-forwarded-for')?.split(',')[0] || 
    request.headers.get('x-real-ip') || 
    'unknown';
  const userAgent = request.headers.get('user-agent') || 'unknown';
  return `${ip}:${userAgent}`;
}

function cleanupExpiredEntries(now: number): void {
  for (const [key, data] of rateLimitStore.entries()) {
    if (now > data.resetTime) {
      rateLimitStore.delete(key);
    }
  }
}

function sanitizeInput(input: string): string {
  return input
    .replace(/[<>]/g, '') // Remove potential HTML tags
    .replace(/javascript:/gi, '') // Remove javascript: protocol
    .replace(/on\w+=/gi, '') // Remove event handlers
    .trim();
}

function generateRequestId(): string {
  return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
}

// File upload security utilities
export const FILE_UPLOAD_SECURITY = {
  // Maximum file sizes by type
  MAX_SIZES: {
    image: 5 * 1024 * 1024, // 5MB
    document: 10 * 1024 * 1024, // 10MB
    video: 100 * 1024 * 1024, // 100MB
    audio: 50 * 1024 * 1024, // 50MB
  },
  
  // Allowed MIME types
  ALLOWED_TYPES: {
    image: ['image/jpeg', 'image/png', 'image/gif', 'image/webp'],
    document: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain'
    ],
    video: ['video/mp4', 'video/webm', 'video/quicktime'],
    audio: ['audio/mpeg', 'audio/wav', 'audio/ogg']
  },
  
  // Dangerous file extensions to block
  BLOCKED_EXTENSIONS: [
    '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
    '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl', '.sh'
  ]
};

/**
 * Validate uploaded file security
 */
export function validateFileUpload(
  file: File,
  allowedCategory: keyof typeof FILE_UPLOAD_SECURITY.ALLOWED_TYPES
): { isValid: boolean; error?: string } {
  // Check file size
  const maxSize = FILE_UPLOAD_SECURITY.MAX_SIZES[allowedCategory];
  if (file.size > maxSize) {
    return {
      isValid: false,
      error: `File too large. Maximum size: ${Math.round(maxSize / (1024 * 1024))}MB`
    };
  }
  
  // Check MIME type
  const allowedTypes = FILE_UPLOAD_SECURITY.ALLOWED_TYPES[allowedCategory];
  if (!allowedTypes.includes(file.type)) {
    return {
      isValid: false,
      error: `Invalid file type. Allowed types: ${allowedTypes.join(', ')}`
    };
  }
  
  // Check file extension
  const extension = '.' + file.name.split('.').pop()?.toLowerCase();
  if (FILE_UPLOAD_SECURITY.BLOCKED_EXTENSIONS.includes(extension)) {
    return {
      isValid: false,
      error: 'File type not allowed for security reasons'
    };
  }
  
  // Additional security checks
  if (file.name.includes('..') || file.name.includes('/') || file.name.includes('\\')) {
    return {
      isValid: false,
      error: 'Invalid file name'
    };
  }
  
  return { isValid: true };
}
