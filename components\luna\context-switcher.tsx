'use client';

import { useState } from 'react';
import { ChevronDownIcon, UserIcon, BuildingOfficeIcon } from '@heroicons/react/24/outline';
import { useLunaAuth, useCurrentContext } from '@/hooks/use-luna-auth';

interface ContextSwitcherProps {
  className?: string;
}

export function ContextSwitcher({ className = '' }: ContextSwitcherProps) {
  const { user, switchContext } = useLunaAuth();
  const { context, availableOrganizations } = useCurrentContext();
  const [isOpen, setIsOpen] = useState(false);
  const [switching, setSwitching] = useState(false);

  if (!user) return null;

  // Don't show context switcher for users who can't switch contexts
  const canSwitchToIndividual = user.role === 'individual' || user.isPlatformAdmin;
  const hasOrganizations = availableOrganizations.length > 0;

  if (!canSwitchToIndividual && !hasOrganizations) {
    return null;
  }

  const handleContextSwitch = async (
    contextType: 'individual' | 'organization',
    organizationId?: string
  ) => {
    setSwitching(true);
    setIsOpen(false);
    
    try {
      await switchContext(contextType, organizationId);
    } catch (error) {
      console.error('Context switch failed:', error);
    } finally {
      setSwitching(false);
    }
  };

  const currentContextDisplay = () => {
    if (context?.type === 'organization') {
      const org = availableOrganizations.find(o => o.organization_id === context.organization_id);
      return {
        icon: <BuildingOfficeIcon className="h-4 w-4" />,
        label: org?.organization_name || 'Organization',
        subtitle: 'Organization Mode'
      };
    } else {
      return {
        icon: <UserIcon className="h-4 w-4" />,
        label: user.full_name || 'Individual',
        subtitle: 'Individual Mode'
      };
    }
  };

  const currentDisplay = currentContextDisplay();

  return (
    <div className={`relative ${className}`}>
      <button
        onClick={() => setIsOpen(!isOpen)}
        disabled={switching}
        className="flex items-center space-x-2 px-3 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 disabled:opacity-50"
      >
        {currentDisplay.icon}
        <div className="flex flex-col items-start">
          <span className="text-sm font-medium">{currentDisplay.label}</span>
          <span className="text-xs text-gray-500">{currentDisplay.subtitle}</span>
        </div>
        <ChevronDownIcon className="h-4 w-4 text-gray-400" />
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-64 bg-white border border-gray-200 rounded-md shadow-lg z-50">
          <div className="py-1">
            {/* Individual Context Option */}
            {canSwitchToIndividual && (
              <button
                onClick={() => handleContextSwitch('individual')}
                className={`w-full flex items-center px-4 py-3 text-sm hover:bg-gray-50 ${
                  context?.type === 'individual' ? 'bg-blue-50 text-blue-700' : 'text-gray-700'
                }`}
              >
                <UserIcon className="h-5 w-5 mr-3" />
                <div className="flex flex-col items-start">
                  <span className="font-medium">Individual Mode</span>
                  <span className="text-xs text-gray-500">Personal learning and profile</span>
                </div>
                {context?.type === 'individual' && (
                  <div className="ml-auto h-2 w-2 bg-blue-600 rounded-full"></div>
                )}
              </button>
            )}

            {/* Organization Context Options */}
            {hasOrganizations && (
              <>
                {canSwitchToIndividual && (
                  <div className="border-t border-gray-100 my-1"></div>
                )}
                <div className="px-4 py-2">
                  <span className="text-xs font-medium text-gray-500 uppercase tracking-wide">
                    Organizations
                  </span>
                </div>
                {availableOrganizations.map((org) => (
                  <button
                    key={org.organization_id}
                    onClick={() => handleContextSwitch('organization', org.organization_id)}
                    className={`w-full flex items-center px-4 py-3 text-sm hover:bg-gray-50 ${
                      context?.type === 'organization' && context.organization_id === org.organization_id
                        ? 'bg-blue-50 text-blue-700'
                        : 'text-gray-700'
                    }`}
                  >
                    <BuildingOfficeIcon className="h-5 w-5 mr-3" />
                    <div className="flex flex-col items-start">
                      <span className="font-medium">{org.organization_name}</span>
                      <span className="text-xs text-gray-500 capitalize">{org.role} access</span>
                    </div>
                    {context?.type === 'organization' && context.organization_id === org.organization_id && (
                      <div className="ml-auto h-2 w-2 bg-blue-600 rounded-full"></div>
                    )}
                  </button>
                ))}
              </>
            )}
          </div>
        </div>
      )}

      {/* Overlay to close dropdown */}
      {isOpen && (
        <div
          className="fixed inset-0 z-40"
          onClick={() => setIsOpen(false)}
        />
      )}
    </div>
  );
}

// Compact version for mobile or tight spaces
export function CompactContextSwitcher({ className = '' }: ContextSwitcherProps) {
  const { user } = useLunaAuth();
  const { context } = useCurrentContext();

  if (!user) return null;

  const currentDisplay = context?.type === 'organization' ? (
    <BuildingOfficeIcon className="h-5 w-5 text-blue-600" />
  ) : (
    <UserIcon className="h-5 w-5 text-green-600" />
  );

  return (
    <div className={`flex items-center ${className}`}>
      {currentDisplay}
      <span className="ml-2 text-sm font-medium text-gray-700">
        {context?.type === 'organization' ? 'Org' : 'Individual'}
      </span>
    </div>
  );
}
