import { createClient } from '@supabase/supabase-js'
import { NextRequest, NextResponse } from 'next/server'

export async function POST(request: NextRequest) {
  try {
    // Create service role client to bypass all RLS
    const supabaseService = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false
        }
      }
    )

    console.log('🔧 Updating interview status to pending_scheduling...')

    // Update the existing interview to pending_scheduling status
    const { data: updatedInterview, error: updateError } = await supabaseService
      .from('interviews')
      .update({
        status: 'pending_scheduling',
        feedback: {
          interview_type: 'video_call',
          invitation_message: 'We would like to invite you for an interview. Please select your preferred time.',
          proposed_times: [
            {
              start_time: new Date(Date.now() + 24 * 60 * 60 * 1000).toISOString(), // Tomorrow
              end_time: new Date(Date.now() + 24 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString() // +1 hour
            },
            {
              start_time: new Date(Date.now() + 48 * 60 * 60 * 1000).toISOString(), // Day after tomorrow
              end_time: new Date(Date.now() + 48 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString() // +1 hour
            },
            {
              start_time: new Date(Date.now() + 72 * 60 * 60 * 1000).toISOString(), // 3 days from now
              end_time: new Date(Date.now() + 72 * 60 * 60 * 1000 + 60 * 60 * 1000).toISOString() // +1 hour
            }
          ]
        }
      })
      .eq('id', '47719ed4-97c5-447d-bd06-c6cebff734b9') // The interview ID from the logs
      .select()

    console.log('🔧 Update result:', { data: updatedInterview, error: updateError })

    if (updateError) {
      return NextResponse.json({
        error: 'Failed to update interview',
        details: updateError
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      message: 'Interview status updated to pending_scheduling',
      data: updatedInterview
    })

  } catch (error) {
    console.error('🔧 Error updating interview status:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}

export async function GET(request: NextRequest) {
  return POST(request)
}
