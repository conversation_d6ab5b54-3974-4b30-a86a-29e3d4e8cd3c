# Quick Reference - Error Logging System

## 🚨 When You Encounter an Error

### 1. Immediate Actions
```bash
# Document the error immediately
node scripts/log-error.js

# Or manually add to error-database.md
```

### 2. Search for Similar Issues
```bash
# Search the error database
grep -r "error message" docs/error-log/
```

### 3. Common Error Patterns

#### Supabase Import Error
```
Error: (0 , l.createClient) is not a function
Fix: Use correct import packages
```

#### Temporal Dead Zone
```
Error: Cannot access 'X' before initialization
Fix: Move function definitions to top of component
```

#### JSX Syntax Error
```
Error: Unexpected token 'div'. Expected jsx identifier
Fix: Check for missing closing tags
```

## 🔧 Quick Fixes

### Supabase Imports
```javascript
// ✅ CORRECT
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
```

### Error Handling Pattern
```javascript
try {
  // Operation
} catch (error) {
  console.error('Operation failed:', error)
  return NextResponse.json({
    error: 'Brief message',
    details: error.message
  }, { status: 500 })
}
```

### Component Safety
```javascript
// ✅ NULL CHECKS
if (!data) return <div>Loading...</div>

// ✅ FUNCTION ORDER
const helperFunction = () => { ... }  // Define first
const component = () => {
  // Use helper function here
}
```

## 📁 File Structure
```
docs/error-log/
├── error-database.md      # Main error log
├── prevention-checklist.md # Prevention guide
├── debugging-guide.md     # Debug procedures
└── categories/           # Categorized errors
    ├── api-errors.md
    ├── import-errors.md
    └── ...
```

## 🎯 Emergency Debugging

### 1. API 500 Error
```javascript
// Add to API route:
console.log('🚀 API Entry:', { method: request.method, url: request.url })
```

### 2. Frontend Crash
```javascript
// Add to component:
console.log('🔍 Component state:', { props, state, data })
```

### 3. Build Failure
```bash
npm run build  # Check build locally
npm run lint   # Check for linting errors
```

## 📞 Escalation

If you can't solve an error:
1. Document what you've tried
2. Add detailed logs
3. Create minimal reproduction
4. Ask for help with context

## 🏷️ Common Tags

- `#api` - API route issues
- `#supabase` - Supabase related
- `#import` - Import/dependency issues
- `#frontend` - UI/component issues
- `#deployment` - Build/deploy issues
- `#database` - Database operations
- `#auth` - Authentication issues

## 📊 Severity Levels

- **Critical**: App completely broken
- **High**: Core feature broken
- **Medium**: Feature partially broken
- **Low**: Minor issue or cosmetic

## 🚀 Quick Commands

```bash
# Log new error
node scripts/log-error.js

# Search errors
grep -r "search term" docs/error-log/

# Check prevention list
cat docs/error-log/prevention-checklist.md
```
