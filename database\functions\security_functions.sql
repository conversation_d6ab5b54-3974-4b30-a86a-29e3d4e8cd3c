-- SECURITY DEFINER FUNCTIONS FOR RLS POLICIES
-- These functions bypass <PERSON><PERSON> safely to prevent infinite recursion

-- Function to check if user is platform admin
CREATE OR REPLACE FUNCTION public.is_platform_admin(user_id_param UUID)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.users
    WHERE id = user_id_param AND role = 'admin'
  );
END;
$$;

-- Function to check if user is BPO admin for a specific BPO
CREATE OR REPLACE FUNCTION public.is_bpo_admin(user_id_param UUID, bpo_id_param UUID)
RETURNS boolean
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM public.bpo_teams
    WHERE user_id = user_id_param 
    AND bpo_id = bpo_id_param 
    AND role = 'admin'
  );
END;
$$;

-- Function to get user's BPO memberships (with proper enum casting)
CREATE OR REPLACE FUNCTION public.get_user_bpo_memberships(user_id_param UUID)
RETURNS TABLE(bpo_id UUID, role TEXT)
LANGUAGE plpgsql
SECURITY DEFINER
STABLE
AS $$
BEGIN
  RETURN QUERY
  SELECT bt.bpo_id, bt.role::TEXT
  FROM public.bpo_teams bt
  WHERE bt.user_id = user_id_param;
END;
$$;

-- Grant execute permissions on functions to authenticated users
GRANT EXECUTE ON FUNCTION public.is_platform_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.is_bpo_admin(UUID, UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION public.get_user_bpo_memberships(UUID) TO authenticated;
