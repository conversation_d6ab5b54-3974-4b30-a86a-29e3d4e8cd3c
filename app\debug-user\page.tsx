import { getServerAuthUser } from "@/lib/auth"
import { createServerClient } from "@/lib/supabase-server"

// Force dynamic rendering for this page due to authentication requirements
export const dynamic = 'force-dynamic'

export default async function DebugUser() {
  const authResult = await getServerAuthUser()
  const supabase = await createServerClient()
  
  // Get raw user data from database
  let rawUserData = null
  if (authResult.user) {
    const { data } = await supabase
      .from('users')
      .select('*')
      .eq('id', authResult.user.id)
      .single()
    rawUserData = data
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4">User Debug Information</h1>
      
      <div className="space-y-4">
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-semibold">Auth Result:</h2>
          <pre className="text-sm">{JSON.stringify(authResult, null, 2)}</pre>
        </div>
        
        <div className="bg-gray-100 p-4 rounded">
          <h2 className="font-semibold">Raw Database User Data:</h2>
          <pre className="text-sm">{JSON.stringify(rawUserData, null, 2)}</pre>
        </div>
        
        <div className="bg-blue-100 p-4 rounded">
          <h2 className="font-semibold">First Name Extraction Test:</h2>
          <p>Full Name: {authResult.user?.full_name || 'null'}</p>
          <p>First Name: {authResult.user?.full_name ? authResult.user.full_name.split(' ')[0] : 'fallback to email'}</p>
          <p>Email: {authResult.user?.email}</p>
        </div>
      </div>
    </div>
  )
}
