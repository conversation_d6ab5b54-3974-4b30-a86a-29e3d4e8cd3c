import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>cle, PhoneCall, Trophy, Zap, Bar<PERSON>hart3 } from "lucide-react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Progress } from "@/components/ui/progress"
import { WelcomeMessage } from "@/components/welcome-message"
import { SkillRadarChart } from "@/components/skill-radar-chart"
import { LearningProgressChart } from "@/components/charts/learning-progress-chart"
import Link from "next/link"

// Utility function to strip HTML and truncate text
function stripHtmlAndTruncate(html: string, maxLength: number = 100): string {
  // Remove HTML tags
  const text = html.replace(/<\/?[^>]+(>|$)/g, " ").replace(/\s+/g, " ").trim();
  // Truncate and add ellipsis if needed
  return text.length > maxLength ? `${text.substring(0, maxLength)}...` : text;
}

interface ModuleData {
  id: string
  title: string
  description: string
  currentLesson: string
  nextLessonId?: string
  progress: number
  duration: number
  color: string
}

interface DashboardData {
  userInfo: {
    name: string
    email: string
    avatar_url?: string | null
  }
  trainingStats: {
    totalModules: number
    completedModules: number
    inProgressModules: number
    progressPercentage: number
    badges: number
    callPracticeHours: number
    callPracticeScore: number
    completedAssessments: number
    totalAssessments: number
  }
  inProgressModules: ModuleData[]
  notificationData?: {
    newJobMatches: number
    recentBadges: number
    upcomingInterviews: number
  }
}

export function DashboardOverview({ data }: { data: DashboardData }) {
  const { userInfo, trainingStats, inProgressModules, notificationData } = data

  return (
    <div className="space-y-8 w-full">
      <WelcomeMessage
        name={userInfo.name}
        progress={trainingStats.progressPercentage}
        notificationData={notificationData}
        assessmentData={{
          completed: trainingStats.completedAssessments,
          total: trainingStats.totalAssessments
        }}
      />

      {/* KPI Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-0 bg-white dark:bg-gray-900 rounded-xl shadow-sm overflow-hidden w-full">
        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-blue-500 to-indigo-600 text-white shadow-lg shadow-blue-500/20">
              <CheckCircle className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Training Progress</div>
              <div className="text-3xl font-bold mt-1">{trainingStats.progressPercentage}%</div>
              <p className="text-xs text-muted-foreground">
                {trainingStats.completedModules} of {trainingStats.totalModules} modules completed
              </p>
            </div>
          </div>
          <Progress value={trainingStats.progressPercentage} className="h-1.5 mt-4" />
        </div>

        <div className="p-6 border-b md:border-b-0 md:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-green-500 to-emerald-600 text-white shadow-lg shadow-green-500/20">
              <BookOpen className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Active Modules</div>
              <div className="text-3xl font-bold mt-1">{trainingStats.inProgressModules}</div>
              <div className="flex items-center gap-2 mt-1">
                <div className="h-2 w-2 rounded-full bg-blue-500"></div>
                <p className="text-xs text-muted-foreground">
                  {trainingStats.inProgressModules} of {trainingStats.totalModules} in progress
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6 border-b lg:border-b-0 lg:border-r border-gray-100 dark:border-gray-800">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-amber-500 to-orange-600 text-white shadow-lg shadow-amber-500/20">
              <Trophy className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">Achievements</div>
              <div className="text-3xl font-bold mt-1">{trainingStats.completedAssessments}</div>
              <div className="flex items-center gap-2 mt-1">
                <div className="h-2 w-2 rounded-full bg-amber-500"></div>
                <p className="text-xs text-muted-foreground">
                  {trainingStats.completedAssessments} of {trainingStats.totalAssessments} assessments
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="p-6">
          <div className="flex items-center gap-4">
            <div className="flex h-12 w-12 items-center justify-center rounded-full bg-gradient-to-br from-indigo-500 to-purple-600 text-white shadow-lg shadow-indigo-500/20">
              <PhoneCall className="h-6 w-6" />
            </div>
            <div>
              <div className="text-sm font-medium text-gray-500 dark:text-gray-400">AI Call Practice</div>
              <div className="text-3xl font-bold mt-1">{trainingStats.callPracticeHours.toFixed(1)}</div>
              <div className="flex items-center gap-2 mt-1">
                <div className="h-2 w-2 rounded-full bg-green-500"></div>
                <p className="text-xs text-muted-foreground">
                  {trainingStats.callPracticeScore}/100 Avg. Score
                </p>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Current Training Section */}
      <div>
        <div className="flex items-center justify-between mb-6">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Your Learning Journey</h2>
            <p className="text-muted-foreground">Continue your training path</p>
          </div>
          <Button className="bg-gradient-primary" asChild>
            <Link href={
              inProgressModules.length > 0 && inProgressModules[0].nextLessonId
                ? `/prospect/training/${inProgressModules[0].id}/lessons/${inProgressModules[0].nextLessonId}`
                : "/prospect/training"
            }>
              <Zap className="mr-2 h-4 w-4" />
              Continue Training
            </Link>
          </Button>
        </div>

        <div className="grid gap-6 md:grid-cols-3">
          {inProgressModules.map(module => {
            const colorMap = {
              blue: {
                bg: "from-blue-500/10 to-indigo-500/10 dark:from-blue-500/5 dark:to-indigo-500/5",
                icon: "from-blue-500 to-indigo-600",
                shadow: "shadow-blue-500/20",
                button: "from-blue-600 to-indigo-600"
              },
              green: {
                bg: "from-green-500/10 to-emerald-500/10 dark:from-green-500/5 dark:to-emerald-500/5",
                icon: "from-green-500 to-emerald-600",
                shadow: "shadow-green-500/20",
                button: "from-green-600 to-emerald-600"
              },
              purple: {
                bg: "from-purple-500/10 to-pink-500/10 dark:from-purple-500/5 dark:to-pink-500/5",
                icon: "from-purple-500 to-pink-600",
                shadow: "shadow-purple-500/20",
                button: "from-purple-600 to-pink-600"
              }
            }

            const colors = colorMap[module.color as keyof typeof colorMap] || colorMap.blue

            const continueUrl = module.nextLessonId
              ? `/prospect/training/${module.id}/lessons/${module.nextLessonId}`
              : `/prospect/training/${module.id}`

            return (
              <Link href={continueUrl} key={module.id} className="block">
                <Card className="border-none shadow-sm overflow-hidden transition-all hover:shadow-md">
                  <div className={`bg-gradient-to-r ${colors.bg} p-4`}>
                    <div className="flex items-center gap-3">
                      <div className={`flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-br ${colors.icon} text-white shadow-lg ${colors.shadow}`}>
                        <BookOpen className="h-5 w-5" />
                      </div>
                      <div>
                        <h3 className="font-medium">{module.title}</h3>
                        <div className="flex items-center text-xs text-muted-foreground">
                          {module.duration} hours total • {module.progress}% complete
                        </div>
                      </div>
                    </div>
                    <Progress value={module.progress} className="h-1.5 mt-3" />
                  </div>
                  <CardContent className="p-4">
                    <p className="text-sm font-medium">
                      {module.progress === 0 ? "Start with:" : "Continue with:"} {module.currentLesson}
                    </p>
                    <p className="mt-1 text-xs text-muted-foreground">{stripHtmlAndTruncate(module.description, 80)}</p>
                    <div className={`w-full mt-4 py-2 px-3 text-center text-sm font-medium text-white rounded-md bg-gradient-to-r ${colors.button}`}>
                      {module.progress === 0 ? "Start Learning" : "Continue Learning"}
                    </div>
                  </CardContent>
                </Card>
              </Link>
            )
          })}
        </div>
      </div>

      {/* Analytics Section */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <div>
            <h2 className="text-2xl font-bold tracking-tight">Analytics & Insights</h2>
            <p className="text-muted-foreground">Track your progress and performance</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="bg-white dark:bg-gray-800">
              Weekly
            </Button>
            <Button variant="outline" size="sm" className="bg-white dark:bg-gray-800">
              Monthly
            </Button>
          </div>
        </div>

        <div className="grid gap-6 md:grid-cols-2">
          <Card className="border-none shadow-sm overflow-hidden">
            <CardHeader className="pb-2 border-b">
              <CardTitle>Skill Proficiency</CardTitle>
              <CardDescription>Your strengths and areas for improvement</CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <SkillRadarChart />
            </CardContent>
          </Card>

          <Card className="border-none shadow-sm overflow-hidden">
            <CardHeader className="pb-2 border-b">
              <CardTitle>Learning Progress</CardTitle>
              <CardDescription>Your training activity over time</CardDescription>
            </CardHeader>
            <CardContent className="pt-4">
              <LearningProgressChart />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
