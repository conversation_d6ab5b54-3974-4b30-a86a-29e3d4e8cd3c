# Hard Logout Feature Documentation

## Overview

The Hard Logout feature allows super administrators to forcibly log out users from the BPO Training Platform. This is a critical security feature that can be used in emergency situations such as security breaches, system maintenance, or when immediate user session termination is required.

## Features

### 1. Emergency Logout All Users
- **Purpose**: Immediately invalidate all user sessions except the admin performing the action
- **Use Cases**: 
  - Security breach or suspected unauthorized access
  - System maintenance requiring all users to be offline
  - Critical platform updates
  - Emergency situations requiring immediate user disconnection

### 2. Individual User Logout
- **Purpose**: Log out a specific user by invalidating their session
- **Use Cases**:
  - Suspicious user activity
  - Account compromise
  - Administrative action required for specific user

### 3. Session Monitoring
- **Purpose**: View currently active user sessions
- **Features**:
  - Shows users active in the last 24 hours
  - Displays last sign-in times
  - Provides user details (name, email)
  - Real-time session data refresh

## Implementation Details

### API Endpoints

#### POST `/api/admin/hard-logout`
Performs hard logout operations (admin only).

**Request Body:**
```json
{
  "action": "logout_all_users" | "logout_single_user",
  "targetUserId": "string" // Required for single user logout
}
```

**Response:**
```json
{
  "success": boolean,
  "message": "string",
  "loggedOutUsers": number,
  "errors": string[], // Optional, if any errors occurred
  "timestamp": "string",
  "performedBy": "string" // Admin email
}
```

#### GET `/api/admin/hard-logout`
Retrieves current session monitoring data (admin only).

**Response:**
```json
{
  "totalUsers": number,
  "recentlyActiveUsers": number,
  "users": [
    {
      "id": "string",
      "email": "string",
      "full_name": "string",
      "last_sign_in_at": "string"
    }
  ]
}
```

### Security Features

1. **Admin-Only Access**: Requires platform admin privileges
2. **Self-Protection**: Admins cannot logout themselves using this feature
3. **Audit Logging**: All hard logout actions are logged with admin details
4. **Confirmation Dialogs**: Critical actions require explicit confirmation
5. **Error Handling**: Graceful handling of partial failures

### Technical Implementation

#### Backend (Supabase Admin API)
- Uses Supabase Admin Client with service role key
- Leverages `auth.admin.signOut(userId)` for session invalidation
- Implements proper error handling and logging

#### Frontend Components
- **HardLogoutControl**: Main component for admin dashboard
- **AlertDialog**: Confirmation dialogs for critical actions
- **Real-time Updates**: Automatic refresh of session data after actions

## Usage Instructions

### For Super Administrators

#### Accessing the Feature
1. Navigate to the Admin Dashboard (`/admin`)
2. Scroll down to the "Session Management" section
3. The Hard Logout Control panel will be visible

#### Monitoring Active Sessions
1. Click the "Refresh" button to get current session data
2. View the list of recently active users (last 24 hours)
3. See user details and last sign-in times

#### Logging Out Individual Users
1. Find the user in the active sessions list
2. Click the logout button (🚪) next to their name
3. Confirm the action in the dialog
4. The user will be immediately logged out

#### Emergency Logout All Users
1. Click the red "Emergency Logout All Users" button
2. Read the warning dialog carefully
3. Confirm the action by clicking "Yes, Logout All Users"
4. All users (except yourself) will be immediately logged out

### User Experience After Hard Logout

When a user is forcibly logged out:
1. Their current session becomes invalid
2. On their next request, they receive an authentication error
3. They are automatically redirected to the login page
4. They must log in again to continue using the platform

## Security Considerations

### When to Use Hard Logout

**Appropriate Situations:**
- Confirmed security breach
- Suspicious activity across multiple accounts
- System maintenance requiring user disconnection
- Emergency platform updates
- Compliance requirements

**Inappropriate Situations:**
- Minor user issues that can be resolved individually
- Regular maintenance that doesn't require user disconnection
- Testing purposes (use test environments instead)

### Best Practices

1. **Document Usage**: Always document when and why hard logout was used
2. **Communicate**: Inform users about planned maintenance that requires logout
3. **Monitor Impact**: Check system logs after performing hard logout
4. **Verify Success**: Confirm that the intended users were logged out
5. **Follow Up**: Monitor for any issues after the action

## Testing

### Test Environment Setup
A test page is available at `/test-hard-logout` for development and testing purposes.

### Test Scenarios
1. **Permission Testing**: Verify only platform admins can access the feature
2. **Session Monitoring**: Test fetching and displaying active sessions
3. **Individual Logout**: Test logging out specific users
4. **Mass Logout**: Test emergency logout all users functionality
5. **Error Handling**: Test behavior with invalid requests or network issues

## Troubleshooting

### Common Issues

#### "Platform admin access required"
- **Cause**: Current user doesn't have platform admin role
- **Solution**: Ensure user has `role = 'admin'` in the users table

#### "Failed to logout user"
- **Cause**: User session may already be invalid or network issues
- **Solution**: Check user's actual login status and retry if needed

#### "Partial success with errors"
- **Cause**: Some users couldn't be logged out (already logged out, network issues)
- **Solution**: Review error details and retry for failed users if needed

### Monitoring and Logs

- All hard logout actions are logged to the console with admin details
- Check browser developer tools for detailed error messages
- Monitor Supabase logs for authentication-related issues

## Future Enhancements

Potential improvements for the hard logout feature:

1. **Scheduled Logout**: Ability to schedule logout at specific times
2. **Selective Logout**: Logout users based on criteria (role, last activity, etc.)
3. **Notification System**: Send notifications to users before forced logout
4. **Audit Dashboard**: Dedicated page for viewing logout history
5. **Integration**: Connect with monitoring systems for automated responses

## Support

For technical support or questions about the hard logout feature:
1. Check the troubleshooting section above
2. Review system logs for detailed error information
3. Contact the development team with specific error messages
4. Provide context about when and why the feature was used
