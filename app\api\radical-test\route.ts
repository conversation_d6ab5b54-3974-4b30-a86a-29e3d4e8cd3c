import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    console.log('🔥 RADICAL TEST: Starting basic Supabase connection test...');
    
    // Get environment variables directly
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;
    
    console.log('🔥 Environment check:', {
      hasUrl: !!supabaseUrl,
      hasAnonKey: !!supabaseKey,
      hasServiceKey: !!serviceKey,
      url: supabaseUrl
    });

    if (!supabaseUrl || !supabaseKey) {
      return NextResponse.json({
        error: 'Missing Supabase environment variables',
        details: { hasUrl: !!supabaseUrl, hasAnonKey: !!supabaseKey }
      }, { status: 500 });
    }

    // Test 1: Basic client creation with anon key
    console.log('🔥 TEST 1: Creating basic Supabase client...');
    const supabase = createClient(supabaseUrl, supabaseKey);
    console.log('🔥 TEST 1: Client created successfully');

    // Test 2: Try to connect to auth (this should work even without users)
    console.log('🔥 TEST 2: Testing auth connection...');
    const { data: authData, error: authError } = await supabase.auth.getSession();
    console.log('🔥 TEST 2 Result:', {
      hasSession: !!authData?.session,
      authError: authError?.message
    });

    // Test 3: Try basic database query (this is where it might fail)
    console.log('🔥 TEST 3: Testing basic database connection...');
    const { data: dbData, error: dbError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    console.log('🔥 TEST 3 Result:', {
      success: !dbError,
      error: dbError?.message,
      errorCode: dbError?.code,
      errorDetails: dbError?.details,
      data: dbData
    });

    // Test 4: Try with service role key if anon fails
    if (dbError && serviceKey) {
      console.log('🔥 TEST 4: Trying with service role key...');
      const adminClient = createClient(supabaseUrl, serviceKey);
      const { data: adminData, error: adminError } = await adminClient
        .from('users')
        .select('count')
        .limit(1);
      
      console.log('🔥 TEST 4 Result:', {
        success: !adminError,
        error: adminError?.message,
        errorCode: adminError?.code,
        data: adminData
      });
    }

    // Test 5: Try to sign in with the working user
    console.log('🔥 TEST 5: Testing sign in with working user...');
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'BeeMO5317'
    });
    
    console.log('🔥 TEST 5 Result:', {
      success: !signInError,
      error: signInError?.message,
      errorStatus: signInError?.status,
      hasUser: !!signInData?.user,
      hasSession: !!signInData?.session
    });

    return NextResponse.json({
      message: 'Radical test completed - check server logs for details',
      summary: {
        environmentOk: !!supabaseUrl && !!supabaseKey,
        authConnectionOk: !authError,
        databaseConnectionOk: !dbError,
        signInAttempted: true,
        signInOk: !signInError
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('🔥 RADICAL TEST FAILED:', error);
    return NextResponse.json({
      error: 'Radical test failed with exception',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
