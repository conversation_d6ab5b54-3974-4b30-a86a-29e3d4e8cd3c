import { NextRequest } from 'next/server';
import { getAuthenticatedUser } from '@/lib/auth';
import { 
  createApiSuccessResponse, 
  handleApiError, 
  validateRequestBody,
  withApiErrorHandler 
} from '@/lib/api-error-handler';
import { createAdminClient } from '@/lib/supabase';

/**
 * Example API route demonstrating standardized error handling
 * GET /api/example-standardized - Get user's training modules
 */
export async function GET(request: NextRequest) {
  return withApiErrorHandler(async () => {
    // Authenticate user
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      throw new Error('Authentication required');
    }

    const user = authResult.user;
    const supabase = createAdminClient();

    // Fetch training modules with error handling
    const { data: modules, error } = await supabase
      .from('training_modules')
      .select('id, title, description, status, duration_minutes')
      .eq('status', 'published')
      .order('created_at', { ascending: false });

    if (error) {
      throw error; // Will be handled by handleSupabaseApiError
    }

    return {
      modules: modules || [],
      user: {
        id: user.id,
        email: user.email,
        role: user.role
      },
      total: modules?.length || 0
    };
  }, 'GET /api/example-standardized')();
}

/**
 * Example API route demonstrating POST with validation
 * POST /api/example-standardized - Create a new training module
 */
export async function POST(request: NextRequest) {
  return withApiErrorHandler(async () => {
    // Authenticate user and require admin access
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      throw new Error('Authentication required');
    }

    if (!authResult.user.isAdmin) {
      throw new Error('Admin access required');
    }

    const user = authResult.user;
    
    // Parse and validate request body
    const body = await request.json();
    const validatedData = validateRequestBody(body, [
      'title',
      'description',
      'duration_minutes'
    ], 'POST /api/example-standardized');

    const supabase = createAdminClient();

    // Create training module
    const { data: module, error } = await supabase
      .from('training_modules')
      .insert({
        title: validatedData.title,
        description: validatedData.description,
        duration_minutes: validatedData.duration_minutes,
        status: 'draft',
        created_by: user.id
      })
      .select()
      .single();

    if (error) {
      throw error; // Will be handled by handleSupabaseApiError
    }

    return {
      module,
      message: 'Training module created successfully'
    };
  }, 'POST /api/example-standardized')();
}

/**
 * Example API route demonstrating manual error handling
 * PUT /api/example-standardized/[id] - Update training module
 */
export async function PUT(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      return handleApiError(
        new Error('Authentication required'),
        'PUT /api/example-standardized'
      );
    }

    if (!authResult.user.isAdmin) {
      return handleApiError(
        new Error('Admin access required'),
        'PUT /api/example-standardized'
      );
    }

    const user = authResult.user;
    
    // Get module ID from URL
    const url = new URL(request.url);
    const moduleId = url.pathname.split('/').pop();
    
    if (!moduleId) {
      return handleApiError(
        new Error('Module ID is required'),
        'PUT /api/example-standardized'
      );
    }

    // Parse and validate request body
    const body = await request.json();
    const validatedData = validateRequestBody(body, [
      'title',
      'description'
    ], 'PUT /api/example-standardized');

    const supabase = createAdminClient();

    // Update training module
    const { data: module, error } = await supabase
      .from('training_modules')
      .update({
        title: validatedData.title,
        description: validatedData.description,
        duration_minutes: validatedData.duration_minutes,
        updated_at: new Date().toISOString()
      })
      .eq('id', moduleId)
      .select()
      .single();

    if (error) {
      return handleApiError(error, 'PUT /api/example-standardized');
    }

    if (!module) {
      return handleApiError(
        new Error('Training module not found'),
        'PUT /api/example-standardized'
      );
    }

    return createApiSuccessResponse({
      module,
      message: 'Training module updated successfully'
    });

  } catch (error) {
    return handleApiError(error, 'PUT /api/example-standardized');
  }
}

/**
 * Example API route demonstrating DELETE with error handling
 * DELETE /api/example-standardized/[id] - Delete training module
 */
export async function DELETE(request: NextRequest) {
  try {
    // Authenticate user
    const authResult = await getAuthenticatedUser();
    if (!authResult.user) {
      return handleApiError(
        new Error('Authentication required'),
        'DELETE /api/example-standardized'
      );
    }

    if (!authResult.user.isAdmin) {
      return handleApiError(
        new Error('Admin access required'),
        'DELETE /api/example-standardized'
      );
    }

    // Get module ID from URL
    const url = new URL(request.url);
    const moduleId = url.pathname.split('/').pop();
    
    if (!moduleId) {
      return handleApiError(
        new Error('Module ID is required'),
        'DELETE /api/example-standardized'
      );
    }

    const supabase = createAdminClient();

    // Check if module exists and can be deleted
    const { data: existingModule, error: fetchError } = await supabase
      .from('training_modules')
      .select('id, title')
      .eq('id', moduleId)
      .single();

    if (fetchError) {
      return handleApiError(fetchError, 'DELETE /api/example-standardized');
    }

    if (!existingModule) {
      return handleApiError(
        new Error('Training module not found'),
        'DELETE /api/example-standardized'
      );
    }

    // Delete the module
    const { error: deleteError } = await supabase
      .from('training_modules')
      .delete()
      .eq('id', moduleId);

    if (deleteError) {
      return handleApiError(deleteError, 'DELETE /api/example-standardized');
    }

    return createApiSuccessResponse({
      message: `Training module "${existingModule.title}" deleted successfully`,
      deletedId: moduleId
    });

  } catch (error) {
    return handleApiError(error, 'DELETE /api/example-standardized');
  }
}
