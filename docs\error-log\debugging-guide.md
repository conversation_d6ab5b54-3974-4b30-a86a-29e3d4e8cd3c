# Debugging Guide - BPO Training Platform

Step-by-step debugging procedures for common issues.

## 🔍 General Debugging Process

### 1. Identify the Problem
- [ ] What is the user trying to do?
- [ ] What error message appears?
- [ ] When does the error occur?
- [ ] Can you reproduce it consistently?

### 2. Gather Information
- [ ] Check browser console for errors
- [ ] Check Network tab for failed requests
- [ ] Check server logs (if available)
- [ ] Note the exact error message and stack trace

### 3. Isolate the Issue
- [ ] Test in different browsers
- [ ] Test with different user accounts
- [ ] Test with different data
- [ ] Check if it works in development vs production

### 4. Research Solutions
- [ ] Search the error database
- [ ] Check common patterns documentation
- [ ] Search online for similar issues
- [ ] Review recent code changes

## 🚨 API Errors (500, 400, etc.)

### Step 1: Check <PERSON>rowser Console
```javascript
// Look for these patterns:
- "Failed to load resource: the server responded with a status of 500"
- Detailed error messages in console.error logs
- Network tab showing request/response details
```

### Step 2: Add Client-Side Debugging
```javascript
// Add to your fetch calls:
try {
  const response = await fetch('/api/endpoint', { method: 'POST' })
  console.log('Response status:', response.status)
  console.log('Response headers:', Object.fromEntries(response.headers.entries()))
  
  const result = await response.json()
  console.log('Response body:', result)
} catch (error) {
  console.error('Fetch error:', error)
}
```

### Step 3: Add Server-Side Debugging
```javascript
// Add to your API routes:
export async function POST(request) {
  try {
    console.log('🚀 API called:', new Date().toISOString())
    console.log('📝 Request method:', request.method)
    console.log('📝 Request URL:', request.url)
    
    // Your code here
    
  } catch (error) {
    console.error('❌ API Error:', error)
    console.error('❌ Stack trace:', error.stack)
    return NextResponse.json({
      error: 'Internal server error',
      details: error.message,
      stack: error.stack
    }, { status: 500 })
  }
}
```

### Step 4: Check Common Issues
- [ ] **Import Errors**: Verify Supabase imports are correct
- [ ] **Environment Variables**: Check all required env vars exist
- [ ] **Authentication**: Verify user is authenticated
- [ ] **Permissions**: Check user has required permissions
- [ ] **Data Validation**: Ensure all required fields are provided

## 🖥️ Frontend Errors

### Step 1: Check Console Errors
```javascript
// Common patterns to look for:
- "Cannot access 'X' before initialization" (Temporal Dead Zone)
- "Cannot read property 'X' of undefined" (Null reference)
- "X is not a function" (Import or scope issues)
```

### Step 2: Add Component Debugging
```javascript
// Add to your components:
useEffect(() => {
  console.log('Component mounted with props:', props)
  console.log('Component state:', state)
}, [])

// Add null checks:
if (!data) {
  console.log('Data not available yet')
  return <div>Loading...</div>
}
```

### Step 3: Check Common Issues
- [ ] **Function Order**: Helper functions defined before use
- [ ] **Null Safety**: Data exists before accessing properties
- [ ] **State Updates**: State is updated correctly
- [ ] **Effect Dependencies**: useEffect dependencies are correct

## 🗄️ Database Errors

### Step 1: Check Error Messages
```javascript
// Supabase error patterns:
- "duplicate key value violates unique constraint"
- "null value in column violates not-null constraint"
- "permission denied for table"
- "row-level security policy violation"
```

### Step 2: Test Database Operations
```javascript
// Test with service role client:
const { data, error } = await supabaseAdmin
  .from('table_name')
  .select('*')
  .limit(1)

console.log('Database test:', { data, error })
```

### Step 3: Check Common Issues
- [ ] **RLS Policies**: Row Level Security allows the operation
- [ ] **Table Permissions**: User has access to the table
- [ ] **Data Types**: Data matches expected types
- [ ] **Required Fields**: All required fields are provided
- [ ] **Foreign Keys**: Referenced records exist

## 🚀 Deployment Errors

### Step 1: Check Build Logs
```bash
# Look for these patterns:
- "Failed to compile"
- "Module not found"
- "Syntax error"
- "Type error"
```

### Step 2: Test Locally
```bash
# Run these commands:
npm run build          # Check for build errors
npm run type-check     # Check for TypeScript errors
npm run lint          # Check for linting errors
```

### Step 3: Check Common Issues
- [ ] **Missing Dependencies**: All packages installed
- [ ] **Environment Variables**: All env vars set in deployment
- [ ] **Syntax Errors**: All JSX tags properly closed
- [ ] **Import Paths**: All imports resolve correctly

## 🛠️ Debugging Tools

### Browser Developer Tools
```javascript
// Console commands for debugging:
console.log('Debug info:', data)
console.error('Error details:', error)
console.table(arrayData)  // For arrays/objects
console.trace()           // For call stack
```

### Network Tab
- Check request/response details
- Look for failed requests (red status)
- Verify request headers and body
- Check response status and content

### React Developer Tools
- Inspect component props and state
- Check component hierarchy
- Monitor state changes
- Profile performance

## 📝 Documentation Template

When you solve an error, document it:

```markdown
## Error: [Brief Description]

### Problem
[What went wrong]

### Symptoms
[What the user saw]

### Root Cause
[Technical explanation]

### Solution
[Step-by-step fix]

### Prevention
[How to avoid in future]
```

## 🎯 Quick Checklist

When debugging any issue:
- [ ] Read the error message carefully
- [ ] Check browser console
- [ ] Check network requests
- [ ] Add logging to isolate the issue
- [ ] Test with minimal reproduction
- [ ] Check recent changes
- [ ] Search error database
- [ ] Document the solution
