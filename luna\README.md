# 🌙 Luna - Skills Gap Assessment & Training Platform

Luna is a comprehensive skills gap assessment and training platform that helps individuals and organizations identify skill gaps, create personalized learning paths, and track progress toward career and business goals.

## 🏗️ Project Structure

```
luna/
├── database/                 # Database schema and migrations
│   ├── migrations/          # Supabase migrations
│   ├── seed/               # Database seed data
│   └── types/              # TypeScript database types
├── docs/                   # Luna-specific documentation
│   ├── api/               # API documentation
│   ├── deployment/        # Deployment guides
│   └── user-guides/       # User documentation
├── src/                   # Source code (future)
└── scripts/               # Database and utility scripts
```

## 🎯 Current Phase: Phase 1 - Foundation

**Goal**: Transform BPO Training Platform into Luna's multi-tenant architecture

**Timeline**: 3-4 weeks

**Key Deliverables**:
- Multi-tiered account system (Individual + Organization)
- Fresh database schema with multi-tenant support
- Context switching infrastructure
- Portal restructuring and Luna branding

## 🗄️ Database

Luna uses Supabase with PostgreSQL for:
- Multi-tenant data isolation
- Row Level Security (RLS) policies
- Real-time subscriptions
- Built-in authentication

### Database Management

```bash
# Initialize Supabase (if not done)
npx supabase init

# Create new migration
npx supabase migration new migration_name

# Apply migrations to remote database
npx supabase db push

# Pull remote schema changes
npx supabase db pull

# Reset local database
npx supabase db reset
```

## 🚀 Getting Started

1. **Database Setup**
   ```bash
   cd luna
   npx supabase db push
   ```

2. **Environment Configuration**
   - Update `.env.local` with Luna database credentials
   - Configure Supabase project settings

3. **Run Migrations**
   - Execute Phase 1 database migrations
   - Verify multi-tenant setup

## 📊 Project Phases

- **Phase 1**: Foundation (Current) - Multi-tenant architecture
- **Phase 2**: Core Features - Skills gap analysis and marketplace
- **Phase 3**: Enhanced Features - AI-powered intelligence and analytics

## 🔗 Links

- **Supabase Dashboard**: [Luna Project](https://supabase.com/dashboard/project/gmaygmwgqfrxqwplcfbo)
- **GitHub Repository**: [Luna Platform](https://github.com/JennineHamilton/luna)
- **Documentation**: See `docs/` folder for detailed guides

---

*Luna Platform - Transforming skills development for the modern workforce*
