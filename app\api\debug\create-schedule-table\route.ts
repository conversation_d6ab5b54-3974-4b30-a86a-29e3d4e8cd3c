import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Check if the bpo_schedules table exists
    const { data: tableCheck, error: tableCheckError } = await supabase
      .from('bpo_schedules')
      .select('id')
      .limit(1);

    if (tableCheckError) {
      // Table doesn't exist, provide SQL instructions
      const createTableSQL = `
-- Run this SQL in your Supabase SQL Editor:

CREATE TABLE IF NOT EXISTS bpo_schedules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6),
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,

    -- Ensure end time is after start time
    CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_user_id ON bpo_schedules(user_id);
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_day_of_week ON bpo_schedules(day_of_week);
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_availability ON bpo_schedules(is_available);
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_user_day ON bpo_schedules(user_id, day_of_week);

-- Enable RLS
ALTER TABLE bpo_schedules ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own schedules" ON bpo_schedules
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own schedules" ON bpo_schedules
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own schedules" ON bpo_schedules
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own schedules" ON bpo_schedules
    FOR DELETE USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON bpo_schedules TO authenticated;
      `;

      return NextResponse.json({
        success: false,
        tableExists: false,
        message: 'bpo_schedules table does not exist. Please run the provided SQL in your Supabase SQL Editor.',
        sql: createTableSQL,
        error: tableCheckError
      });
    } else {
      return NextResponse.json({
        success: true,
        tableExists: true,
        message: 'bpo_schedules table already exists',
        recordCount: tableCheck?.length || 0
      });
    }

  } catch (error) {
    console.error('Error in create schedule table API:', error);
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    );
  }
}
