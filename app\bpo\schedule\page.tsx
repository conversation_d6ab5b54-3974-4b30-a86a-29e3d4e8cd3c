'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { supabase } from '@/lib/supabase';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { Switch } from '@/components/ui/switch';
import { TimePicker } from '@/components/ui/time-picker';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Calendar,
  Clock,
  Plus,
  ChevronLeft,
  ChevronRight,
  Save,
  Trash2,
  Edit3,
  MoreHorizontal
} from 'lucide-react';

interface TimeSlot {
  id?: string;
  start: string;
  end: string;
  title?: string;
}

interface CalendarEvent {
  id?: string;
  date: string; // YYYY-MM-DD format
  timeSlots: TimeSlot[];
  isAvailable: boolean;
}

type ViewMode = 'day' | 'week' | 'month' | 'year';

const DAYS_OF_WEEK = ['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'];
const MONTHS = [
  'January', 'February', 'March', 'April', 'May', 'June',
  'July', 'August', 'September', 'October', 'November', 'December'
];

const HOURS = Array.from({ length: 24 }, (_, i) => {
  const hour = i.toString().padStart(2, '0');
  return `${hour}:00`;
});

export default function BPOSchedulePage() {
  const router = useRouter();
  const { toast } = useToast();
  const [loading, setLoading] = useState(true);
  const [user, setUser] = useState<any>(null);

  // Calendar state
  const [viewMode, setViewMode] = useState<ViewMode>('week');
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedDate, setSelectedDate] = useState<string | null>(null);
  const [events, setEvents] = useState<CalendarEvent[]>([]);
  const [hasUnsavedChanges, setHasUnsavedChanges] = useState(false);

  // Dialog state
  const [isEventDialogOpen, setIsEventDialogOpen] = useState(false);
  const [editingEvent, setEditingEvent] = useState<CalendarEvent | null>(null);
  const [newTimeSlot, setNewTimeSlot] = useState({ start: '09:00', end: '17:00' });

  // Bulk operations state
  const [selectedDays, setSelectedDays] = useState<string[]>([]);
  const [bulkMode, setBulkMode] = useState(false);
  const [quickTemplates] = useState([
    { name: 'Standard Business Hours', slots: [{ start: '09:00', end: '17:00' }] },
    { name: 'Morning Shift', slots: [{ start: '08:00', end: '12:00' }] },
    { name: 'Afternoon Shift', slots: [{ start: '13:00', end: '17:00' }] },
    { name: 'Split Shift', slots: [{ start: '09:00', end: '12:00' }, { start: '14:00', end: '17:00' }] },
    { name: 'Extended Hours', slots: [{ start: '08:00', end: '18:00' }] }
  ]);

  // Helper functions for time conversion
  const convertTo12Hour = (time24: string): string => {
    if (!time24) return '';
    const [hours, minutes] = time24.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const hour12 = hour % 12 || 12;
    return `${hour12}:${minutes} ${ampm}`;
  };

  const convertTo24Hour = (time12: string): string => {
    if (!time12) return '';
    const [time, ampm] = time12.split(' ');
    const [hours, minutes] = time.split(':');
    let hour = parseInt(hours);

    if (ampm === 'PM' && hour !== 12) {
      hour += 12;
    } else if (ampm === 'AM' && hour === 12) {
      hour = 0;
    }

    return `${hour.toString().padStart(2, '0')}:${minutes}`;
  };

  useEffect(() => {
    fetchScheduleData();
  }, []);

  const fetchScheduleData = async () => {
    try {
      setLoading(true);

      // Get the current user
      const { data: { session } } = await supabase.auth.getSession();
      if (!session) {
        router.push('/login');
        return;
      }

      setUser(session.user);

      // Fetch schedule from database (new JSONB format)
      const { data: scheduleRecord, error } = await supabase
        .from('bpo_schedules')
        .select('*')
        .eq('user_id', session.user.id)
        .single();

      if (error && error.code !== 'PGRST116') { // PGRST116 = no rows found
        console.error('Error fetching schedule:', error);
        toast({
          title: "Error loading schedule",
          description: "There was an error loading your schedule from the database.",
          variant: "destructive",
        });
        return;
      }

      // Convert database schedule to calendar events
      if (scheduleRecord?.schedule_data) {
        const calendarEvents = convertScheduleToEvents(scheduleRecord.schedule_data);
        setEvents(calendarEvents);
      } else {
        // No schedule found, start with empty events
        setEvents([]);
      }

    } catch (error) {
      console.error('Error fetching schedule data:', error);
      toast({
        title: "Error",
        description: "There was an error loading your schedule.",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const saveSchedule = async () => {
    try {
      if (!user) return;

      // Convert calendar events to new JSONB format
      const scheduleRecord = convertEventsToSchedule(events);

      console.log('Events to convert:', events);
      console.log('Converted schedule record:', scheduleRecord);

      // Use upsert to insert or update the schedule
      const { error: upsertError } = await supabase
        .from('bpo_schedules')
        .upsert(scheduleRecord, {
          onConflict: 'user_id'
        });

      if (upsertError) {
        console.error('Upsert error:', upsertError);
        throw upsertError;
      }

      // Count total time slots for feedback
      const totalSlots = Object.values(scheduleRecord.schedule_data).reduce(
        (total, daySlots) => total + daySlots.length,
        0
      );

      setHasUnsavedChanges(false);
      toast({
        title: "✅ Schedule Saved Successfully!",
        description: `Your weekly schedule has been saved with ${totalSlots} time slots across all days.`,
        duration: 5000,
      });

    } catch (error: any) {
      console.error('Error saving schedule:', error);
      toast({
        title: "❌ Error Saving Schedule",
        description: error.message || "There was an error saving your schedule to the database. Please try again.",
        variant: "destructive",
      });
    }
  };

  // Database conversion functions
  const convertScheduleToEvents = (scheduleData: { [key: string]: { start: string; end: string }[] }): CalendarEvent[] => {
    const eventMap: { [date: string]: CalendarEvent } = {};

    // Get current week dates
    const today = new Date();
    const currentWeekStart = new Date(today);
    currentWeekStart.setDate(today.getDate() - today.getDay()); // Start of current week (Sunday)

    for (let i = 0; i < 7; i++) {
      const date = new Date(currentWeekStart);
      date.setDate(currentWeekStart.getDate() + i);
      const dayOfWeek = date.getDay();
      const dateStr = formatDateString(date);

      const daySlots = scheduleData[dayOfWeek.toString()] || [];

      if (daySlots.length > 0) {
        eventMap[dateStr] = {
          id: `event_${dateStr}`,
          date: dateStr,
          timeSlots: daySlots.map((slot, index) => ({
            id: `slot_${dateStr}_${index}`,
            start: slot.start,
            end: slot.end
          })),
          isAvailable: true
        };
      }
    }

    return Object.values(eventMap);
  };

  const convertEventsToSchedule = (events: CalendarEvent[]): any => {
    const scheduleData: { [key: string]: { start: string; end: string }[] } = {};

    // Initialize all days with empty arrays
    for (let day = 0; day <= 6; day++) {
      scheduleData[day.toString()] = [];
    }

    events.forEach(event => {
      const eventDate = new Date(event.date);
      const dayOfWeek = eventDate.getDay(); // 0 = Sunday, 1 = Monday, etc.

      if (event.isAvailable && event.timeSlots.length > 0) {
        event.timeSlots.forEach(slot => {
          scheduleData[dayOfWeek.toString()].push({
            start: slot.start,
            end: slot.end
          });
        });
      }
    });

    return {
      user_id: user?.id,
      schedule_data: scheduleData
    };
  };

  // Calendar navigation functions
  const navigateDate = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);

    switch (viewMode) {
      case 'day':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 1 : -1));
        break;
      case 'week':
        newDate.setDate(newDate.getDate() + (direction === 'next' ? 7 : -7));
        break;
      case 'month':
        newDate.setMonth(newDate.getMonth() + (direction === 'next' ? 1 : -1));
        break;
      case 'year':
        newDate.setFullYear(newDate.getFullYear() + (direction === 'next' ? 1 : -1));
        break;
    }

    setCurrentDate(newDate);
  };

  const goToToday = () => {
    setCurrentDate(new Date());
  };

  const formatDateString = (date: Date): string => {
    return date.toISOString().split('T')[0];
  };

  const getEventForDate = (dateStr: string): CalendarEvent | undefined => {
    return events.find(event => event.date === dateStr);
  };

  const addOrUpdateEvent = (dateStr: string, timeSlots: TimeSlot[], isAvailable: boolean) => {
    setEvents(prev => {
      const existingIndex = prev.findIndex(event => event.date === dateStr);
      const newEvent: CalendarEvent = {
        id: existingIndex >= 0 ? prev[existingIndex].id : `event_${Date.now()}`,
        date: dateStr,
        timeSlots,
        isAvailable
      };

      if (existingIndex >= 0) {
        const updated = [...prev];
        updated[existingIndex] = newEvent;
        return updated;
      } else {
        return [...prev, newEvent];
      }
    });
    setHasUnsavedChanges(true);
  };

  const deleteEvent = (dateStr: string) => {
    setEvents(prev => prev.filter(event => event.date !== dateStr));
    setHasUnsavedChanges(true);
  };

  const openEventDialog = (dateStr: string) => {
    setSelectedDate(dateStr);
    const existingEvent = getEventForDate(dateStr);
    setEditingEvent(existingEvent || {
      date: dateStr,
      timeSlots: [],
      isAvailable: false
    });
    setSelectedDays([]); // Reset bulk selection
    setIsEventDialogOpen(true);
  };

  const applyTemplateToMultipleDays = (template: { slots: { start: string; end: string }[] }) => {
    if (selectedDays.length === 0) {
      toast({
        title: "No days selected",
        description: "Please select days to apply the template to",
        variant: "destructive",
      });
      return;
    }

    const currentWeek = getWeekDates(new Date(selectedDate!));
    selectedDays.forEach(dayIndex => {
      const targetDate = currentWeek[parseInt(dayIndex)];
      const dateStr = formatDateString(targetDate);
      const timeSlots = template.slots.map((slot, i) => ({
        id: `slot_${Date.now()}_${i}_${dayIndex}`,
        start: slot.start,
        end: slot.end
      }));
      addOrUpdateEvent(dateStr, timeSlots, true);
    });

    toast({
      title: "Template Applied",
      description: `Applied ${template.slots.length} time slot(s) to ${selectedDays.length} days`,
    });

    setSelectedDays([]);
  };

  const addTimeSlotToEvent = () => {
    if (!editingEvent) return;

    // Validate the new time slot
    if (!newTimeSlot.start || !newTimeSlot.end) {
      toast({
        title: "Invalid time slot",
        description: "Please select both start and end times.",
        variant: "destructive",
      });
      return;
    }

    if (newTimeSlot.start >= newTimeSlot.end) {
      toast({
        title: "Invalid time range",
        description: "End time must be after start time.",
        variant: "destructive",
      });
      return;
    }

    setEditingEvent({
      ...editingEvent,
      timeSlots: [...editingEvent.timeSlots, { ...newTimeSlot, id: `slot_${Date.now()}` }],
      isAvailable: true
    });

    // Reset the new time slot form
    setNewTimeSlot({ start: '09:00', end: '17:00' });
  };

  const removeTimeSlotFromEvent = (slotId: string) => {
    if (!editingEvent) return;

    setEditingEvent({
      ...editingEvent,
      timeSlots: editingEvent.timeSlots.filter(slot => slot.id !== slotId)
    });
  };

  const updateTimeSlotInEvent = (slotId: string, field: 'start' | 'end', value: string) => {
    if (!editingEvent) return;

    setEditingEvent({
      ...editingEvent,
      timeSlots: editingEvent.timeSlots.map(slot =>
        slot.id === slotId ? { ...slot, [field]: value } : slot
      )
    });
  };

  const saveEvent = () => {
    if (!editingEvent || !selectedDate) return;

    if (editingEvent.timeSlots.length === 0) {
      deleteEvent(selectedDate);
    } else {
      addOrUpdateEvent(selectedDate, editingEvent.timeSlots, editingEvent.isAvailable);
    }

    setIsEventDialogOpen(false);
    setEditingEvent(null);
    setSelectedDate(null);
  };

  // Calendar view helpers
  const getWeekDates = (date: Date): Date[] => {
    const week = [];
    const startOfWeek = new Date(date);
    startOfWeek.setDate(date.getDate() - date.getDay());

    for (let i = 0; i < 7; i++) {
      const day = new Date(startOfWeek);
      day.setDate(startOfWeek.getDate() + i);
      week.push(day);
    }

    return week;
  };

  const getMonthDates = (date: Date): Date[] => {
    const year = date.getFullYear();
    const month = date.getMonth();
    const firstDay = new Date(year, month, 1);
    const lastDay = new Date(year, month + 1, 0);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.getDay());

    const dates = [];
    const currentDate = new Date(startDate);

    while (currentDate <= lastDay || dates.length % 7 !== 0) {
      dates.push(new Date(currentDate));
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return dates;
  };

  const formatTimeRange = (timeSlots: TimeSlot[]) => {
    if (timeSlots.length === 0) return '';
    return timeSlots.map(slot => `${convertTo12Hour(slot.start)}-${convertTo12Hour(slot.end)}`).join(', ');
  };

  const getTotalHoursForDate = (dateStr: string): number => {
    const event = getEventForDate(dateStr);
    if (!event || !event.isAvailable) return 0;

    return event.timeSlots.reduce((total, slot) => {
      const start = new Date(`2000-01-01T${slot.start}`);
      const end = new Date(`2000-01-01T${slot.end}`);
      return total + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
    }, 0);
  };

  const getViewTitle = (): string => {
    switch (viewMode) {
      case 'day':
        return currentDate.toLocaleDateString('en-US', {
          weekday: 'long',
          year: 'numeric',
          month: 'long',
          day: 'numeric'
        });
      case 'week':
        const weekStart = getWeekDates(currentDate)[0];
        const weekEnd = getWeekDates(currentDate)[6];
        return `${weekStart.toLocaleDateString('en-US', { month: 'short', day: 'numeric' })} - ${weekEnd.toLocaleDateString('en-US', { month: 'short', day: 'numeric', year: 'numeric' })}`;
      case 'month':
        return currentDate.toLocaleDateString('en-US', { year: 'numeric', month: 'long' });
      case 'year':
        return currentDate.getFullYear().toString();
      default:
        return '';
    }
  };

  if (loading) {
    return (
      <div className="h-64 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500 mx-auto"></div>
          <p className="mt-2 text-gray-500 dark:text-gray-400">Loading calendar...</p>
        </div>
      </div>
    );
  }

  // Check if user has any schedule set up
  const hasAnySchedule = events.some(event => event.isAvailable && event.timeSlots.length > 0);

  return (
    <div className="h-full flex flex-col">
      {/* Smart Onboarding Banner */}
      {!hasAnySchedule && !loading && (
        <div className="bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-900/20 dark:to-indigo-900/20 border-b border-blue-200 dark:border-blue-800">
          <div className="p-4">
            <div className="flex items-start gap-3">
              <div className="flex-shrink-0 w-8 h-8 bg-blue-100 dark:bg-blue-900/50 rounded-full flex items-center justify-center">
                <Clock className="h-4 w-4 text-blue-600 dark:text-blue-400" />
              </div>
              <div className="flex-1">
                <h3 className="font-medium text-blue-900 dark:text-blue-100">Set up your interview availability</h3>
                <p className="text-sm text-blue-700 dark:text-blue-300 mt-1">
                  Choose a quick template to get started, or click on any day to set custom hours.
                </p>
              </div>
              <div className="flex gap-2">
                <Button
                  size="sm"
                  onClick={() => {
                    const template = quickTemplates[0]; // Standard Business Hours
                    const weekdays = ['1', '2', '3', '4', '5'];
                    setSelectedDays(weekdays);
                    applyTemplateToMultipleDays(template);
                  }}
                  className="bg-blue-600 hover:bg-blue-700 text-white"
                >
                  Set Standard Hours
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Calendar Header */}
      <div className="flex items-center justify-between p-4 border-b bg-white dark:bg-gray-900">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-semibold">Schedule</h1>

          {/* Navigation */}
          <div className="flex items-center gap-2">
            <Button variant="outline" size="sm" onClick={() => navigateDate('prev')}>
              <ChevronLeft className="h-4 w-4" />
            </Button>
            <Button variant="outline" size="sm" onClick={goToToday}>
              Today
            </Button>
            <Button variant="outline" size="sm" onClick={() => navigateDate('next')}>
              <ChevronRight className="h-4 w-4" />
            </Button>
          </div>

          <div className="text-lg font-medium text-gray-700 dark:text-gray-300">
            {getViewTitle()}
          </div>
        </div>

        <div className="flex items-center gap-3">
          {/* View Mode Selector */}
          <div className="flex items-center border rounded-lg">
            {(['day', 'week', 'month', 'year'] as ViewMode[]).map((mode) => (
              <Button
                key={mode}
                variant={viewMode === mode ? "default" : "ghost"}
                size="sm"
                className="rounded-none first:rounded-l-lg last:rounded-r-lg"
                onClick={() => setViewMode(mode)}
              >
                {mode.charAt(0).toUpperCase() + mode.slice(1)}
              </Button>
            ))}
          </div>

          {hasUnsavedChanges && (
            <Badge variant="outline" className="text-amber-600 border-amber-200">
              Unsaved
            </Badge>
          )}

          <Button onClick={saveSchedule} disabled={!hasUnsavedChanges} size="sm">
            <Save className="h-4 w-4 mr-2" />
            Save
          </Button>
        </div>
      </div>

      {/* Calendar Grid */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'day' && (
          <div className="p-4">
            <DayView
              date={currentDate}
              event={getEventForDate(formatDateString(currentDate))}
              onEventClick={() => openEventDialog(formatDateString(currentDate))}
            />
          </div>
        )}

        {viewMode === 'week' && (
          <div className="p-4">
            <WeekView
              dates={getWeekDates(currentDate)}
              events={events}
              onDateClick={(date) => openEventDialog(formatDateString(date))}
            />
          </div>
        )}

        {viewMode === 'month' && (
          <div className="p-4">
            <MonthView
              dates={getMonthDates(currentDate)}
              currentMonth={currentDate.getMonth()}
              events={events}
              onDateClick={(date) => openEventDialog(formatDateString(date))}
            />
          </div>
        )}

        {viewMode === 'year' && (
          <div className="p-4">
            <YearView
              year={currentDate.getFullYear()}
              events={events}
              onMonthClick={(month) => {
                setCurrentDate(new Date(currentDate.getFullYear(), month, 1));
                setViewMode('month');
              }}
            />
          </div>
        )}
      </div>

      {/* Event Dialog */}
      <Dialog open={isEventDialogOpen} onOpenChange={setIsEventDialogOpen}>
        <DialogContent className="max-w-5xl w-[95vw] max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <Calendar className="h-5 w-5" />
              {selectedDate ? new Date(selectedDate).toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              }) : 'Edit Schedule'}
            </DialogTitle>
            <DialogDescription>
              Set your availability quickly and easily
            </DialogDescription>
          </DialogHeader>

          <div className="grid grid-cols-1 xl:grid-cols-2 gap-4 md:gap-6">
            {/* Left Column - Quick Templates */}
            <div className="space-y-4">
              <div>
                <Label className="text-base font-medium">Quick Templates</Label>
                <p className="text-sm text-gray-500 mb-3">Choose a common schedule pattern</p>
                <div className="grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-1 gap-2">
                  {quickTemplates.map((template, index) => (
                    <Button
                      key={index}
                      variant="outline"
                      className="justify-start h-auto p-3 text-left"
                      onClick={() => {
                        if (editingEvent) {
                          setEditingEvent({
                            ...editingEvent,
                            isAvailable: true,
                            timeSlots: template.slots.map((slot, i) => ({
                              id: `slot_${Date.now()}_${i}`,
                              start: slot.start,
                              end: slot.end
                            }))
                          });
                        }
                      }}
                    >
                      <div className="w-full">
                        <div className="font-medium text-sm">{template.name}</div>
                        <div className="text-xs text-gray-500 mt-0.5">
                          {template.slots.map(slot => `${convertTo12Hour(slot.start)}-${convertTo12Hour(slot.end)}`).join(', ')}
                        </div>
                      </div>
                    </Button>
                  ))}
                </div>
              </div>

              {/* Bulk Operations */}
              <div className="border-t pt-4">
                <Label className="text-base font-medium">Apply to Multiple Days</Label>
                <p className="text-sm text-gray-500 mb-3">Select days to apply the same schedule</p>

                <div className="grid grid-cols-7 gap-1 mb-3">
                  {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => {
                    const isSelected = selectedDays.includes(index.toString());
                    return (
                      <Button
                        key={day}
                        variant={isSelected ? "default" : "outline"}
                        size="sm"
                        className="h-9 text-xs px-1 min-w-0"
                        onClick={() => {
                          if (isSelected) {
                            setSelectedDays(prev => prev.filter(d => d !== index.toString()));
                          } else {
                            setSelectedDays(prev => [...prev, index.toString()]);
                          }
                        }}
                      >
                        {day}
                      </Button>
                    );
                  })}
                </div>

                <div className="flex flex-wrap gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => setSelectedDays(['1', '2', '3', '4', '5'])}
                  >
                    Weekdays
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => setSelectedDays(['0', '6'])}
                  >
                    Weekends
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs"
                    onClick={() => setSelectedDays(['0', '1', '2', '3', '4', '5', '6'])}
                  >
                    All Days
                  </Button>
                </div>
              </div>
            </div>

            {/* Right Column - Custom Schedule */}
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <Label className="text-base font-medium">Custom Schedule</Label>
                <Switch
                  checked={editingEvent?.isAvailable || false}
                  onCheckedChange={(checked) => {
                    if (checked && editingEvent && editingEvent.timeSlots.length === 0) {
                      setEditingEvent({
                        ...editingEvent,
                        isAvailable: checked,
                        timeSlots: [{ id: `slot_${Date.now()}`, start: '09:00', end: '17:00' }]
                      });
                    } else {
                      setEditingEvent(prev => prev ? { ...prev, isAvailable: checked } : null);
                    }
                  }}
                />
              </div>

              {editingEvent?.isAvailable && (
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <Label>Time Slots</Label>
                    <Button size="sm" variant="outline" onClick={addTimeSlotToEvent}>
                      <Plus className="h-3 w-3 mr-1" />
                      Add Slot
                    </Button>
                  </div>

                  {editingEvent.timeSlots.map((slot) => (
                    <div key={slot.id} className="flex flex-col gap-2 p-3 border rounded-lg bg-gray-50 dark:bg-gray-800">
                      <div className="flex items-center gap-2 w-full">
                        <div className="flex items-center gap-2 flex-1 min-w-0">
                          <TimePicker
                            value={slot.start}
                            onChange={(value) => updateTimeSlotInEvent(slot.id!, 'start', value)}
                            className="w-24 flex-shrink-0"
                          />
                          <span className="text-sm text-gray-500 font-medium flex-shrink-0">to</span>
                          <TimePicker
                            value={slot.end}
                            onChange={(value) => updateTimeSlotInEvent(slot.id!, 'end', value)}
                            className="w-24 flex-shrink-0"
                          />
                        </div>
                        <Button
                          size="sm"
                          variant="ghost"
                          onClick={() => removeTimeSlotFromEvent(slot.id!)}
                          className="flex-shrink-0"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}

                  <div className="flex flex-col gap-2 p-3 bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg">
                    <div className="flex items-center gap-2 w-full">
                      <div className="flex items-center gap-2 flex-1 min-w-0">
                        <TimePicker
                          value={newTimeSlot.start}
                          onChange={(value) => setNewTimeSlot(prev => ({ ...prev, start: value }))}
                          className="w-24 flex-shrink-0"
                        />
                        <span className="text-sm text-gray-500 font-medium flex-shrink-0">to</span>
                        <TimePicker
                          value={newTimeSlot.end}
                          onChange={(value) => setNewTimeSlot(prev => ({ ...prev, end: value }))}
                          className="w-24 flex-shrink-0"
                        />
                      </div>
                      <span className="text-xs text-blue-600 flex-shrink-0">New slot</span>
                    </div>
                  </div>
                </div>
              )}

              {!editingEvent?.isAvailable && (
                <div className="text-center py-8 text-gray-500">
                  <Clock className="h-12 w-12 mx-auto mb-2 opacity-50" />
                  <p>Enable availability to set time slots</p>
                </div>
              )}
            </div>
          </div>

          <DialogFooter className="flex flex-col sm:flex-row justify-between gap-3 pt-4">
            <div className="flex gap-2 order-2 sm:order-1">
              {selectedDays.length > 0 && editingEvent?.isAvailable && (
                <Button
                  variant="secondary"
                  size="sm"
                  onClick={() => {
                    // Apply current schedule to selected days
                    if (editingEvent && selectedDays.length > 0) {
                      const currentWeek = getWeekDates(new Date(selectedDate!));
                      selectedDays.forEach(dayIndex => {
                        const targetDate = currentWeek[parseInt(dayIndex)];
                        const dateStr = formatDateString(targetDate);
                        addOrUpdateEvent(dateStr, editingEvent.timeSlots, editingEvent.isAvailable);
                      });
                      toast({
                        title: "Schedule Applied",
                        description: `Applied to ${selectedDays.length} selected days`,
                      });
                    }
                  }}
                  className="w-full sm:w-auto"
                >
                  Apply to {selectedDays.length} Days
                </Button>
              )}
            </div>
            <div className="flex gap-2 order-1 sm:order-2">
              <Button
                variant="outline"
                onClick={() => {
                  setIsEventDialogOpen(false);
                  setSelectedDays([]);
                }}
                className="flex-1 sm:flex-none"
              >
                Cancel
              </Button>
              <Button
                onClick={() => {
                  saveEvent();
                  setSelectedDays([]);
                }}
                className="flex-1 sm:flex-none"
              >
                Save
              </Button>
            </div>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
}

// Calendar View Components
function DayView({ date, event, onEventClick }: {
  date: Date;
  event?: CalendarEvent;
  onEventClick: () => void;
}) {
  return (
    <div className="space-y-4">
      <div className="text-center">
        <h2 className="text-2xl font-bold">{date.getDate()}</h2>
        <p className="text-gray-500">{DAYS_OF_WEEK[date.getDay()]}</p>
      </div>

      <Card className="cursor-pointer hover:shadow-md transition-shadow" onClick={onEventClick}>
        <CardContent className="p-4">
          {event?.isAvailable ? (
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <div className="w-3 h-3 bg-green-500 rounded-full"></div>
                <span className="font-medium">Available</span>
              </div>
              {event.timeSlots.map((slot, index) => (
                <div key={index} className="text-sm text-gray-600 dark:text-gray-400">
                  {slot.start} - {slot.end}
                </div>
              ))}
            </div>
          ) : (
            <div className="flex items-center gap-2 text-gray-500">
              <div className="w-3 h-3 bg-gray-300 rounded-full"></div>
              <span>Not available</span>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

function WeekView({ dates, events, onDateClick }: {
  dates: Date[];
  events: CalendarEvent[];
  onDateClick: (date: Date) => void;
}) {
  return (
    <div className="grid grid-cols-7 gap-1">
      {DAYS_OF_WEEK.map((day) => (
        <div key={day} className="p-2 text-center font-medium text-gray-500 border-b">
          {day}
        </div>
      ))}

      {dates.map((date, index) => {
        const dateStr = date.toISOString().split('T')[0];
        const event = events.find(e => e.date === dateStr);
        const isToday = date.toDateString() === new Date().toDateString();

        return (
          <div
            key={index}
            className={`min-h-24 p-2 border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
              isToday ? 'bg-blue-50 dark:bg-blue-900/20' : ''
            }`}
            onClick={() => onDateClick(date)}
          >
            <div className={`text-sm font-medium ${isToday ? 'text-blue-600' : ''}`}>
              {date.getDate()}
            </div>

            {event?.isAvailable && (
              <div className="mt-1 space-y-1">
                {event.timeSlots.slice(0, 2).map((slot, slotIndex) => (
                  <div
                    key={slotIndex}
                    className="text-xs bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-200 px-1 py-0.5 rounded truncate"
                  >
                    {slot.start}-{slot.end}
                  </div>
                ))}
                {event.timeSlots.length > 2 && (
                  <div className="text-xs text-gray-500">
                    +{event.timeSlots.length - 2} more
                  </div>
                )}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

function MonthView({ dates, currentMonth, events, onDateClick }: {
  dates: Date[];
  currentMonth: number;
  events: CalendarEvent[];
  onDateClick: (date: Date) => void;
}) {
  return (
    <div className="grid grid-cols-7 gap-1">
      {DAYS_OF_WEEK.map((day) => (
        <div key={day} className="p-2 text-center font-medium text-gray-500 border-b">
          {day}
        </div>
      ))}

      {dates.map((date, index) => {
        const dateStr = date.toISOString().split('T')[0];
        const event = events.find(e => e.date === dateStr);
        const isCurrentMonth = date.getMonth() === currentMonth;
        const isToday = date.toDateString() === new Date().toDateString();

        return (
          <div
            key={index}
            className={`min-h-20 p-1 border cursor-pointer hover:bg-gray-50 dark:hover:bg-gray-800 ${
              !isCurrentMonth ? 'text-gray-400 bg-gray-50 dark:bg-gray-800/50' : ''
            } ${isToday ? 'bg-blue-50 dark:bg-blue-900/20' : ''}`}
            onClick={() => onDateClick(date)}
          >
            <div className={`text-sm ${isToday ? 'font-bold text-blue-600' : ''}`}>
              {date.getDate()}
            </div>

            {event?.isAvailable && isCurrentMonth && (
              <div className="mt-1">
                <div className="w-2 h-2 bg-green-500 rounded-full"></div>
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}

function YearView({ year, events, onMonthClick }: {
  year: number;
  events: CalendarEvent[];
  onMonthClick: (month: number) => void;
}) {
  return (
    <div className="grid grid-cols-3 gap-6">
      {MONTHS.map((month, index) => {
        const monthEvents = events.filter(event => {
          const eventDate = new Date(event.date);
          return eventDate.getFullYear() === year && eventDate.getMonth() === index;
        });

        const availableDays = monthEvents.filter(e => e.isAvailable).length;

        return (
          <Card
            key={month}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => onMonthClick(index)}
          >
            <CardContent className="p-4">
              <h3 className="font-medium text-center mb-2">{month}</h3>
              <div className="text-center text-sm text-gray-500">
                {availableDays > 0 ? (
                  <span className="text-green-600">{availableDays} available days</span>
                ) : (
                  <span>No availability set</span>
                )}
              </div>
            </CardContent>
          </Card>
        );
      })}
    </div>
  );
}
