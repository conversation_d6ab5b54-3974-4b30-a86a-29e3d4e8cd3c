"use client"

import {
  Upload, File, FileText, Eye, Edit, Trash2, MoreVertical, Image,
  FileIcon, Download, Award, Calendar, Clock, Search, Filter,
  ImageIcon, FileTextIcon, FolderIcon, PlusCircle
} from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger, DropdownMenuSeparator } from "@/components/ui/dropdown-menu"
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle } from "@/components/ui/alert-dialog"
import { useState, useRef } from "react"
import { toast } from "sonner"

interface FileData {
  id: string
  file_name: string
  file_type: string
  file_url: string
  file_size: number
  upload_date: string
  module_title?: string
  module_description?: string
  issue_date?: string
  expiry_date?: string
  verification_code?: string
  created_at: string
  updated_at: string
  mime_type?: string
  original_filename?: string
  title?: string
}

interface FilesPageProps {
  files: FileData[]
  prospectId: string
}

export function FilesPage({ files, prospectId }: FilesPageProps) {
  const [isUploadOpen, setIsUploadOpen] = useState(false)
  const [uploadType, setUploadType] = useState<string>("")
  const [fileName, setFileName] = useState<string>("")
  const [dateReceived, setDateReceived] = useState<string>("")
  const [issuer, setIssuer] = useState<string>("")
  const [isUploading, setIsUploading] = useState(false)
  const [previewFile, setPreviewFile] = useState<FileData | null>(null)
  const [editingFile, setEditingFile] = useState<FileData | null>(null)
  const [editFileName, setEditFileName] = useState<string>("")
  const [deletingFile, setDeletingFile] = useState<FileData | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  const [isUpdating, setIsUpdating] = useState(false)
  const [searchQuery, setSearchQuery] = useState("")
  const [activeTab, setActiveTab] = useState("all")
  const fileInputRef = useRef<HTMLInputElement>(null)

  // Helper functions - moved to top to avoid temporal dead zone
  const isImageFile = (mimeType?: string, fileName?: string) => {
    if (!mimeType && !fileName) return false
    const type = mimeType || ''
    const name = fileName || ''
    return type.startsWith('image/') || /\.(jpg|jpeg|png|gif|webp)$/i.test(name)
  }

  const isPdfFile = (mimeType?: string, fileName?: string) => {
    if (!mimeType && !fileName) return false
    const type = mimeType || ''
    const name = fileName || ''
    return type === 'application/pdf' || name.endsWith('.pdf')
  }

  // Filter files based on search and active tab
  const filteredFiles = (files || []).filter(file => {
    if (!file) return false

    const fileName = file.file_name || file.title || ''
    const matchesSearch = fileName.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         (file.title && file.title.toLowerCase().includes(searchQuery.toLowerCase()))

    const matchesTab = activeTab === "all" ||
                      (activeTab === "certificates" && file.file_type === "certificate") ||
                      (activeTab === "documents" && file.file_type === "document")

    return matchesSearch && matchesTab
  })

  // Get file counts for tabs
  const fileCounts = {
    all: (files || []).length,
    certificates: (files || []).filter(f => f && f.file_type === "certificate").length,
    documents: (files || []).filter(f => f && f.file_type === "document").length
  }

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes'
    const k = 1024
    const sizes = ['Bytes', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  const formatDate = (dateString: string) => {
    if (!dateString) return 'N/A'
    return new Date(dateString).toLocaleDateString()
  }

  const getFilePreview = (file: FileData) => {
    const isImage = isImageFile(file.mime_type, file.file_name)
    const isPdf = isPdfFile(file.mime_type, file.file_name)

    if (isImage) {
      return (
        <div className="w-full h-32 bg-gray-50 rounded-lg overflow-hidden relative">
          <img
            src={file.file_url}
            alt={file.file_name}
            className="w-full h-full object-cover"
            onError={(e) => {
              const target = e.target as HTMLImageElement
              target.style.display = 'none'
              const fallback = target.parentElement?.querySelector('.fallback-icon') as HTMLElement
              if (fallback) {
                fallback.style.display = 'flex'
              }
            }}
          />
          <div className="fallback-icon absolute inset-0 w-full h-full hidden items-center justify-center bg-gray-100">
            <ImageIcon className="h-8 w-8 text-gray-400" />
          </div>
        </div>
      )
    } else if (isPdf) {
      return (
        <div className="w-full h-32 bg-red-50 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <FileTextIcon className="h-12 w-12 text-red-500 mx-auto mb-2" />
            <span className="text-xs text-red-600 font-medium">PDF</span>
          </div>
        </div>
      )
    } else {
      return (
        <div className="w-full h-32 bg-blue-50 rounded-lg flex items-center justify-center">
          <div className="text-center">
            <FileIcon className="h-12 w-12 text-blue-500 mx-auto mb-2" />
            <span className="text-xs text-blue-600 font-medium">
              {file.mime_type?.split('/')[1]?.toUpperCase() || 'FILE'}
            </span>
          </div>
        </div>
      )
    }
  }

  const getFileTypeColor = (file: FileData) => {
    if (file.file_type === 'certificate') {
      return file.verification_code ? 'bg-green-100 text-green-700' : 'bg-blue-100 text-blue-700'
    }
    return 'bg-gray-100 text-gray-700'
  }

  const getFileTypeLabel = (file: FileData) => {
    if (file.file_type === 'certificate') {
      return file.verification_code ? 'Earned Certificate' : 'Certificate'
    }
    return 'Document'
  }



  const handlePreview = (file: FileData) => {
    setPreviewFile(file)
  }

  const handleEdit = (file: FileData) => {
    setEditingFile(file)
    setEditFileName(file.title || file.file_name)
  }

  const handleUpdateFile = async () => {
    if (!editingFile || !editFileName.trim()) {
      toast.error('Please enter a valid file name')
      return
    }

    setIsUpdating(true)

    try {
      const response = await fetch(`/api/files/${editingFile.id}`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          title: editFileName.trim()
        })
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Update failed')
      }

      toast.success('File updated successfully!')
      setEditingFile(null)
      setEditFileName("")

      // Refresh the page to show updated data
      window.location.reload()

    } catch (error) {
      console.error('Update error:', error)
      toast.error(error instanceof Error ? error.message : 'Failed to update file')
    } finally {
      setIsUpdating(false)
    }
  }

  const handleDelete = async () => {
    if (!deletingFile) return

    setIsDeleting(true)

    try {
      console.log('🗑️ Starting delete for file:', deletingFile.id)

      const response = await fetch(`/api/files/${deletingFile.id}`, {
        method: 'DELETE',
        headers: {
          'Content-Type': 'application/json',
        }
      })

      console.log('🔍 Delete response status:', response.status)
      console.log('🔍 Delete response headers:', Object.fromEntries(response.headers.entries()))

      let result
      try {
        result = await response.json()
        console.log('🔍 Delete response body:', result)
      } catch (jsonError) {
        console.error('❌ Failed to parse response as JSON:', jsonError)
        const textResult = await response.text()
        console.log('🔍 Raw response text:', textResult)
        throw new Error(`Server returned ${response.status}: ${textResult}`)
      }

      if (!response.ok) {
        console.error('❌ Delete failed with status:', response.status)
        console.error('❌ Error details:', result)
        throw new Error(result.error || result.details || `Delete failed with status ${response.status}`)
      }

      console.log('✅ Delete successful:', result)
      toast.success('File deleted successfully!')
      setDeletingFile(null)

      // Refresh the page to show updated data
      window.location.reload()

    } catch (error) {
      console.error('❌ Delete error:', error)
      console.error('❌ Error stack:', error instanceof Error ? error.stack : 'No stack')
      toast.error(error instanceof Error ? error.message : 'Failed to delete file')
    } finally {
      setIsDeleting(false)
    }
  }

  const handleDownload = async (file: FileData) => {
    try {
      // Create a temporary link to download the file
      const link = document.createElement('a')
      link.href = file.file_url
      link.download = file.original_filename || file.file_name
      link.target = '_blank'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)

      toast.success('Download started!')
    } catch (error) {
      console.error('Download error:', error)
      toast.error('Failed to download file')
    }
  }

  const FileCard = ({ file }: { file: FileData }) => {
    if (!file) return null

    return (
    <Card className="group hover:shadow-md transition-all duration-200 cursor-pointer">
      <CardContent className="p-4">
        {/* File Preview */}
        <div
          className="mb-3 cursor-pointer"
          onClick={() => handlePreview(file)}
        >
          {getFilePreview(file)}
        </div>

        {/* File Info */}
        <div className="space-y-2">
          <div className="flex items-start justify-between gap-2">
            <h3
              className="font-medium text-sm leading-tight flex-1"
              style={{
                display: '-webkit-box',
                WebkitLineClamp: 2,
                WebkitBoxOrient: 'vertical',
                overflow: 'hidden'
              }}
              title={file.title || file.file_name}
            >
              {file.title || file.file_name}
            </h3>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button
                  variant="ghost"
                  size="sm"
                  className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                >
                  <MoreVertical className="h-3 w-3" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align="end" className="w-40">
                <DropdownMenuItem onClick={() => handlePreview(file)}>
                  <Eye className="mr-2 h-4 w-4" />
                  Preview
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleDownload(file)}>
                  <Download className="mr-2 h-4 w-4" />
                  Download
                </DropdownMenuItem>
                <DropdownMenuItem onClick={() => handleEdit(file)}>
                  <Edit className="mr-2 h-4 w-4" />
                  Rename
                </DropdownMenuItem>
                <DropdownMenuSeparator />
                <DropdownMenuItem
                  onClick={() => setDeletingFile(file)}
                  className="text-red-600"
                >
                  <Trash2 className="mr-2 h-4 w-4" />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* File Metadata */}
          <div className="flex items-center justify-between">
            <Badge variant="secondary" className={`text-xs ${getFileTypeColor(file)}`}>
              {getFileTypeLabel(file)}
            </Badge>
            <span className="text-xs text-muted-foreground">
              {formatFileSize(file.file_size)}
            </span>
          </div>

          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Calendar className="h-3 w-3" />
            {formatDate(file.upload_date)}
          </div>
        </div>

        {/* Quick Actions */}
        <div className="flex gap-1 mt-3 opacity-0 group-hover:opacity-100 transition-opacity">
          <Button
            variant="outline"
            size="sm"
            className="flex-1 h-7 text-xs"
            onClick={() => handlePreview(file)}
          >
            <Eye className="mr-1 h-3 w-3" />
            Preview
          </Button>
          <Button
            variant="outline"
            size="sm"
            className="h-7 px-2"
            onClick={() => handleDownload(file)}
          >
            <Download className="h-3 w-3" />
          </Button>
        </div>
      </CardContent>
    </Card>
    )
  }

  const handleUpload = async () => {
    if (!fileInputRef.current?.files?.[0]) {
      toast.error('Please select a file to upload')
      return
    }

    if (!uploadType) {
      toast.error('Please select a file type')
      return
    }

    if (!fileName.trim()) {
      toast.error('Please enter a file name')
      return
    }

    setIsUploading(true)

    try {
      const file = fileInputRef.current.files[0]
      const formData = new FormData()
      formData.append('file', file)
      formData.append('fileType', uploadType)
      formData.append('fileName', fileName.trim())

      // Add conditional fields
      if (uploadType === 'certificate') {
        if (dateReceived) formData.append('dateReceived', dateReceived)
        if (issuer.trim()) formData.append('issuer', issuer.trim())
      } else if (uploadType === 'document') {
        if (dateReceived) formData.append('dateReceived', dateReceived)
      }

      const response = await fetch('/api/files/upload', {
        method: 'POST',
        body: formData
      })

      const result = await response.json()

      if (!response.ok) {
        throw new Error(result.error || 'Upload failed')
      }

      toast.success('File uploaded successfully!')
      setIsUploadOpen(false)
      setUploadType("")
      setFileName("")
      setDateReceived("")
      setIssuer("")
      if (fileInputRef.current) {
        fileInputRef.current.value = ""
      }

      // Refresh the page to show the new file
      window.location.reload()

    } catch (error) {
      console.error('Upload error:', error)

      // Show detailed error message
      let errorMessage = 'Failed to upload file'
      if (error instanceof Error) {
        errorMessage = error.message
      }

      toast.error(`Upload failed: ${errorMessage}`)

      // Log additional error details for debugging
      console.error('Detailed upload error:', {
        error,
        timestamp: new Date().toISOString(),
        fileInfo: {
          name: fileName,
          type: uploadType,
          size: fileInputRef.current?.files?.[0]?.size
        }
      })
    } finally {
      setIsUploading(false)
    }
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
        <div>
          <h1 className="text-2xl font-bold tracking-tight">Files</h1>
          <p className="text-muted-foreground">
            {(files || []).length} {(files || []).length === 1 ? 'file' : 'files'} • Manage your certificates, documents, and training files
          </p>
        </div>

        <div className="flex items-center gap-3">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search files..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-9 w-64"
            />
          </div>

          {/* Upload Button */}
          <Dialog open={isUploadOpen} onOpenChange={setIsUploadOpen}>
            <DialogTrigger asChild>
              <Button>
                <PlusCircle className="mr-2 h-4 w-4" />
                Upload File
              </Button>
            </DialogTrigger>
          <DialogContent className="sm:max-w-[425px]">
            <DialogHeader>
              <DialogTitle>Upload File</DialogTitle>
              <DialogDescription>
                Upload a certificate or document to your profile.
              </DialogDescription>
            </DialogHeader>
            <div className="grid gap-4 py-4">
              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="file-type" className="text-right">
                  Type
                </Label>
                <Select value={uploadType} onValueChange={setUploadType}>
                  <SelectTrigger className="col-span-3">
                    <SelectValue placeholder="Select file type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="certificate">Certificate</SelectItem>
                    <SelectItem value="document">Document</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="file" className="text-right">
                  File
                </Label>
                <Input
                  id="file"
                  ref={fileInputRef}
                  type="file"
                  accept=".pdf,.jpg,.jpeg,.png"
                  className="col-span-3"
                />
              </div>

              <div className="grid grid-cols-4 items-center gap-4">
                <Label htmlFor="file-name" className="text-right">
                  Name
                </Label>
                <Input
                  id="file-name"
                  value={fileName}
                  onChange={(e) => setFileName(e.target.value)}
                  placeholder={uploadType === "certificate" ? "Certificate name" : "Document name"}
                  className="col-span-3"
                />
              </div>

              {/* Conditional fields based on file type */}
              {uploadType === "certificate" && (
                <>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="date-received" className="text-right">
                      Date Received
                    </Label>
                    <Input
                      id="date-received"
                      type="date"
                      value={dateReceived}
                      onChange={(e) => setDateReceived(e.target.value)}
                      className="col-span-3"
                    />
                  </div>
                  <div className="grid grid-cols-4 items-center gap-4">
                    <Label htmlFor="issuer" className="text-right">
                      Issuer
                    </Label>
                    <Input
                      id="issuer"
                      value={issuer}
                      onChange={(e) => setIssuer(e.target.value)}
                      placeholder="Organization that issued the certificate"
                      className="col-span-3"
                    />
                  </div>
                </>
              )}

              {uploadType === "document" && (
                <div className="grid grid-cols-4 items-center gap-4">
                  <Label htmlFor="document-date" className="text-right">
                    Date
                  </Label>
                  <Input
                    id="document-date"
                    type="date"
                    value={dateReceived}
                    onChange={(e) => setDateReceived(e.target.value)}
                    className="col-span-3"
                  />
                </div>
              )}
            </div>
            <DialogFooter>
              <Button
                onClick={handleUpload}
                disabled={isUploading}
                type="button"
              >
                {isUploading ? 'Uploading...' : 'Upload File'}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
        </div>
      </div>

      {/* File Type Tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="all" className="flex items-center gap-2">
            <FolderIcon className="h-4 w-4" />
            All ({fileCounts.all})
          </TabsTrigger>
          <TabsTrigger value="certificates" className="flex items-center gap-2">
            <Award className="h-4 w-4" />
            Certificates ({fileCounts.certificates})
          </TabsTrigger>
          <TabsTrigger value="documents" className="flex items-center gap-2">
            <FileTextIcon className="h-4 w-4" />
            Documents ({fileCounts.documents})
          </TabsTrigger>
        </TabsList>

        {/* File Grid - All Tabs Use Same Layout */}
        <TabsContent value="all" className="mt-6">
          {filteredFiles.length === 0 ? (
            <div className="text-center py-12">
              {searchQuery ? (
                <>
                  <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No files found matching "{searchQuery}"</p>
                  <p className="text-sm text-muted-foreground mt-1">Try adjusting your search terms</p>
                </>
              ) : (
                <>
                  <FolderIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No files uploaded yet</p>
                  <p className="text-sm text-muted-foreground mt-1">Upload your first file to get started</p>
                </>
              )}
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
              {filteredFiles.map((file) => (
                <FileCard key={file.id} file={file} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="certificates" className="mt-6">
          {filteredFiles.length === 0 ? (
            <div className="text-center py-12">
              <Award className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No certificates found</p>
              <p className="text-sm text-muted-foreground mt-1">Upload certificates or complete training modules</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
              {filteredFiles.map((file) => (
                <FileCard key={file.id} file={file} />
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="documents" className="mt-6">
          {filteredFiles.length === 0 ? (
            <div className="text-center py-12">
              <FileTextIcon className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No documents found</p>
              <p className="text-sm text-muted-foreground mt-1">Upload documents like resumes or portfolios</p>
            </div>
          ) : (
            <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 xl:grid-cols-6 gap-4">
              {filteredFiles.map((file) => (
                <FileCard key={file.id} file={file} />
              ))}
            </div>
          )}
        </TabsContent>


      </Tabs>

      {/* File Preview Dialog */}
      <Dialog open={!!previewFile} onOpenChange={() => setPreviewFile(null)}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>{previewFile?.title || previewFile?.file_name}</DialogTitle>
            <DialogDescription>
              {previewFile?.mime_type} • {previewFile && formatFileSize(previewFile.file_size)}
            </DialogDescription>
          </DialogHeader>
          <div className="flex-1 overflow-auto">
            {previewFile && (
              <div className="space-y-4">
                {isImageFile(previewFile.mime_type, previewFile.file_name) ? (
                  <div className="flex justify-center">
                    <img
                      src={previewFile.file_url}
                      alt={previewFile.file_name}
                      className="max-w-full max-h-[60vh] object-contain rounded-lg"
                    />
                  </div>
                ) : isPdfFile(previewFile.mime_type, previewFile.file_name) ? (
                  <div className="w-full h-[60vh]">
                    <iframe
                      src={previewFile.file_url}
                      className="w-full h-full border rounded-lg"
                      title={previewFile.file_name}
                    />
                  </div>
                ) : (
                  <div className="text-center py-8">
                    <FileIcon className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                    <p className="text-muted-foreground">Preview not available for this file type</p>
                    <Button
                      onClick={() => previewFile && handleDownload(previewFile)}
                      className="mt-4"
                    >
                      <Download className="mr-2 h-4 w-4" />
                      Download to View
                    </Button>
                  </div>
                )}
              </div>
            )}
          </div>
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => previewFile && handleDownload(previewFile)}
            >
              <Download className="mr-2 h-4 w-4" />
              Download
            </Button>
            <Button onClick={() => setPreviewFile(null)}>Close</Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Edit File Dialog */}
      <Dialog open={!!editingFile} onOpenChange={() => setEditingFile(null)}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Rename File</DialogTitle>
            <DialogDescription>
              Change the display name for this file.
            </DialogDescription>
          </DialogHeader>
          <div className="space-y-4">
            <div>
              <Label htmlFor="edit-file-name">File Name</Label>
              <Input
                id="edit-file-name"
                value={editFileName}
                onChange={(e) => setEditFileName(e.target.value)}
                placeholder="Enter new file name"
              />
            </div>
          </div>
          <DialogFooter>
            <Button variant="outline" onClick={() => setEditingFile(null)}>
              Cancel
            </Button>
            <Button
              onClick={handleUpdateFile}
              disabled={isUpdating || !editFileName.trim()}
            >
              {isUpdating ? 'Updating...' : 'Update'}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      {/* Delete Confirmation Dialog */}
      <AlertDialog open={!!deletingFile} onOpenChange={() => setDeletingFile(null)}>
        <AlertDialogContent>
          <AlertDialogHeader>
            <AlertDialogTitle>Delete File</AlertDialogTitle>
            <AlertDialogDescription>
              Are you sure you want to delete "{deletingFile?.title || deletingFile?.file_name}"?
              This action cannot be undone.
            </AlertDialogDescription>
          </AlertDialogHeader>
          <AlertDialogFooter>
            <AlertDialogCancel>Cancel</AlertDialogCancel>
            <AlertDialogAction
              onClick={handleDelete}
              disabled={isDeleting}
              className="bg-red-600 hover:bg-red-700"
            >
              {isDeleting ? 'Deleting...' : 'Delete'}
            </AlertDialogAction>
          </AlertDialogFooter>
        </AlertDialogContent>
      </AlertDialog>
    </div>
  )
}
