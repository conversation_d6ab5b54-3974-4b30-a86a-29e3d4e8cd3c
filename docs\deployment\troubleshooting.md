# 🔧 Troubleshooting Guide

Comprehensive troubleshooting guide for common issues in the BPO Training Platform, including solutions, debugging steps, and prevention strategies.

## 🚨 Common Issues & Solutions

### 🔐 Authentication Issues

#### Issue: "Cannot connect to Supabase"

**Symptoms:**
- Login fails with network errors
- API calls return 401 Unauthorized
- Database queries fail

**Solutions:**

```bash
# 1. Check environment variables
echo $NEXT_PUBLIC_SUPABASE_URL
echo $NEXT_PUBLIC_SUPABASE_ANON_KEY

# 2. Verify Supabase project status
curl -I $NEXT_PUBLIC_SUPABASE_URL/rest/v1/

# 3. Test authentication
curl -X POST "$NEXT_PUBLIC_SUPABASE_URL/auth/v1/token?grant_type=password" \
  -H "apikey: $NEXT_PUBLIC_SUPABASE_ANON_KEY" \
  -H "Content-Type: application/json" \
  -d '{"email":"<EMAIL>","password":"password123"}'
```

**Prevention:**
- Use environment variable validation
- Implement health checks
- Monitor Supabase status

#### Issue: "RLS Policy Blocking Access"

**Symptoms:**
- Queries return empty results
- Users can't access their own data
- Permission denied errors

**Debugging Steps:**

```sql
-- Check RLS policies
SELECT schemaname, tablename, policyname, permissive, roles, cmd, qual 
FROM pg_policies 
WHERE schemaname = 'public';

-- Test policy with specific user
SET ROLE authenticated;
SET request.jwt.claim.sub = 'user-uuid-here';
SELECT * FROM users WHERE id = 'user-uuid-here';

-- Check security functions
SELECT is_platform_admin();
SELECT * FROM get_user_bpo_memberships();
```

**Solutions:**

```sql
-- Fix common RLS issues
-- 1. Enable RLS if not enabled
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;

-- 2. Create missing policies
CREATE POLICY "Users can view own data" ON users
  FOR SELECT USING (auth.uid() = id);

-- 3. Update security functions
CREATE OR REPLACE FUNCTION is_platform_admin()
RETURNS BOOLEAN AS $$
BEGIN
  RETURN EXISTS (
    SELECT 1 FROM users 
    WHERE id = auth.uid() 
    AND role = 'admin'
    AND status = 'active'
  );
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;
```

### 🗄️ Database Issues

#### Issue: "Database Connection Pool Exhausted"

**Symptoms:**
- "too many connections" errors
- Slow database responses
- Connection timeouts

**Solutions:**

```javascript
// 1. Optimize Supabase client usage
const supabase = createClient(url, key, {
  db: {
    schema: 'public'
  },
  auth: {
    persistSession: false // For server-side usage
  }
})

// 2. Implement connection pooling
const connectionPool = {
  min: 2,
  max: 10,
  idleTimeoutMillis: 30000,
  connectionTimeoutMillis: 2000
}

// 3. Close connections properly
useEffect(() => {
  return () => {
    // Cleanup subscriptions
    subscription?.unsubscribe()
  }
}, [])
```

**Prevention:**
- Monitor connection usage
- Implement proper cleanup
- Use connection pooling

#### Issue: "Slow Query Performance"

**Debugging:**

```sql
-- Find slow queries
SELECT query, calls, total_time, mean_time, rows
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC
LIMIT 10;

-- Check missing indexes
SELECT schemaname, tablename, attname, n_distinct, correlation
FROM pg_stats
WHERE schemaname = 'public'
AND n_distinct > 100
AND correlation < 0.1;

-- Analyze query plans
EXPLAIN (ANALYZE, BUFFERS) 
SELECT * FROM training_modules 
WHERE status = 'published'
ORDER BY created_at DESC;
```

**Solutions:**

```sql
-- Add missing indexes
CREATE INDEX CONCURRENTLY idx_training_modules_status_created 
ON training_modules(status, created_at) 
WHERE status = 'published';

-- Optimize queries
-- Before: N+1 query problem
SELECT * FROM progress WHERE prospect_id = $1;
-- For each progress record:
SELECT * FROM activities WHERE id = progress.activity_id;

-- After: Single query with joins
SELECT p.*, a.title, a.type
FROM progress p
JOIN activities a ON a.id = p.activity_id
WHERE p.prospect_id = $1;
```

### ⚡ Performance Issues

#### Issue: "Slow Page Load Times"

**Debugging:**

```javascript
// 1. Measure Web Vitals
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

getCLS(console.log)
getFID(console.log)
getFCP(console.log)
getLCP(console.log)
getTTFB(console.log)

// 2. Analyze bundle size
npm run analyze

// 3. Check network requests
// Use browser DevTools Network tab
```

**Solutions:**

```javascript
// 1. Implement lazy loading
const TrainingModule = lazy(() => import('./TrainingModule'))

// 2. Optimize images
<OptimizedImage
  src="/large-image.jpg"
  alt="Training"
  width={800}
  height={600}
  priority={false}
  lazy={true}
/>

// 3. Add caching
const cachedData = await cache.get('training-modules', async () => {
  return await fetchTrainingModules()
})

// 4. Optimize database queries
const optimizedQuery = supabase
  .from('training_modules')
  .select('id, title, description') // Only select needed fields
  .eq('status', 'published')
  .order('created_at', { ascending: false })
  .limit(20) // Limit results
```

#### Issue: "Memory Leaks"

**Debugging:**

```javascript
// 1. Monitor memory usage
const memoryUsage = () => {
  if (performance.memory) {
    console.log({
      used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
      total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
      limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
    })
  }
}

// 2. Check for uncleaned subscriptions
useEffect(() => {
  const subscription = supabase
    .channel('training-updates')
    .on('postgres_changes', { event: '*', schema: 'public', table: 'training_modules' }, handleUpdate)
    .subscribe()

  return () => {
    subscription.unsubscribe() // Important!
  }
}, [])
```

**Solutions:**

```javascript
// 1. Proper cleanup
useEffect(() => {
  const timer = setInterval(updateProgress, 1000)
  const subscription = subscribeToUpdates()
  
  return () => {
    clearInterval(timer)
    subscription.unsubscribe()
  }
}, [])

// 2. Memoize expensive computations
const expensiveValue = useMemo(() => {
  return computeExpensiveValue(data)
}, [data])

// 3. Debounce frequent operations
const debouncedSearch = useDebouncedCallback(
  (searchTerm) => performSearch(searchTerm),
  300
)
```

### 🔄 Deployment Issues

#### Issue: "Build Failures"

**Common Causes & Solutions:**

```bash
# 1. TypeScript errors
npm run type-check
# Fix TypeScript errors before building

# 2. ESLint errors
npm run lint
npm run lint:fix

# 3. Missing environment variables
# Check .env.example and ensure all required vars are set

# 4. Dependency issues
rm -rf node_modules package-lock.json
npm install

# 5. Memory issues during build
export NODE_OPTIONS="--max-old-space-size=4096"
npm run build
```

#### Issue: "Vercel Deployment Failures"

**Debugging:**

```bash
# 1. Check Vercel logs
vercel logs your-deployment-url

# 2. Test build locally
npm run build
npm start

# 3. Check environment variables
vercel env ls

# 4. Verify project settings
vercel project ls
```

**Solutions:**

```javascript
// vercel.json - Fix common issues
{
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30 // Increase timeout for slow API routes
    }
  },
  "regions": ["iad1"], // Specify region for better performance
  "framework": "nextjs"
}
```

### 🔒 Security Issues

#### Issue: "CORS Errors"

**Symptoms:**
- API calls fail from browser
- "Access-Control-Allow-Origin" errors
- Cross-origin request blocked

**Solutions:**

```javascript
// next.config.mjs
const nextConfig = {
  async headers() {
    return [
      {
        source: "/api/(.*)",
        headers: [
          {
            key: "Access-Control-Allow-Origin",
            value: process.env.NODE_ENV === 'production' 
              ? "https://your-domain.com" 
              : "*"
          },
          {
            key: "Access-Control-Allow-Methods",
            value: "GET, POST, PUT, DELETE, OPTIONS"
          },
          {
            key: "Access-Control-Allow-Headers",
            value: "Content-Type, Authorization"
          }
        ]
      }
    ]
  }
}
```

#### Issue: "Rate Limiting False Positives"

**Debugging:**

```javascript
// Check rate limit status
const checkRateLimit = async (req) => {
  const key = `rate_limit:${req.ip}`
  const current = await redis.get(key)
  const ttl = await redis.ttl(key)
  
  console.log({
    ip: req.ip,
    current,
    limit: 100,
    resetIn: ttl
  })
}
```

**Solutions:**

```javascript
// Implement whitelist for trusted IPs
const trustedIPs = [
  '127.0.0.1',
  '::1',
  process.env.TRUSTED_IP
].filter(Boolean)

const rateLimitMiddleware = (req, res, next) => {
  if (trustedIPs.includes(req.ip)) {
    return next() // Skip rate limiting for trusted IPs
  }
  
  // Apply rate limiting
  return applyRateLimit(req, res, next)
}
```

## 🔍 Debugging Tools & Techniques

### Application Debugging

```javascript
// 1. Debug mode configuration
const debugConfig = {
  enabled: process.env.NODE_ENV === 'development',
  logLevel: process.env.LOG_LEVEL || 'info',
  showQueries: process.env.DEBUG_QUERIES === 'true',
  showPerformance: process.env.DEBUG_PERFORMANCE === 'true'
}

// 2. Custom debug logger
const debug = {
  log: (message, data) => {
    if (debugConfig.enabled) {
      console.log(`[DEBUG] ${message}`, data)
    }
  },
  
  query: (query, duration) => {
    if (debugConfig.showQueries) {
      console.log(`[QUERY] ${query} (${duration}ms)`)
    }
  },
  
  performance: (operation, duration) => {
    if (debugConfig.showPerformance) {
      console.log(`[PERF] ${operation}: ${duration}ms`)
    }
  }
}

// 3. Error boundary with debugging
class ErrorBoundary extends Component {
  componentDidCatch(error, errorInfo) {
    console.error('Error caught by boundary:', error)
    console.error('Error info:', errorInfo)
    
    // Send to error tracking service
    if (process.env.NODE_ENV === 'production') {
      Sentry.captureException(error, { extra: errorInfo })
    }
  }
}
```

### Database Debugging

```sql
-- Enable query logging
ALTER SYSTEM SET log_statement = 'all';
ALTER SYSTEM SET log_min_duration_statement = 0;

-- Monitor active connections
SELECT 
  pid,
  usename,
  application_name,
  client_addr,
  state,
  query_start,
  query
FROM pg_stat_activity
WHERE state = 'active';

-- Check table locks
SELECT 
  t.relname,
  l.locktype,
  l.mode,
  l.granted,
  a.usename,
  a.query
FROM pg_locks l
JOIN pg_stat_activity a ON l.pid = a.pid
JOIN pg_class t ON l.relation = t.oid
WHERE t.relkind = 'r';
```

### Network Debugging

```javascript
// 1. API request interceptor
const apiInterceptor = (config) => {
  console.log(`[API] ${config.method.toUpperCase()} ${config.url}`)
  console.log('[API] Headers:', config.headers)
  console.log('[API] Data:', config.data)
  
  const start = Date.now()
  
  return config
}

// 2. Response interceptor
const responseInterceptor = (response) => {
  const duration = Date.now() - response.config.metadata.startTime
  console.log(`[API] Response: ${response.status} (${duration}ms)`)
  
  return response
}

// 3. Network monitoring
const monitorNetworkRequests = () => {
  if (typeof window !== 'undefined') {
    const observer = new PerformanceObserver((list) => {
      list.getEntries().forEach((entry) => {
        if (entry.name.includes('/api/')) {
          console.log(`[NETWORK] ${entry.name}: ${entry.duration}ms`)
        }
      })
    })
    
    observer.observe({ entryTypes: ['navigation', 'resource'] })
  }
}
```

## 📊 Monitoring & Alerting

### Health Check Implementation

```javascript
// app/api/health/route.ts
export async function GET() {
  const checks = await Promise.allSettled([
    checkDatabase(),
    checkAuth(),
    checkStorage(),
    checkCache(),
    checkExternalServices()
  ])

  const results = checks.map((check, index) => ({
    name: ['database', 'auth', 'storage', 'cache', 'external'][index],
    status: check.status === 'fulfilled' ? 'healthy' : 'unhealthy',
    details: check.status === 'fulfilled' ? check.value : check.reason
  }))

  const overallHealth = results.every(r => r.status === 'healthy')

  return Response.json({
    status: overallHealth ? 'healthy' : 'unhealthy',
    timestamp: new Date().toISOString(),
    checks: results
  }, {
    status: overallHealth ? 200 : 503
  })
}
```

### Error Tracking Setup

```javascript
// lib/error-tracking.ts
import * as Sentry from '@sentry/nextjs'

export const setupErrorTracking = () => {
  Sentry.init({
    dsn: process.env.SENTRY_DSN,
    environment: process.env.NODE_ENV,
    beforeSend(event) {
      // Filter out sensitive data
      if (event.request?.data) {
        delete event.request.data.password
        delete event.request.data.token
      }
      return event
    }
  })
}

export const trackError = (error, context = {}) => {
  console.error('Error:', error)
  
  if (process.env.NODE_ENV === 'production') {
    Sentry.withScope(scope => {
      Object.keys(context).forEach(key => {
        scope.setTag(key, context[key])
      })
      Sentry.captureException(error)
    })
  }
}
```

## 🆘 Getting Help

### Support Channels

1. **Documentation**: Check this guide and other docs
2. **GitHub Issues**: Report bugs and feature requests
3. **Community Discord**: Get help from other developers
4. **Stack Overflow**: Tag questions with `bpo-training-platform`

### Creating Bug Reports

```markdown
## Bug Report Template

**Describe the bug**
A clear description of what the bug is.

**To Reproduce**
Steps to reproduce the behavior:
1. Go to '...'
2. Click on '....'
3. Scroll down to '....'
4. See error

**Expected behavior**
What you expected to happen.

**Screenshots**
If applicable, add screenshots.

**Environment:**
- OS: [e.g. iOS]
- Browser: [e.g. chrome, safari]
- Version: [e.g. 22]
- Node.js version:
- npm version:

**Additional context**
- Error messages
- Console logs
- Network requests
- Environment variables (redacted)
```

### Emergency Procedures

```bash
# 1. Quick rollback (Vercel)
vercel rollback

# 2. Database emergency access
# Use Supabase dashboard for immediate fixes

# 3. Disable features via environment variables
vercel env add MAINTENANCE_MODE true production

# 4. Emergency contact
# Contact your team lead or DevOps engineer
```

---

**Next**: Learn about [Development Workflow](../contributing/development-workflow.md) for contributing to the project.
