"use client";

import React from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { StandardizedErrorExample } from '@/components/examples/standardized-error-example';
import { ErrorBoundary } from '@/components/error-boundary';

/**
 * Test page for the new standardized error handling system
 */
export default function TestErrorHandlingPage() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="text-center space-y-2">
        <h1 className="text-3xl font-bold">Error Handling Test Suite</h1>
        <p className="text-muted-foreground">
          Test the new standardized error handling system with various scenarios
        </p>
      </div>

      <ErrorBoundary context="TestErrorHandlingPage">
        <div className="grid gap-6">
          {/* Main Error Handling Examples */}
          <StandardizedErrorExample />
          
          {/* Additional Test Components */}
          <ErrorBoundaryTest />
          <ApiErrorTest />
          <NetworkErrorTest />
        </div>
      </ErrorBoundary>
    </div>
  );
}

/**
 * Test component that intentionally throws errors to test error boundary
 */
function ErrorBoundaryTest() {
  const [shouldThrow, setShouldThrow] = React.useState(false);

  if (shouldThrow) {
    throw new Error('This is a test error thrown by the ErrorBoundaryTest component');
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Error Boundary Test</CardTitle>
        <CardDescription>
          Test the error boundary by throwing a React component error
        </CardDescription>
      </CardHeader>
      <CardContent>
        <button
          onClick={() => setShouldThrow(true)}
          className="px-4 py-2 bg-red-500 text-white rounded hover:bg-red-600"
        >
          Throw Component Error
        </button>
        <p className="text-sm text-muted-foreground mt-2">
          This will trigger the error boundary and show the standardized error page
        </p>
      </CardContent>
    </Card>
  );
}

/**
 * Test component for API error handling
 */
function ApiErrorTest() {
  const [result, setResult] = React.useState<string>('');
  const [loading, setLoading] = React.useState(false);

  const testApiError = async (errorType: string) => {
    setLoading(true);
    setResult('');
    
    try {
      const response = await fetch(`/api/test-errors?type=${errorType}`);
      const data = await response.json();
      
      if (!response.ok) {
        setResult(`API Error (${response.status}): ${data.error?.userMessage || data.error?.message || 'Unknown error'}`);
      } else {
        setResult(`Success: ${JSON.stringify(data)}`);
      }
    } catch (error) {
      setResult(`Network Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>API Error Test</CardTitle>
        <CardDescription>
          Test different types of API errors and responses
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
          <button
            onClick={() => testApiError('auth')}
            disabled={loading}
            className="px-3 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50 text-sm"
          >
            Auth Error
          </button>
          <button
            onClick={() => testApiError('validation')}
            disabled={loading}
            className="px-3 py-2 bg-yellow-500 text-white rounded hover:bg-yellow-600 disabled:opacity-50 text-sm"
          >
            Validation Error
          </button>
          <button
            onClick={() => testApiError('database')}
            disabled={loading}
            className="px-3 py-2 bg-red-500 text-white rounded hover:bg-red-600 disabled:opacity-50 text-sm"
          >
            Database Error
          </button>
          <button
            onClick={() => testApiError('success')}
            disabled={loading}
            className="px-3 py-2 bg-green-500 text-white rounded hover:bg-green-600 disabled:opacity-50 text-sm"
          >
            Success
          </button>
        </div>
        
        {loading && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded text-sm">
            Testing API error handling...
          </div>
        )}
        
        {result && (
          <div className={`p-3 border rounded text-sm ${
            result.startsWith('Success') 
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            {result}
          </div>
        )}
      </CardContent>
    </Card>
  );
}

/**
 * Test component for network error handling
 */
function NetworkErrorTest() {
  const [result, setResult] = React.useState<string>('');
  const [loading, setLoading] = React.useState(false);

  const testNetworkError = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // Try to fetch from a non-existent endpoint
      const response = await fetch('/api/non-existent-endpoint');
      const data = await response.json();
      setResult(`Unexpected success: ${JSON.stringify(data)}`);
    } catch (error) {
      setResult(`Network Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
    } finally {
      setLoading(false);
    }
  };

  const testTimeout = async () => {
    setLoading(true);
    setResult('');
    
    try {
      // Create a request that will timeout
      const controller = new AbortController();
      setTimeout(() => controller.abort(), 1000); // 1 second timeout
      
      const response = await fetch('/api/admin/stats', {
        signal: controller.signal
      });
      const data = await response.json();
      setResult(`Success: ${JSON.stringify(data)}`);
    } catch (error) {
      if (error instanceof Error && error.name === 'AbortError') {
        setResult('Request Timeout: The request was aborted after 1 second');
      } else {
        setResult(`Error: ${error instanceof Error ? error.message : 'Unknown error'}`);
      }
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Network Error Test</CardTitle>
        <CardDescription>
          Test network-related error scenarios
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <div className="flex gap-2">
          <button
            onClick={testNetworkError}
            disabled={loading}
            className="px-4 py-2 bg-orange-500 text-white rounded hover:bg-orange-600 disabled:opacity-50"
          >
            Test 404 Error
          </button>
          <button
            onClick={testTimeout}
            disabled={loading}
            className="px-4 py-2 bg-purple-500 text-white rounded hover:bg-purple-600 disabled:opacity-50"
          >
            Test Timeout
          </button>
        </div>
        
        {loading && (
          <div className="p-3 bg-blue-50 border border-blue-200 rounded text-sm">
            Testing network error handling...
          </div>
        )}
        
        {result && (
          <div className={`p-3 border rounded text-sm ${
            result.startsWith('Success') 
              ? 'bg-green-50 border-green-200 text-green-800'
              : 'bg-red-50 border-red-200 text-red-800'
          }`}>
            {result}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
