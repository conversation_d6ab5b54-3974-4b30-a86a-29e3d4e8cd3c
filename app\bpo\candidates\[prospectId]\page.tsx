import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, CardFooter } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { InviteToInterviewDialog } from "@/components/invite-to-interview-dialog";
import Link from "next/link";
import { ArrowLeft, Mail, Phone, MapPin, Calendar, Briefcase, BookOpen, Trophy, FileCheck, BarChart3, Star, Headphones } from "lucide-react";
import { createServerClient } from '@/lib/supabase-server';
import BPOCandidateProfileClient from './bpo-candidate-profile-client';

type ContactInfo = {
  name?: string;
  title?: string;
  avatar_url?: string;
  email?: string;
  phone?: string;
  about?: string;
  location?: {
    city?: string;
    country?: string;
  };
};

type Skill = {
  name: string;
  level?: string;
};

type Experience = {
  title: string;
  company: string;
  start: string;
  end: string;
  description: string;
};

type Education = {
  degree: string;
  institution: string;
  start: string;
  end: string;
  description: string;
};

type Certificate = {
  id: string;
  module_title: string;
  issue_date: string;
  certificate_url: string;
};

type Badge = {
  id: string;
  title: string;
  description: string;
  badge_type: string;
  image_url?: string;
  date_earned: string;
};

type AssessmentReport = {
  id: string;
  title: string;
  score: number;
  completion_date: string;
  category: string;
};

type AgentRating = {
  overall_score: number;
  communication: number;
  problem_solving: number;
  customer_service: number;
  technical_knowledge: number;
  last_updated: string;
};

type TrainingReport = {
  progress_percentage: number;
  completed_modules: number;
  total_modules: number;
  call_practice_hours: number;
  call_practice_score: number;
};

type ProspectProfile = {
  name: string;
  title: string;
  avatarUrl: string;
  location: string;
  email: string;
  phone: string;
  about: string;
  introVideoUrl: string;
  experience: Experience[];
  education: Education[];
  skills: Skill[];
  certificates: Certificate[];
  badges: Badge[];
  assessmentReports: AssessmentReport[];
  agentRating: AgentRating;
  trainingReport: TrainingReport;
};

async function getProspectData(prospectId: string): Promise<ProspectProfile | null> {
  const supabase = await createServerClient();

  // Fetch basic prospect data
  const { data: prospect, error } = await supabase
    .from("prospects")
    .select("id, user_id, contact_info, education, experience, skills, intro_video_url, resume_url, profile_visibility, profile_image")
    .eq("id", prospectId)
    .single();
  
  if (error || !prospect) return null;

  // Get profile image URL
  let profileImageUrl = "/placeholder.svg";
  
  // First check if profile_image exists in the prospect record
  if (prospect.profile_image) {
    profileImageUrl = prospect.profile_image;
  } 
  // If not, check contact_info.avatar_url as fallback
  else if (prospect.contact_info && 
      typeof prospect.contact_info === 'object' && 
      !Array.isArray(prospect.contact_info) &&
      (prospect.contact_info as any).avatar_url) {
    profileImageUrl = (prospect.contact_info as any).avatar_url;
  }
  // If neither exists, try to get from storage bucket
  else {
    // Construct a URL to the storage bucket
    profileImageUrl = `${process.env.NEXT_PUBLIC_SUPABASE_URL}/storage/v1/object/public/profile_images/${prospectId}`;
  }

  // Type guard: contact_info should be a plain object, not an array
  let contact: ContactInfo = {};
  if (prospect.contact_info && typeof prospect.contact_info === 'object' && !Array.isArray(prospect.contact_info)) {
    contact = prospect.contact_info as ContactInfo;
  }
  
  const locationObj = contact.location && typeof contact.location === 'object' ? contact.location : {};
  const location = locationObj.city || locationObj.country ? `${locationObj.city || ''}${locationObj.city && locationObj.country ? ', ' : ''}${locationObj.country || ''}` : '';

  // Fetch certificates from files table
  const { data: certificates } = await supabase
    .from("files")
    .select(`
      id,
      issued_at,
      file_url,
      module_id,
      training_modules(title)
    `)
    .eq("prospect_id", prospectId)
    .in("file_type", ["certificate", "training_certificate"]);

  // Format certificates
  const formattedCertificates: Certificate[] = certificates?.map((cert: any) => ({
    id: cert.id,
    module_title: cert.training_modules?.title || "Unknown Module",
    issue_date: cert.issued_at || "",
    certificate_url: cert.file_url || ""
  })) || [];

  // Fetch badges
  const { data: badges } = await supabase
    .from("badges")
    .select("*")
    .eq("prospect_id", prospectId);

  // Format badges
  const formattedBadges: Badge[] = badges?.map((badge: any) => ({
    id: badge.id,
    title: badge.title || "Achievement Badge",
    description: badge.description || "",
    badge_type: badge.badge_type || "achievement",
    image_url: badge.image_url,
    date_earned: badge.created_at
  })) || [];

  // Fetch assessment completions
  const { data: assessmentCompletions } = await supabase
    .from("assessment_completions")
    .select(`
      id,
      score,
      completed_at,
      assessment_id,
      assessments(title, category)
    `)
    .eq("user_id", prospect.user_id)
    .eq("status", "completed");

  // Format assessment reports
  const formattedAssessments: AssessmentReport[] = assessmentCompletions?.map((completion: any) => ({
    id: completion.id,
    title: completion.assessments?.title || "Unknown Assessment",
    score: completion.score || 0,
    completion_date: completion.completed_at || "",
    category: completion.assessments?.category || "General"
  })) || [];

  // Fetch call practice sessions for agent rating calculation
  const { data: callPractice } = await supabase
    .from("call_practice_sessions")
    .select("duration_minutes, score, metrics")
    .eq("prospect_id", prospectId);

  // Calculate agent rating
  let agentRating: AgentRating = {
    overall_score: 0,
    communication: 0,
    problem_solving: 0,
    customer_service: 0,
    technical_knowledge: 0,
    last_updated: new Date().toISOString()
  };

  // Calculate training report
  let trainingReport: TrainingReport = {
    progress_percentage: 0,
    completed_modules: 0,
    total_modules: 0,
    call_practice_hours: 0,
    call_practice_score: 0
  };

  if (callPractice && callPractice.length > 0) {
    // Sum up practice hours
    trainingReport.call_practice_hours = callPractice.reduce(
      (sum: number, session: any) => sum + ((session.duration_minutes || 0) / 60), 
      0
    );
    
    // Average score
    const totalScore = callPractice.reduce(
      (sum: number, session: any) => sum + (session.score || 0), 
      0
    );
    
    trainingReport.call_practice_score = Math.round(totalScore / callPractice.length);
    
    // Agent rating from call metrics (if available)
    let metricCounts = {
      communication: 0,
      problem_solving: 0,
      customer_service: 0,
      technical_knowledge: 0
    };
    
    let metricTotals = {
      communication: 0,
      problem_solving: 0,
      customer_service: 0,
      technical_knowledge: 0
    };
    
    // Calculate average scores from call practice metrics
    callPractice.forEach((session: any) => {
      if (session.metrics && typeof session.metrics === 'object') {
        const metrics = session.metrics as Record<string, number>;
        
        if ('communication' in metrics) {
          metricTotals.communication += metrics.communication;
          metricCounts.communication++;
        }
        
        if ('problem_solving' in metrics) {
          metricTotals.problem_solving += metrics.problem_solving;
          metricCounts.problem_solving++;
        }
        
        if ('customer_service' in metrics) {
          metricTotals.customer_service += metrics.customer_service;
          metricCounts.customer_service++;
        }
        
        if ('technical_knowledge' in metrics) {
          metricTotals.technical_knowledge += metrics.technical_knowledge;
          metricCounts.technical_knowledge++;
        }
      }
    });
    
    // Calculate averages
    agentRating.communication = metricCounts.communication > 0 
      ? Math.round(metricTotals.communication / metricCounts.communication) 
      : 0;
      
    agentRating.problem_solving = metricCounts.problem_solving > 0 
      ? Math.round(metricTotals.problem_solving / metricCounts.problem_solving) 
      : 0;
      
    agentRating.customer_service = metricCounts.customer_service > 0 
      ? Math.round(metricTotals.customer_service / metricCounts.customer_service) 
      : 0;
      
    agentRating.technical_knowledge = metricCounts.technical_knowledge > 0 
      ? Math.round(metricTotals.technical_knowledge / metricCounts.technical_knowledge) 
      : 0;
    
    // Overall score is average of all metrics
    const nonZeroScores = [
      agentRating.communication,
      agentRating.problem_solving,
      agentRating.customer_service,
      agentRating.technical_knowledge
    ].filter(score => score > 0);
    
    if (nonZeroScores.length > 0) {
      agentRating.overall_score = Math.round(
        nonZeroScores.reduce((sum, score) => sum + score, 0) / nonZeroScores.length
      );
    }
  }

  // Fetch training module progress
  const { data: modules } = await supabase
    .from("training_modules")
    .select("id, title")
    .eq("status", "published");

  if (modules) {
    trainingReport.total_modules = modules.length;

    // Get progress records
    const { data: progress } = await supabase
      .from("progress_records")
      .select(`
        id,
        status,
        activity_id,
        activities(
          id,
          lesson_id,
          lessons(
            id,
            module_id
          )
        )
      `)
      .eq("prospect_id", prospectId);

    if (progress) {
      // Count completed modules
      const moduleActivityCounts = new Map<string, number>();
      const moduleCompletionCounts = new Map<string, number>();
      const moduleProgress = new Set<string>();

      // Count activities per module
      progress.forEach((record: any) => {
        const moduleId = record.activities?.lessons?.module_id;
        if (moduleId) {
          moduleProgress.add(moduleId);

          const currentCount = moduleActivityCounts.get(moduleId) || 0;
          moduleActivityCounts.set(moduleId, currentCount + 1);

          if (record.status === 'completed') {
            const completedCount = moduleCompletionCounts.get(moduleId) || 0;
            moduleCompletionCounts.set(moduleId, completedCount + 1);
          }
        }
      });

      // Count completed modules
      let completedCount = 0;

      moduleProgress.forEach((hasProgress, moduleId) => {
        const totalActivities = moduleActivityCounts.get(moduleId) || 0;
        const completedActivities = moduleCompletionCounts.get(moduleId) || 0;

        if (totalActivities > 0 && completedActivities >= totalActivities) {
          completedCount++;
        }
      });

      trainingReport.completed_modules = completedCount;

      // Calculate overall progress percentage
      if (trainingReport.total_modules > 0) {
        const totalActivities = Array.from(moduleActivityCounts.values()).reduce((sum, count) => sum + count, 0);
        const completedActivities = progress.filter((p: any) => p.status === 'completed').length;

        if (totalActivities > 0) {
          trainingReport.progress_percentage = Math.round((completedActivities / totalActivities) * 100);
        }
      }
    }
  }

  return {
    name: contact.name || '',
    title: contact.title || '',
    avatarUrl: profileImageUrl,
    location,
    email: contact.email || '',
    phone: contact.phone || '',
    about: contact.about || '',
    introVideoUrl: prospect.intro_video_url || '',
    experience: Array.isArray(prospect.experience) ? prospect.experience : [],
    education: Array.isArray(prospect.education) ? prospect.education : [],
    skills: Array.isArray(prospect.skills) ? prospect.skills : [],
    certificates: formattedCertificates,
    badges: formattedBadges,
    assessmentReports: formattedAssessments,
    agentRating,
    trainingReport
  };
}

export default async function BPOCandidateProfilePage({ params }: { params: Promise<{ prospectId: string }> }) {
  const { prospectId } = await params;
  const candidate = await getProspectData(prospectId);
  if (!candidate) {
    return <div className="p-8 text-center text-muted-foreground">Profile not found.</div>;
  }

  return <BPOCandidateProfileClient candidate={candidate} prospectId={prospectId} />;
}
