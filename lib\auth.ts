import { SupabaseClient, User } from '@supabase/supabase-js';
import { NextRequest, NextResponse } from 'next/server';
import { createApiClient, createServerClient } from '@/lib/supabase-server';
import { isPlatformAdmin, isBPOAdmin, getUserBPOMemberships } from '@/lib/auth-utils';
import type { Database } from '@/types/database.types';

// =============================================================================
// AUTHENTICATION TYPES
// =============================================================================

export interface AuthUser {
  id: string;
  email: string;
  role: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member';
  full_name?: string;
  avatar_url?: string;
  timezone?: string;
  isPlatformAdmin: boolean;
  isOrgOwner: boolean;
  isOrgAdmin: boolean;
  organizationMemberships: Array<{
    organization_id: string;
    role: string;
    organization_name?: string;
    status: string;
  }>;
  currentContext?: {
    type: 'individual' | 'organization';
    organization_id?: string;
  };
}

export interface AuthResult {
  user: AuthUser | null;
  error: string | null;
  status: number;
}

// =============================================================================
// SECURE AUTHENTICATION FUNCTIONS
// =============================================================================

/**
 * Get authenticated user with secure validation for API routes
 * Uses getUser() instead of getSession() for better security
 */
export async function getAuthenticatedUser(): Promise<AuthResult> {
  try {
    console.log('[AUTH] 🚀 Starting simplified getAuthenticatedUser...');
    const supabase = await createApiClient();
    console.log('[AUTH] ✅ Supabase client created successfully');

    // Use getUser() for authentication
    console.log('[AUTH] 🔍 Calling supabase.auth.getUser()...');
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    console.log('[AUTH] 📊 Auth getUser result:', {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      userErrorMessage: userError?.message
    });

    if (userError || !user) {
      console.log('[AUTH] ❌ Auth failed - no user or error:', userError);
      return {
        user: null,
        error: 'Unauthorized - Authentication required',
        status: 401
      };
    }

    // Get user details from database (no RLS, direct query)
    console.log('[AUTH] 🔍 Querying users table for user:', user.id);
    const { data: userData, error: dbError } = await supabase
      .from('users')
      .select('id, email, role, full_name, avatar_url, timezone')
      .eq('id', user.id)
      .single();

    console.log('[AUTH] 📊 Database query result:', {
      hasUserData: !!userData,
      userData: userData,
      dbErrorMessage: dbError?.message
    });

    if (dbError || !userData) {
      console.log('[AUTH] ❌ Database error or user not found:', dbError);
      return {
        user: null,
        error: 'User not found in database',
        status: 404
      };
    }

    // Simple role-based checks (no RLS functions needed)
    console.log('[AUTH] 🔍 Determining user permissions...');
    const isPlatformAdminUser = userData.role === 'platform_admin';
    const isOrgOwner = userData.role === 'org_owner';
    const isOrgAdmin = userData.role === 'org_admin';
    const isOrgMember = userData.role === 'org_member';
    const isIndividual = userData.role === 'individual';

    // Get organization memberships (simplified query)
    console.log('[AUTH] 🔍 Querying organization memberships...');
    const { data: memberships, error: membershipError } = await supabase
      .from('organization_memberships')
      .select(`
        organization_id,
        role,
        status,
        organizations(name)
      `)
      .eq('user_id', user.id)
      .eq('status', 'active');

    console.log('[AUTH] 📊 Organization memberships result:', {
      hasMemberships: !!memberships,
      membershipCount: memberships?.length || 0,
      membershipError: membershipError?.message
    });

    const organizationMemberships = memberships?.map(m => ({
      organization_id: m.organization_id,
      role: m.role,
      organization_name: (m.organizations as any)?.name,
      status: m.status || 'active'
    })) || [];

    // Get current user context (simplified)
    console.log('[AUTH] 🔍 Querying user context...');
    const { data: contextData, error: contextError } = await supabase
      .from('user_contexts')
      .select('active_context, active_organization_id')
      .eq('user_id', user.id)
      .single();

    console.log('[AUTH] 📊 User context result:', {
      hasContext: !!contextData,
      contextData: contextData,
      contextError: contextError?.message
    });

    console.log('[AUTH] 🔧 Building simplified AuthUser object...');
    const authUser: AuthUser = {
      id: userData.id,
      email: userData.email,
      role: userData.role,
      full_name: userData.full_name,
      avatar_url: userData.avatar_url,
      timezone: userData.timezone,
      isPlatformAdmin: isPlatformAdminUser,
      isOrgOwner: isOrgOwner,
      isOrgAdmin: isOrgAdmin || organizationMemberships.some(m => m.role === 'admin'),
      organizationMemberships,
      currentContext: contextData ? {
        type: contextData.active_context,
        organization_id: contextData.active_organization_id
      } : {
        type: isIndividual ? 'individual' : 'organization',
        organization_id: null
      }
    };

    console.log('[AUTH] ✅ Authentication successful! User:', {
      id: authUser.id,
      email: authUser.email,
      role: authUser.role,
      isPlatformAdmin: authUser.isPlatformAdmin,
      organizationCount: authUser.organizationMemberships.length,
      currentContext: authUser.currentContext
    });

    return {
      user: authUser,
      error: null,
      status: 200
    };

  } catch (error) {
    console.error('[AUTH] ❌ Authentication error:', error);
    return {
      user: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

/**
 * Get authenticated user for server components
 */
export async function getServerAuthUser(): Promise<AuthResult> {
  try {
    const supabase = await createServerClient();
    
    // Use getUser() for secure authentication
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    if (userError || !user) {
      return {
        user: null,
        error: 'Unauthorized - Authentication required',
        status: 401
      };
    }

    // Get user details from database
    const { data: userData, error: dbError } = await supabase
      .from('users')
      .select('id, email, role, full_name, avatar_url, timezone')
      .eq('id', user.id)
      .single();

    if (dbError || !userData) {
      return {
        user: null,
        error: 'User not found in database',
        status: 404
      };
    }

    // Check platform admin status using secure functions
    const isPlatformAdminUser = await isPlatformAdmin(supabase, user.id);

    // Get organization memberships
    const { data: memberships, error: membershipError } = await supabase
      .from('organization_memberships')
      .select(`
        organization_id,
        role,
        status,
        organizations!inner(name)
      `)
      .eq('user_id', user.id)
      .eq('status', 'active');

    const organizationMemberships = memberships?.map(m => ({
      organization_id: m.organization_id,
      role: m.role,
      organization_name: (m.organizations as any)?.name,
      status: m.status || 'active'
    })) || [];

    // Get current user context
    const { data: contextData } = await supabase
      .from('user_contexts')
      .select('active_context, active_organization_id')
      .eq('user_id', user.id)
      .single();

    const authUser: AuthUser = {
      id: userData.id,
      email: userData.email,
      role: userData.role,
      full_name: userData.full_name,
      avatar_url: userData.avatar_url,
      timezone: userData.timezone,
      isPlatformAdmin: isPlatformAdminUser,
      isOrgOwner: userData.role === 'org_owner',
      isOrgAdmin: userData.role === 'org_admin' || organizationMemberships.some(m => m.role === 'admin'),
      organizationMemberships,
      currentContext: contextData ? {
        type: contextData.active_context,
        organization_id: contextData.active_organization_id
      } : undefined
    };

    return {
      user: authUser,
      error: null,
      status: 200
    };

  } catch (error) {
    console.error('Server authentication error:', error);
    return {
      user: null,
      error: 'Authentication failed',
      status: 500
    };
  }
}

// =============================================================================
// ROLE-BASED ACCESS CONTROL
// =============================================================================

/**
 * Require platform admin access
 */
export async function requirePlatformAdmin(): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser();
  
  if (!authResult.user) {
    return authResult;
  }

  if (!authResult.user.isPlatformAdmin) {
    return {
      user: null,
      error: 'Forbidden - Platform admin access required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Require organization admin access for a specific organization
 */
export async function requireOrgAdmin(organizationId: string): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser();

  if (!authResult.user) {
    return authResult;
  }

  // Platform admins can access any organization
  if (authResult.user.isPlatformAdmin) {
    return authResult;
  }

  // Check if user is admin of the specific organization
  const isOrgAdminForThisOrg = authResult.user.organizationMemberships.some(
    m => m.organization_id === organizationId && (m.role === 'admin' || m.role === 'owner')
  );

  if (!isOrgAdminForThisOrg) {
    return {
      user: null,
      error: 'Forbidden - Organization admin access required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Require organization member access for a specific organization
 */
export async function requireOrgMember(organizationId: string): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser();

  if (!authResult.user) {
    return authResult;
  }

  // Platform admins can access any organization
  if (authResult.user.isPlatformAdmin) {
    return authResult;
  }

  // Check if user is member of the specific organization
  const isOrgMember = authResult.user.organizationMemberships.some(
    m => m.organization_id === organizationId
  );

  if (!isOrgMember) {
    return {
      user: null,
      error: 'Forbidden - Organization member access required',
      status: 403
    };
  }

  return authResult;
}

/**
 * Require individual user access (for individual context features)
 */
export async function requireIndividualUser(): Promise<AuthResult> {
  const authResult = await getAuthenticatedUser();

  if (!authResult.user) {
    return authResult;
  }

  // Check if user has individual role or can operate in individual context
  if (authResult.user.role !== 'individual' && authResult.user.role !== 'platform_admin') {
    return {
      user: null,
      error: 'Forbidden - Individual user access required',
      status: 403
    };
  }

  return authResult;
}

// =============================================================================
// RESPONSE HELPERS
// =============================================================================

/**
 * Create standardized error response
 */
export function createAuthErrorResponse(authResult: AuthResult): NextResponse {
  return NextResponse.json(
    { error: authResult.error },
    { status: authResult.status }
  );
}

/**
 * Handle authentication in API routes with consistent error responses
 */
export async function withAuth<T>(
  handler: (user: AuthUser) => Promise<T>,
  requireAdmin = false
): Promise<NextResponse> {
  try {
    const authResult = requireAdmin 
      ? await requirePlatformAdmin()
      : await getAuthenticatedUser();

    if (!authResult.user) {
      return createAuthErrorResponse(authResult);
    }

    const result = await handler(authResult.user);
    return NextResponse.json(result);

  } catch (error) {
    console.error('API handler error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

// =============================================================================
// CONTEXT SWITCHING
// =============================================================================

/**
 * Switch user context between individual and organization modes
 */
export async function switchUserContext(
  userId: string,
  contextType: 'individual' | 'organization',
  organizationId?: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createServerClient();

    // Validate organization access if switching to organization context
    if (contextType === 'organization' && organizationId) {
      const { data: membership } = await supabase
        .from('organization_memberships')
        .select('id')
        .eq('user_id', userId)
        .eq('organization_id', organizationId)
        .eq('status', 'active')
        .single();

      if (!membership) {
        return { success: false, error: 'No access to specified organization' };
      }
    }

    // Update user context
    const { error } = await supabase
      .from('user_contexts')
      .upsert({
        user_id: userId,
        active_context: contextType,
        active_organization_id: contextType === 'organization' ? organizationId : null,
        last_context_switch: new Date().toISOString(),
        updated_at: new Date().toISOString()
      });

    if (error) {
      console.error('Context switch error:', error);
      return { success: false, error: 'Failed to switch context' };
    }

    return { success: true };

  } catch (error) {
    console.error('Context switch error:', error);
    return { success: false, error: 'Failed to switch context' };
  }
}
