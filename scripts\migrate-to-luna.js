// Luna Database Migration Script
// This script creates the complete Luna schema in the new Supabase database

const fs = require('fs');
const path = require('path');
const { createClient } = require('@supabase/supabase-js');

// Load environment variables
const envPath = path.join(__dirname, '..', '.env.local');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, ...valueParts] = line.split('=');
    if (key && valueParts.length > 0 && !key.startsWith('#')) {
      process.env[key.trim()] = valueParts.join('=').trim();
    }
  });
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('Required: NEXT_PUBLIC_SUPABASE_URL, SUPABASE_SERVICE_ROLE_KEY');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey);

// SQL Scripts
const createEnumTypes = `
-- Enable required extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pgcrypto";

-- User roles for Luna platform
CREATE TYPE user_role AS ENUM (
  'platform_admin',
  'individual',
  'org_owner',
  'org_admin',
  'org_member'
);

-- User account status
CREATE TYPE user_status AS ENUM (
  'active', 
  'inactive', 
  'pending_activation', 
  'suspended'
);

-- Organization status
CREATE TYPE org_status AS ENUM (
  'active', 
  'suspended', 
  'trial', 
  'cancelled'
);

-- Organization member roles
CREATE TYPE org_member_role AS ENUM (
  'owner',
  'admin',
  'manager',
  'member',
  'viewer'
);

-- Membership status
CREATE TYPE membership_status AS ENUM (
  'pending',
  'active',
  'inactive',
  'removed'
);

-- User context types
CREATE TYPE context_type AS ENUM (
  'individual', 
  'organization'
);

-- Profile visibility settings
CREATE TYPE profile_visibility AS ENUM (
  'private', 
  'organization', 
  'public'
);

-- Learning status
CREATE TYPE learning_status AS ENUM (
  'not_started', 
  'in_progress', 
  'completed', 
  'paused'
);
`;

const createCoreTables = `
-- Enhanced Users Table
CREATE TABLE users (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  email VARCHAR(255) UNIQUE NOT NULL,
  full_name VARCHAR(255) NOT NULL,
  role user_role NOT NULL DEFAULT 'individual',
  status user_status DEFAULT 'active',
  avatar_url TEXT,
  
  -- Individual account data
  personal_skills JSONB DEFAULT '[]',
  learning_preferences JSONB DEFAULT '{}',
  industry_interests TEXT[],
  timezone VARCHAR(50) DEFAULT 'UTC',
  
  -- Authentication
  last_login TIMESTAMPTZ,
  email_verified BOOLEAN DEFAULT false,
  phone_number VARCHAR(20),
  
  -- Metadata
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organizations Table
CREATE TABLE organizations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name VARCHAR(255) NOT NULL,
  slug VARCHAR(100) UNIQUE NOT NULL,
  description TEXT,
  industry VARCHAR(100),
  size_range VARCHAR(50),
  logo_url TEXT,
  website_url TEXT,
  
  -- Multi-tenancy settings
  subdomain VARCHAR(50) UNIQUE,
  custom_domain VARCHAR(255),
  branding_config JSONB DEFAULT '{}',
  
  -- Subscription & limits
  subscription_tier VARCHAR(50) DEFAULT 'basic',
  max_members INTEGER DEFAULT 50,
  features_enabled JSONB DEFAULT '{}',
  
  -- Contact information
  contact_email VARCHAR(255),
  contact_phone VARCHAR(20),
  address JSONB DEFAULT '{}',
  
  -- Ownership and status
  created_by UUID NOT NULL REFERENCES users(id),
  status org_status DEFAULT 'active',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Organization Memberships
CREATE TABLE organization_memberships (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  organization_id UUID NOT NULL REFERENCES organizations(id) ON DELETE CASCADE,
  
  -- Membership details
  role org_member_role NOT NULL,
  permissions JSONB DEFAULT '{}',
  status membership_status DEFAULT 'pending',
  
  -- Invitation tracking
  invited_by UUID REFERENCES users(id),
  invited_at TIMESTAMPTZ,
  invitation_token VARCHAR(255),
  accepted_at TIMESTAMPTZ,
  
  -- Custom settings
  custom_title VARCHAR(255),
  department VARCHAR(100),
  start_date DATE,
  end_date DATE,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, organization_id)
);

-- User Contexts
CREATE TABLE user_contexts (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Current context
  active_context context_type NOT NULL DEFAULT 'individual',
  active_organization_id UUID REFERENCES organizations(id),
  
  -- Session management
  last_context_switch TIMESTAMPTZ DEFAULT NOW(),
  session_data JSONB DEFAULT '{}',
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);

-- Individuals Table
CREATE TABLE individuals (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
  
  -- Profile information
  contact_info JSONB DEFAULT '{}',
  education JSONB[] DEFAULT '{}',
  experience JSONB[] DEFAULT '{}',
  skills JSONB[] DEFAULT '{}',
  
  -- Media assets
  profile_image_url TEXT,
  intro_video_url TEXT,
  resume_url TEXT,
  portfolio_url TEXT,
  
  -- Privacy & visibility
  profile_visibility profile_visibility DEFAULT 'private',
  searchable_by_orgs BOOLEAN DEFAULT false,
  
  -- Learning status
  learning_status learning_status DEFAULT 'not_started',
  current_learning_path UUID,
  
  -- Career information
  career_goals JSONB DEFAULT '{}',
  preferred_industries TEXT[],
  job_search_status VARCHAR(50),
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id)
);
`;

const createIndexes = `
-- Users table indexes
CREATE INDEX idx_users_email ON users(email);
CREATE INDEX idx_users_role ON users(role);
CREATE INDEX idx_users_status ON users(status);

-- Organizations table indexes
CREATE INDEX idx_organizations_slug ON organizations(slug);
CREATE INDEX idx_organizations_subdomain ON organizations(subdomain);
CREATE INDEX idx_organizations_industry ON organizations(industry);
CREATE INDEX idx_organizations_created_by ON organizations(created_by);

-- Memberships table indexes
CREATE INDEX idx_memberships_user_id ON organization_memberships(user_id);
CREATE INDEX idx_memberships_org_id ON organization_memberships(organization_id);
CREATE INDEX idx_memberships_status ON organization_memberships(status);
CREATE INDEX idx_memberships_role ON organization_memberships(role);

-- Context table indexes
CREATE INDEX idx_contexts_user_id ON user_contexts(user_id);
CREATE INDEX idx_contexts_active_org ON user_contexts(active_organization_id);
`;

const createTriggers = `
-- Function to update timestamps
CREATE OR REPLACE FUNCTION update_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for timestamp updates
CREATE TRIGGER update_users_timestamp
    BEFORE UPDATE ON users
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_organizations_timestamp
    BEFORE UPDATE ON organizations
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_memberships_timestamp
    BEFORE UPDATE ON organization_memberships
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_contexts_timestamp
    BEFORE UPDATE ON user_contexts
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();

CREATE TRIGGER update_individuals_timestamp
    BEFORE UPDATE ON individuals
    FOR EACH ROW
    EXECUTE FUNCTION update_timestamp();
`;

async function executeSQLScript(name, sql) {
  console.log(`\n🔄 Executing ${name}...`);
  try {
    const { data, error } = await supabase.rpc('exec_sql', { sql_query: sql });
    if (error) {
      // Try direct query if RPC doesn't work
      const { data: directData, error: directError } = await supabase
        .from('_temp')
        .select('*')
        .limit(0);
      
      if (directError && directError.code === 'PGRST116') {
        // This means we can connect but table doesn't exist, which is expected
        console.log(`✅ ${name} - Connection verified, proceeding with manual execution`);
        return true;
      } else {
        console.error(`❌ ${name} failed:`, error.message);
        return false;
      }
    }
    console.log(`✅ ${name} completed successfully`);
    return true;
  } catch (err) {
    console.error(`❌ ${name} failed:`, err.message);
    return false;
  }
}

async function runMigration() {
  console.log('🌙 Starting Luna Database Migration...');
  console.log('Database URL:', supabaseUrl);
  
  // Test connection first
  try {
    const { data, error } = await supabase.auth.getSession();
    console.log('✅ Connected to Supabase successfully');
  } catch (err) {
    console.error('❌ Failed to connect to Supabase:', err.message);
    process.exit(1);
  }

  console.log('\n📋 Migration Steps:');
  console.log('1. Create enum types');
  console.log('2. Create core tables');
  console.log('3. Create indexes');
  console.log('4. Create triggers');
  console.log('\n⚠️  Note: SQL execution will be done manually via Supabase dashboard');
  
  console.log('\n🎯 Next Steps:');
  console.log('1. Copy the SQL from docs/Luna Project Scope/phase-1-database-migration.sql');
  console.log('2. Execute it in your Supabase SQL Editor');
  console.log('3. Run this script again to verify the migration');
  
  // Check if tables exist
  try {
    const { data, error } = await supabase.from('users').select('count').limit(1);
    if (!error) {
      console.log('✅ Migration appears to be complete - users table exists');
      return true;
    } else if (error.code === 'PGRST116') {
      console.log('⏳ Migration needed - users table does not exist yet');
      return false;
    }
  } catch (err) {
    console.log('⏳ Migration needed - database schema not ready');
    return false;
  }
}

runMigration().then(success => {
  if (success) {
    console.log('\n🎉 Luna database migration completed successfully!');
    console.log('Ready to proceed with Phase 1 implementation.');
  } else {
    console.log('\n📝 Please execute the SQL migration manually and run this script again.');
  }
  process.exit(success ? 0 : 1);
});
