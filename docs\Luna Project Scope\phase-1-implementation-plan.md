# 🏗️ Phase 1: Foundation - Detailed Implementation Plan

## 🎯 Implementation Overview

**Duration**: 3-4 weeks  
**Goal**: Transform BPO Training Platform foundation into Luna's multi-tenant architecture  
**New Database**: Fresh Supabase instance with Luna-optimized schema  
**Logo**: Luna_Logo_Dark.PNG ready for integration

## 📅 Week-by-Week Implementation Plan

### 🗄️ **Week 1: Database Foundation & Schema Creation**

#### **Day 1-2: New Supabase Setup & Core Schema**

**Morning Tasks**:
- [ ] Create new Supabase project for Luna
- [ ] Configure environment variables for new database
- [ ] Set up database connection and test connectivity

**Database Schema Creation**:
```sql
-- 1. Create new enum types for Luna
CREATE TYPE user_role AS ENUM (
  'platform_admin',    -- Luna platform administrators
  'individual',        -- Standalone individual users
  'org_owner',        -- Organization owners
  'org_admin',        -- Organization administrators
  'org_member'        -- Organization team members
);

CREATE TYPE org_status AS ENUM ('active', 'suspended', 'trial', 'cancelled');
CREATE TYPE org_member_role AS ENUM ('owner', 'admin', 'manager', 'member', 'viewer');
CREATE TYPE membership_status AS ENUM ('pending', 'active', 'inactive', 'removed');
CREATE TYPE context_type AS ENUM ('individual', 'organization');
CREATE TYPE profile_visibility AS ENUM ('private', 'organization', 'public');
CREATE TYPE learning_status AS ENUM ('not_started', 'in_progress', 'completed', 'paused');

-- 2. Create core tables
-- users table (enhanced)
-- organizations table (new)
-- organization_memberships table (bridge)
-- user_contexts table (context switching)
-- individuals table (renamed from prospects)
```

**Afternoon Tasks**:
- [ ] Create all core tables with proper relationships
- [ ] Set up foreign key constraints and indexes
- [ ] Create database functions for common operations

#### **Day 3-4: Row Level Security (RLS) Implementation**

**RLS Policies Creation**:
```sql
-- Users table policies
CREATE POLICY "users_select_own" ON users
  FOR SELECT USING (auth.uid() = id);

CREATE POLICY "users_update_own" ON users
  FOR UPDATE USING (auth.uid() = id);

-- Organizations table policies
CREATE POLICY "orgs_select_members" ON organizations
  FOR SELECT USING (
    id IN (
      SELECT organization_id FROM organization_memberships 
      WHERE user_id = auth.uid() AND status = 'active'
    )
  );

-- Organization memberships policies
CREATE POLICY "memberships_select_own" ON organization_memberships
  FOR SELECT USING (
    user_id = auth.uid() OR 
    organization_id IN (
      SELECT organization_id FROM organization_memberships 
      WHERE user_id = auth.uid() AND role IN ('owner', 'admin')
    )
  );

-- Individuals table policies
CREATE POLICY "individuals_select_own" ON individuals
  FOR SELECT USING (user_id = auth.uid());
```

**Tasks**:
- [ ] Implement all RLS policies for data isolation
- [ ] Test policy enforcement with sample data
- [ ] Create database functions for membership checks
- [ ] Set up audit triggers for security events

#### **Day 5: Database Testing & Validation**

**Testing Tasks**:
- [ ] Create test users with different roles
- [ ] Test data isolation between organizations
- [ ] Validate RLS policy enforcement
- [ ] Performance testing for multi-tenant queries
- [ ] Create database backup and restore procedures

---

### 🔐 **Week 2: Authentication & Context System**

#### **Day 1-2: Enhanced Authentication System**

**Code Updates**:
```typescript
// lib/auth.ts - Enhanced authentication
interface AuthUser {
  id: string;
  email: string;
  role: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member';
  full_name: string;
  avatar_url?: string;
  
  // Context information
  activeContext: 'individual' | 'organization';
  activeOrganizationId?: string;
  organizationMemberships: OrganizationMembership[];
}

interface OrganizationMembership {
  organizationId: string;
  organizationName: string;
  role: 'owner' | 'admin' | 'manager' | 'member' | 'viewer';
  status: 'pending' | 'active' | 'inactive' | 'removed';
  permissions: Record<string, boolean>;
}
```

**Implementation Tasks**:
- [ ] Update authentication middleware for new user roles
- [ ] Implement context-aware user session management
- [ ] Create organization membership validation functions
- [ ] Update user registration flow for individual-first approach

#### **Day 3-4: Context Switching Infrastructure**

**Context Management System**:
```typescript
// lib/context-manager.ts
class ContextManager {
  async switchContext(userId: string, newContext: UserContext): Promise<void>
  async getCurrentContext(userId: string): Promise<UserContext>
  async getAvailableContexts(userId: string): Promise<UserContext[]>
  async validateContextAccess(userId: string, context: UserContext): Promise<boolean>
}

interface UserContext {
  type: 'individual' | 'organization';
  organizationId?: string;
  organizationSlug?: string;
  permissions: string[];
}
```

**Implementation Tasks**:
- [ ] Build context switching service
- [ ] Create context validation middleware
- [ ] Implement session-based context storage
- [ ] Create context switching API endpoints

#### **Day 5: Organization Management System**

**Organization Features**:
- [ ] Organization creation and setup flow
- [ ] Team member invitation system
- [ ] Membership acceptance/rejection workflow
- [ ] Organization settings and configuration
- [ ] Slug generation and validation for custom URLs

---

### 🛣️ **Week 3: Portal Restructuring & Routing**

#### **Day 1-2: Routing System Updates**

**Current → New Route Mapping**:
```typescript
// Route transformations
const routeMapping = {
  // Old BPO routes → New Organization routes
  '/bpo/dashboard': '/org/[orgSlug]/dashboard',
  '/bpo/candidates': '/org/[orgSlug]/team',
  '/bpo/jobs': '/org/[orgSlug]/jobs',
  '/bpo/settings': '/org/[orgSlug]/settings',
  
  // Old Prospect routes → New User routes
  '/prospect/dashboard': '/user/dashboard',
  '/prospect/training': '/user/learning-paths',
  '/prospect/job-board': '/user/job-board',
  '/prospect/profile': '/user/profile',
  
  // Context-aware routes
  '/dashboard': '/dashboard', // Redirects based on active context
  '/profile': '/profile',     // Context-aware profile page
};
```

**Implementation Tasks**:
- [ ] Update Next.js routing structure
- [ ] Implement organization slug-based routing
- [ ] Create context-aware route protection middleware
- [ ] Set up URL rewriting for multi-tenant access

#### **Day 3-4: Portal Transformation**

**Organization Portal Updates**:
- [ ] Transform BPO dashboard to Organization dashboard
- [ ] Update navigation for organization context
- [ ] Implement organization branding system
- [ ] Create team management interface
- [ ] Add organization settings and configuration

**User Portal Updates**:
- [ ] Transform Prospect dashboard to User dashboard
- [ ] Update navigation for individual context
- [ ] Implement personal learning dashboard
- [ ] Create individual profile management
- [ ] Add context switching UI components

#### **Day 5: Multi-Tenant URL Handling**

**URL Structure Implementation**:
```typescript
// Multi-tenant URL patterns
const urlPatterns = {
  // Organization-specific URLs
  orgSlug: '/org/[orgSlug]/*',           // luna.app/org/acme-corp
  subdomain: '[orgSlug].luna.app/*',     // acme-corp.luna.app
  customDomain: 'custom.domain.com/*',   // training.acme-corp.com
  
  // Individual user URLs
  individual: '/user/*',                 // luna.app/user/dashboard
  contextAware: '/dashboard',            // Redirects based on context
};
```

**Implementation Tasks**:
- [ ] Implement subdomain routing logic
- [ ] Create custom domain support infrastructure
- [ ] Set up organization slug validation and uniqueness
- [ ] Test multi-tenant URL resolution

---

### 🔧 **Week 4: Integration, Testing & Branding**

#### **Day 1-2: Application Integration**

**Code Integration Tasks**:
- [ ] Update all database queries for new schema
- [ ] Connect application to new Luna database
- [ ] Update TypeScript types for new database structure
- [ ] Migrate existing API routes to new endpoints

**Type Definitions Update**:
```typescript
// types/database.types.ts - Updated for Luna schema
export interface Database {
  public: {
    Tables: {
      users: {
        Row: {
          id: string;
          email: string;
          full_name: string;
          role: 'platform_admin' | 'individual' | 'org_owner' | 'org_admin' | 'org_member';
          // ... other fields
        };
        // ... Insert and Update types
      };
      organizations: {
        // ... Organization table types
      };
      // ... other tables
    };
  };
}
```

#### **Day 3: Luna Branding Integration**

**Branding Updates**:
- [ ] Replace BPO logos with Luna_Logo_Dark.PNG
- [ ] Update application title and metadata
- [ ] Change color scheme to Luna branding
- [ ] Update favicon and app icons
- [ ] Modify welcome messages and copy

**UI Component Updates**:
```typescript
// components/layout/header.tsx
const Header = () => {
  return (
    <header>
      <img src="/Luna_Logo_Dark.PNG" alt="Luna" className="h-8 w-auto" />
      <h1>Luna Skills Platform</h1>
      {/* Context switcher component */}
      <ContextSwitcher />
    </header>
  );
};
```

#### **Day 4-5: Comprehensive Testing**

**Testing Checklist**:

**Unit Tests**:
- [ ] User context switching logic
- [ ] Organization membership validation
- [ ] Permission checking functions
- [ ] Data isolation utilities

**Integration Tests**:
- [ ] Complete user registration and onboarding
- [ ] Organization creation and team setup
- [ ] Context switching scenarios
- [ ] Multi-tenant data separation

**Security Tests**:
- [ ] RLS policy enforcement
- [ ] Cross-tenant data access prevention
- [ ] Permission boundary validation
- [ ] Authentication flow security

**Performance Tests**:
- [ ] Context switching response times (<500ms)
- [ ] Database query performance
- [ ] Multi-tenant query isolation
- [ ] Concurrent user handling

**User Acceptance Tests**:
- [ ] End-to-end user workflows
- [ ] Organization admin workflows
- [ ] Context switching user experience
- [ ] Mobile responsiveness

## 🎯 Phase 1 Success Criteria

### **Technical Validation**
- [ ] All new database tables created and properly indexed
- [ ] RLS policies prevent cross-tenant data access
- [ ] Context switching works seamlessly (<500ms)
- [ ] Multi-tenant routing functions correctly
- [ ] All existing features work in new structure

### **User Experience Validation**
- [ ] Users can create individual accounts
- [ ] Users can create and manage organizations
- [ ] Team invitation and joining process works smoothly
- [ ] Context switching is intuitive and clear
- [ ] Luna branding is consistently applied

### **Security Validation**
- [ ] Zero cross-tenant data leakage
- [ ] All authentication flows secure
- [ ] Permission boundaries properly enforced
- [ ] Audit trails capture all security events

### **Performance Validation**
- [ ] Page load times remain under 2 seconds
- [ ] Database queries perform within acceptable limits
- [ ] System handles expected concurrent user load
- [ ] Memory usage stays within bounds

## 🚨 Risk Mitigation & Rollback Plan

### **Database Migration Risks**
- **Backup Strategy**: Full database backup before any changes
- **Rollback Plan**: Documented procedure to revert to original database
- **Parallel Testing**: Test new schema with sample data before migration

### **Authentication Risks**
- **Session Management**: Ensure no user sessions are lost during transition
- **Permission Validation**: Extensive testing of new permission system
- **Fallback Authentication**: Maintain ability to revert to original auth system

### **Performance Risks**
- **Load Testing**: Test system under expected load before go-live
- **Monitoring**: Set up comprehensive monitoring for new features
- **Optimization**: Database query optimization and indexing strategy

## 📋 Implementation Checklist

### **Pre-Implementation**
- [ ] New Supabase project created and configured
- [ ] Development environment set up with new database
- [ ] Team access and permissions configured
- [ ] Backup procedures established

### **Week 1 Completion**
- [ ] All database tables created and tested
- [ ] RLS policies implemented and validated
- [ ] Database functions and triggers working
- [ ] Performance benchmarks established

### **Week 2 Completion**
- [ ] Enhanced authentication system deployed
- [ ] Context switching infrastructure functional
- [ ] Organization management system operational
- [ ] API endpoints updated and tested

### **Week 3 Completion**
- [ ] Portal restructuring complete
- [ ] Multi-tenant routing functional
- [ ] Organization and user portals updated
- [ ] URL handling working correctly

### **Week 4 Completion**
- [ ] Full system integration complete
- [ ] Luna branding applied throughout
- [ ] Comprehensive testing passed
- [ ] Documentation updated

---

*Upon successful completion of Phase 1, the foundation will be ready for Phase 2: Core Features implementation.*
