-- Performance Indexes Migration
-- Adds critical indexes for frequently queried columns to improve performance

-- =============================================================================
-- USERS TABLE INDEXES
-- =============================================================================

-- Index for email lookups (authentication)
CREATE INDEX IF NOT EXISTS idx_users_email ON public.users(email);

-- Index for role-based queries
CREATE INDEX IF NOT EXISTS idx_users_role ON public.users(role);

-- Index for status filtering
CREATE INDEX IF NOT EXISTS idx_users_status ON public.users(status);

-- Composite index for role and status queries
CREATE INDEX IF NOT EXISTS idx_users_role_status ON public.users(role, status);

-- =============================================================================
-- PROSPECTS TABLE INDEXES
-- =============================================================================

-- Index for user_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_prospects_user_id ON public.prospects(user_id);

-- Index for training status filtering
CREATE INDEX IF NOT EXISTS idx_prospects_training_status ON public.prospects(training_status);

-- Index for profile visibility filtering
CREATE INDEX IF NOT EXISTS idx_prospects_profile_visibility ON public.prospects(profile_visibility);

-- Composite index for training status and visibility
CREATE INDEX IF NOT EXISTS idx_prospects_training_visibility ON public.prospects(training_status, profile_visibility);

-- =============================================================================
-- BPO_TEAMS TABLE INDEXES
-- =============================================================================

-- Index for bpo_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_bpo_teams_bpo_id ON public.bpo_teams(bpo_id);

-- Index for user_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_bpo_teams_user_id ON public.bpo_teams(user_id);

-- Index for role filtering
CREATE INDEX IF NOT EXISTS idx_bpo_teams_role ON public.bpo_teams(role);

-- Composite index for BPO and user queries
CREATE INDEX IF NOT EXISTS idx_bpo_teams_bpo_user ON public.bpo_teams(bpo_id, user_id);

-- Composite index for BPO and role queries
CREATE INDEX IF NOT EXISTS idx_bpo_teams_bpo_role ON public.bpo_teams(bpo_id, role);

-- =============================================================================
-- TRAINING_MODULES TABLE INDEXES
-- =============================================================================

-- Index for status filtering (published modules)
CREATE INDEX IF NOT EXISTS idx_training_modules_status ON public.training_modules(status);

-- Index for created_by foreign key lookups
CREATE INDEX IF NOT EXISTS idx_training_modules_created_by ON public.training_modules(created_by);

-- Index for required_order sorting
CREATE INDEX IF NOT EXISTS idx_training_modules_required_order ON public.training_modules(required_order);

-- Composite index for status and order queries
CREATE INDEX IF NOT EXISTS idx_training_modules_status_order ON public.training_modules(status, required_order);

-- =============================================================================
-- LESSONS TABLE INDEXES
-- =============================================================================

-- Index for module_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_lessons_module_id ON public.lessons(module_id);

-- Index for order_index sorting
CREATE INDEX IF NOT EXISTS idx_lessons_order_index ON public.lessons(order_index);

-- Index for lesson_type filtering
CREATE INDEX IF NOT EXISTS idx_lessons_lesson_type ON public.lessons(lesson_type);

-- Composite index for module and order queries
CREATE INDEX IF NOT EXISTS idx_lessons_module_order ON public.lessons(module_id, order_index);

-- =============================================================================
-- ACTIVITIES TABLE INDEXES
-- =============================================================================

-- Index for lesson_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_activities_lesson_id ON public.activities(lesson_id);

-- Index for type filtering
CREATE INDEX IF NOT EXISTS idx_activities_type ON public.activities(type);

-- Index for order_index sorting
CREATE INDEX IF NOT EXISTS idx_activities_order_index ON public.activities(order_index);

-- Composite index for lesson and order queries
CREATE INDEX IF NOT EXISTS idx_activities_lesson_order ON public.activities(lesson_id, order_index);

-- =============================================================================
-- PROGRESS_RECORDS TABLE INDEXES
-- =============================================================================

-- Index for prospect_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_progress_records_prospect_id ON public.progress_records(prospect_id);

-- Index for activity_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_progress_records_activity_id ON public.progress_records(activity_id);

-- Index for status filtering
CREATE INDEX IF NOT EXISTS idx_progress_records_status ON public.progress_records(status);

-- Index for module assessment filtering
CREATE INDEX IF NOT EXISTS idx_progress_records_is_module_assessment ON public.progress_records(is_module_assessment);

-- Composite index for prospect and status queries
CREATE INDEX IF NOT EXISTS idx_progress_records_prospect_status ON public.progress_records(prospect_id, status);

-- Composite index for prospect and activity queries
CREATE INDEX IF NOT EXISTS idx_progress_records_prospect_activity ON public.progress_records(prospect_id, activity_id);

-- =============================================================================
-- JOB_POSTINGS TABLE INDEXES
-- =============================================================================

-- Index for bpo_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_job_postings_bpo_id ON public.job_postings(bpo_id);

-- Index for status filtering
CREATE INDEX IF NOT EXISTS idx_job_postings_status ON public.job_postings(status);

-- Index for job_type filtering
CREATE INDEX IF NOT EXISTS idx_job_postings_job_type ON public.job_postings(job_type);

-- Index for application_deadline sorting
CREATE INDEX IF NOT EXISTS idx_job_postings_deadline ON public.job_postings(application_deadline);

-- Composite index for BPO and status queries
CREATE INDEX IF NOT EXISTS idx_job_postings_bpo_status ON public.job_postings(bpo_id, status);

-- =============================================================================
-- ASSESSMENTS TABLE INDEXES
-- =============================================================================

-- Index for category filtering
CREATE INDEX IF NOT EXISTS idx_assessments_category ON public.assessments(category);

-- Index for is_active filtering
CREATE INDEX IF NOT EXISTS idx_assessments_is_active ON public.assessments(is_active);

-- Index for order_index sorting
CREATE INDEX IF NOT EXISTS idx_assessments_order_index ON public.assessments(order_index);

-- Composite index for active assessments by category
CREATE INDEX IF NOT EXISTS idx_assessments_active_category ON public.assessments(is_active, category);

-- =============================================================================
-- ASSESSMENT_QUESTIONS TABLE INDEXES
-- =============================================================================

-- Index for assessment_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_assessment_questions_assessment_id ON public.assessment_questions(assessment_id);

-- Index for question_type filtering
CREATE INDEX IF NOT EXISTS idx_assessment_questions_type ON public.assessment_questions(question_type);

-- =============================================================================
-- MODULE_ASSESSMENTS TABLE INDEXES
-- =============================================================================

-- Index for module_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_module_assessments_module_id ON public.module_assessments(module_id);

-- Index for assessment_id foreign key lookups
CREATE INDEX IF NOT EXISTS idx_module_assessments_assessment_id ON public.module_assessments(assessment_id);

-- Index for is_required filtering
CREATE INDEX IF NOT EXISTS idx_module_assessments_is_required ON public.module_assessments(is_required);

-- Index for order_index sorting
CREATE INDEX IF NOT EXISTS idx_module_assessments_order_index ON public.module_assessments(order_index);

-- Composite index for module and order queries
CREATE INDEX IF NOT EXISTS idx_module_assessments_module_order ON public.module_assessments(module_id, order_index);

-- =============================================================================
-- TIMESTAMP INDEXES FOR AUDIT QUERIES
-- =============================================================================

-- Indexes for created_at timestamps (useful for analytics and reporting)
CREATE INDEX IF NOT EXISTS idx_users_created_at ON public.users(created_at);
CREATE INDEX IF NOT EXISTS idx_prospects_created_at ON public.prospects(created_at);
CREATE INDEX IF NOT EXISTS idx_bpos_created_at ON public.bpos(created_at);
CREATE INDEX IF NOT EXISTS idx_training_modules_created_at ON public.training_modules(created_at);
CREATE INDEX IF NOT EXISTS idx_progress_records_created_at ON public.progress_records(created_at);
CREATE INDEX IF NOT EXISTS idx_job_postings_created_at ON public.job_postings(created_at);

-- Indexes for updated_at timestamps (useful for sync and change tracking)
CREATE INDEX IF NOT EXISTS idx_users_updated_at ON public.users(updated_at);
CREATE INDEX IF NOT EXISTS idx_prospects_updated_at ON public.prospects(updated_at);
CREATE INDEX IF NOT EXISTS idx_training_modules_updated_at ON public.training_modules(updated_at);

-- =============================================================================
-- PARTIAL INDEXES FOR COMMON FILTERED QUERIES
-- =============================================================================

-- Partial index for active users only
CREATE INDEX IF NOT EXISTS idx_users_active ON public.users(id, email, role) 
WHERE status = 'active';

-- Partial index for published training modules only
CREATE INDEX IF NOT EXISTS idx_training_modules_published ON public.training_modules(id, title, required_order) 
WHERE status = 'published';

-- Partial index for published job postings only
CREATE INDEX IF NOT EXISTS idx_job_postings_published ON public.job_postings(id, bpo_id, title, created_at) 
WHERE status = 'published';

-- Partial index for completed progress records only
CREATE INDEX IF NOT EXISTS idx_progress_records_completed ON public.progress_records(prospect_id, activity_id, completed_at) 
WHERE status = 'completed';

-- =============================================================================
-- VERIFICATION
-- =============================================================================

-- Query to verify indexes were created
SELECT 
    schemaname,
    tablename,
    indexname,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
    AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;
