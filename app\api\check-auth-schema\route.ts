import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

export async function GET(request: NextRequest) {
  try {
    console.log('🔍 CHECKING AUTH SCHEMA...');
    
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
    const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;
    
    const supabase = createClient(supabaseUrl, serviceKey);

    // Check if auth.users table exists and has data
    console.log('🔍 Checking auth.users table...');
    const { data: authUsers, error: authUsersError } = await supabase
      .from('auth.users')
      .select('id, email')
      .limit(5);
    
    console.log('🔍 Auth users result:', {
      success: !authUsersError,
      error: authUsersError?.message,
      count: authUsers?.length || 0
    });

    // Check our users table
    console.log('🔍 Checking public.users table...');
    const { data: publicUsers, error: publicUsersError } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(5);
    
    console.log('🔍 Public users result:', {
      success: !publicUsersError,
      error: publicUsersError?.message,
      count: publicUsers?.length || 0,
      users: publicUsers
    });

    // Check if there's a trigger connecting auth.users to public.users
    console.log('🔍 Checking database functions and triggers...');
    const { data: functions, error: functionsError } = await supabase.rpc('pg_get_functiondef', { funcid: 'handle_new_user' });
    
    console.log('🔍 Functions check:', {
      success: !functionsError,
      error: functionsError?.message
    });

    return NextResponse.json({
      message: 'Auth schema check completed',
      results: {
        authUsers: {
          accessible: !authUsersError,
          count: authUsers?.length || 0,
          error: authUsersError?.message
        },
        publicUsers: {
          accessible: !publicUsersError,
          count: publicUsers?.length || 0,
          error: publicUsersError?.message,
          sample: publicUsers?.slice(0, 2)
        },
        functions: {
          accessible: !functionsError,
          error: functionsError?.message
        }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('🔍 AUTH SCHEMA CHECK FAILED:', error);
    return NextResponse.json({
      error: 'Auth schema check failed',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
