-- ============================================================================
-- CREATE AUTH.USERS RECORDS FOR LUNA TEST ACCOUNTS
-- All accounts use password: BeeMO5317
-- ============================================================================

-- Platform Admin
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role,
  aud
) VALUES (
  '********-1111-1111-1111-********1111',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Luna Platform Admin"}',
  false,
  'authenticated',
  'authenticated'
);

-- Individual Users
INSERT INTO auth.users (
  id, instance_id, email, encrypted_password, email_confirmed_at, created_at, updated_at,
  raw_app_meta_data, raw_user_meta_data, is_super_admin, role, aud
) VALUES
(
  '********-0000-0000-0000-********0002',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Sarah Johnson"}',
  false,
  'authenticated',
  'authenticated'
),
(
  '********-0000-0000-0000-********0003',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Mike Chen"}',
  false,
  'authenticated',
  'authenticated'
),
(
  '********-0000-0000-0000-********0004',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Alex Rodriguez"}',
  false,
  'authenticated',
  'authenticated'
);

-- Organization Owners
INSERT INTO auth.users (
  id, instance_id, email, encrypted_password, email_confirmed_at, created_at, updated_at,
  raw_app_meta_data, raw_user_meta_data, is_super_admin, role, aud
) VALUES
(
  '********-0000-0000-0000-********0005',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Jennifer Smith"}',
  false,
  'authenticated',
  'authenticated'
),
(
  '********-0000-0000-0000-********0006',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "David Kim"}',
  false,
  'authenticated',
  'authenticated'
);

-- Organization Admins
INSERT INTO auth.users (
  id, instance_id, email, encrypted_password, email_confirmed_at, created_at, updated_at,
  raw_app_meta_data, raw_user_meta_data, is_super_admin, role, aud
) VALUES
(
  '********-0000-0000-0000-********0007',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Lisa Wang"}',
  false,
  'authenticated',
  'authenticated'
),
(
  '********-0000-0000-0000-********0008',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Robert Johnson"}',
  false,
  'authenticated',
  'authenticated'
);

-- Organization Members
INSERT INTO auth.users (
  id, instance_id, email, encrypted_password, email_confirmed_at, created_at, updated_at,
  raw_app_meta_data, raw_user_meta_data, is_super_admin, role, aud
) VALUES
(
  '********-0000-0000-0000-********0009',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "John Davis"}',
  false,
  'authenticated',
  'authenticated'
),
(
  '********-0000-0000-0000-********0010',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Emma Wilson"}',
  false,
  'authenticated',
  'authenticated'
),
(
  '********-0000-0000-0000-********0011',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Carlos Martinez"}',
  false,
  'authenticated',
  'authenticated'
);

-- Hybrid User (Individual + Organization Member)
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role,
  aud
) VALUES (
  '********-0000-0000-0000-********0012',
  '********-0000-0000-0000-************',
  '<EMAIL>',
  crypt('BeeMO5317', gen_salt('bf')),
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"full_name": "Maria Garcia"}',
  false,
  'authenticated',
  'authenticated'
);