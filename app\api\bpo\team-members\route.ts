import { NextRequest, NextResponse } from 'next/server';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    
    // Get the current user
    const { data: { session }, error: authError } = await supabase.auth.getSession();
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      );
    }

    // Get current user's BPO ID
    const { data: currentBpoUser, error: bpoError } = await supabase
      .from('bpo_teams')
      .select('bpo_id')
      .eq('user_id', session.user.id)
      .single();

    if (bpoError || !currentBpoUser) {
      return NextResponse.json(
        { error: 'BPO team membership not found' },
        { status: 404 }
      );
    }

    // Fetch all team members from the same BPO (excluding current user)
    const { data: teamMembers, error: teamError } = await supabase
      .from('bpo_teams')
      .select(`
        user_id,
        role,
        users!inner(
          id,
          email,
          full_name
        )
      `)
      .eq('bpo_id', currentBpoUser.bpo_id)
      .neq('user_id', session.user.id);

    if (teamError) {
      console.error('Error fetching team members:', teamError);
      return NextResponse.json(
        { error: 'Failed to fetch team members' },
        { status: 500 }
      );
    }

    // Format the response
    const formattedTeamMembers = (teamMembers || []).map(member => ({
      id: member.user_id,
      email: member.users.email,
      name: member.users.full_name || member.users.email,
      role: member.role
    }));

    return NextResponse.json({
      success: true,
      teamMembers: formattedTeamMembers
    });

  } catch (error) {
    console.error('Error in team members API:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
