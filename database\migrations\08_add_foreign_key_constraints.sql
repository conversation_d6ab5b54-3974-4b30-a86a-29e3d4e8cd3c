-- Foreign Key Constraints Migration
-- Adds proper foreign key relationships for data integrity

-- =============================================================================
-- PROSPECTS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for prospects.user_id -> users.id
ALTER TABLE public.prospects 
ADD CONSTRAINT IF NOT EXISTS fk_prospects_user_id 
FOREIGN KEY (user_id) REFERENCES public.users(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================================================
-- BPO_TEAMS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for bpo_teams.bpo_id -> bpos.id
ALTER TABLE public.bpo_teams 
ADD CONSTRAINT IF NOT EXISTS fk_bpo_teams_bpo_id 
FOREIGN KEY (bpo_id) REFERENCES public.bpos(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add foreign key constraint for bpo_teams.user_id -> users.id
ALTER TABLE public.bpo_teams 
ADD CONSTRAINT IF NOT EXISTS fk_bpo_teams_user_id 
FOREIGN KEY (user_id) REFERENCES public.users(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add foreign key constraint for bpo_teams.invited_by -> users.id
ALTER TABLE public.bpo_teams 
ADD CONSTRAINT IF NOT EXISTS fk_bpo_teams_invited_by 
FOREIGN KEY (invited_by) REFERENCES public.users(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- =============================================================================
-- TRAINING_MODULES TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for training_modules.created_by -> users.id
ALTER TABLE public.training_modules 
ADD CONSTRAINT IF NOT EXISTS fk_training_modules_created_by 
FOREIGN KEY (created_by) REFERENCES public.users(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- =============================================================================
-- LESSONS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for lessons.module_id -> training_modules.id
ALTER TABLE public.lessons 
ADD CONSTRAINT IF NOT EXISTS fk_lessons_module_id 
FOREIGN KEY (module_id) REFERENCES public.training_modules(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================================================
-- ACTIVITIES TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for activities.lesson_id -> lessons.id
ALTER TABLE public.activities 
ADD CONSTRAINT IF NOT EXISTS fk_activities_lesson_id 
FOREIGN KEY (lesson_id) REFERENCES public.lessons(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================================================
-- PROGRESS_RECORDS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for progress_records.prospect_id -> prospects.id
ALTER TABLE public.progress_records 
ADD CONSTRAINT IF NOT EXISTS fk_progress_records_prospect_id 
FOREIGN KEY (prospect_id) REFERENCES public.prospects(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add foreign key constraint for progress_records.activity_id -> activities.id
ALTER TABLE public.progress_records 
ADD CONSTRAINT IF NOT EXISTS fk_progress_records_activity_id 
FOREIGN KEY (activity_id) REFERENCES public.activities(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================================================
-- JOB_POSTINGS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for job_postings.bpo_id -> bpos.id
ALTER TABLE public.job_postings 
ADD CONSTRAINT IF NOT EXISTS fk_job_postings_bpo_id 
FOREIGN KEY (bpo_id) REFERENCES public.bpos(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================================================
-- ASSESSMENT_QUESTIONS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for assessment_questions.assessment_id -> assessments.id
ALTER TABLE public.assessment_questions 
ADD CONSTRAINT IF NOT EXISTS fk_assessment_questions_assessment_id 
FOREIGN KEY (assessment_id) REFERENCES public.assessments(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- =============================================================================
-- MODULE_ASSESSMENTS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for module_assessments.module_id -> training_modules.id
ALTER TABLE public.module_assessments 
ADD CONSTRAINT IF NOT EXISTS fk_module_assessments_module_id 
FOREIGN KEY (module_id) REFERENCES public.training_modules(id) 
ON DELETE CASCADE ON UPDATE CASCADE;

-- Add foreign key constraint for module_assessments.assessment_id -> assessments.id
-- Note: This can be NULL, so we only add constraint if assessment_id is not null
ALTER TABLE public.module_assessments 
ADD CONSTRAINT IF NOT EXISTS fk_module_assessments_assessment_id 
FOREIGN KEY (assessment_id) REFERENCES public.assessments(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- =============================================================================
-- APPLICATIONS TABLE FOREIGN KEYS (if table has columns)
-- =============================================================================

-- Note: Applications table appears to be empty in schema, but we'll add constraints
-- for when it gets properly defined

-- First, let's check if applications table has the expected columns
-- If not, we'll create them in a separate migration

-- =============================================================================
-- INTERVIEWS TABLE FOREIGN KEYS (if table has columns)
-- =============================================================================

-- Note: Interviews table appears to be empty in schema, but we'll add constraints
-- for when it gets properly defined

-- =============================================================================
-- BPOS TABLE FOREIGN KEYS
-- =============================================================================

-- Add foreign key constraint for bpos.created_by -> users.id
ALTER TABLE public.bpos 
ADD CONSTRAINT IF NOT EXISTS fk_bpos_created_by 
FOREIGN KEY (created_by) REFERENCES public.users(id) 
ON DELETE SET NULL ON UPDATE CASCADE;

-- =============================================================================
-- UNIQUE CONSTRAINTS FOR DATA INTEGRITY
-- =============================================================================

-- Ensure unique user-BPO team membership
ALTER TABLE public.bpo_teams 
ADD CONSTRAINT IF NOT EXISTS uk_bpo_teams_user_bpo 
UNIQUE (user_id, bpo_id);

-- Ensure unique prospect per user
ALTER TABLE public.prospects 
ADD CONSTRAINT IF NOT EXISTS uk_prospects_user_id 
UNIQUE (user_id);

-- Ensure unique lesson order within module
ALTER TABLE public.lessons 
ADD CONSTRAINT IF NOT EXISTS uk_lessons_module_order 
UNIQUE (module_id, order_index);

-- Ensure unique activity order within lesson
ALTER TABLE public.activities 
ADD CONSTRAINT IF NOT EXISTS uk_activities_lesson_order 
UNIQUE (lesson_id, order_index);

-- Ensure unique progress record per prospect-activity
ALTER TABLE public.progress_records 
ADD CONSTRAINT IF NOT EXISTS uk_progress_records_prospect_activity 
UNIQUE (prospect_id, activity_id);

-- Ensure unique module assessment order
ALTER TABLE public.module_assessments 
ADD CONSTRAINT IF NOT EXISTS uk_module_assessments_module_order 
UNIQUE (module_id, order_index);

-- =============================================================================
-- CHECK CONSTRAINTS FOR DATA VALIDATION
-- =============================================================================

-- Ensure positive duration for training modules
ALTER TABLE public.training_modules 
ADD CONSTRAINT IF NOT EXISTS chk_training_modules_duration_positive 
CHECK (duration_minutes > 0);

-- Ensure positive duration for lessons
ALTER TABLE public.lessons 
ADD CONSTRAINT IF NOT EXISTS chk_lessons_duration_positive 
CHECK (duration_minutes > 0);

-- Ensure positive order index for lessons
ALTER TABLE public.lessons 
ADD CONSTRAINT IF NOT EXISTS chk_lessons_order_positive 
CHECK (order_index > 0);

-- Ensure positive order index for activities
ALTER TABLE public.activities 
ADD CONSTRAINT IF NOT EXISTS chk_activities_order_positive 
CHECK (order_index > 0);

-- Ensure valid score range for progress records
ALTER TABLE public.progress_records 
ADD CONSTRAINT IF NOT EXISTS chk_progress_records_score_range 
CHECK (score >= 0 AND score <= 100);

-- Ensure non-negative attempts for progress records
ALTER TABLE public.progress_records 
ADD CONSTRAINT IF NOT EXISTS chk_progress_records_attempts_positive 
CHECK (attempts >= 0);

-- Ensure non-negative time spent for progress records
ALTER TABLE public.progress_records 
ADD CONSTRAINT IF NOT EXISTS chk_progress_records_time_positive 
CHECK (time_spent_seconds >= 0);

-- Ensure valid passing score range for assessments
ALTER TABLE public.assessments 
ADD CONSTRAINT IF NOT EXISTS chk_assessments_passing_score_range 
CHECK (passing_score >= 0 AND passing_score <= 100);

-- Ensure positive total questions for assessments
ALTER TABLE public.assessments 
ADD CONSTRAINT IF NOT EXISTS chk_assessments_total_questions_positive 
CHECK (total_questions > 0);

-- =============================================================================
-- VERIFICATION QUERIES
-- =============================================================================

-- Query to verify foreign key constraints were created
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    kcu.column_name,
    ccu.table_name AS foreign_table_name,
    ccu.column_name AS foreign_column_name
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
JOIN information_schema.constraint_column_usage AS ccu
    ON ccu.constraint_name = tc.constraint_name
    AND ccu.table_schema = tc.table_schema
WHERE tc.constraint_type = 'FOREIGN KEY'
    AND tc.table_schema = 'public'
    AND tc.constraint_name LIKE 'fk_%'
ORDER BY tc.table_name, tc.constraint_name;

-- Query to verify unique constraints were created
SELECT 
    tc.table_name,
    tc.constraint_name,
    tc.constraint_type,
    string_agg(kcu.column_name, ', ' ORDER BY kcu.ordinal_position) AS columns
FROM information_schema.table_constraints AS tc
JOIN information_schema.key_column_usage AS kcu
    ON tc.constraint_name = kcu.constraint_name
    AND tc.table_schema = kcu.table_schema
WHERE tc.constraint_type = 'UNIQUE'
    AND tc.table_schema = 'public'
    AND tc.constraint_name LIKE 'uk_%'
GROUP BY tc.table_name, tc.constraint_name, tc.constraint_type
ORDER BY tc.table_name, tc.constraint_name;
