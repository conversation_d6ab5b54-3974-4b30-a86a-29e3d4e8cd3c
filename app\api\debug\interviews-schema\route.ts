import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
import { NextRequest, NextResponse } from 'next/server'

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies })

    // Try to get a sample record to see what columns exist
    const { data: sample, error: sampleError } = await supabase
      .from('interviews')
      .select('*')
      .limit(1)

    // Try a simple insert to see what the error is
    const testInsert = {
      application_id: '00000000-0000-0000-0000-000000000000',
      bpo_user_id: '00000000-0000-0000-0000-000000000000',
      scheduled_at: new Date().toISOString(),
      duration_minutes: 60,
      location: 'Test',
      status: 'scheduled',
      notes: 'Test'
    }

    const { data: insertTest, error: insertError } = await supabase
      .from('interviews')
      .insert(testInsert)
      .select()

    return NextResponse.json({
      success: true,
      sample: sample,
      sampleError: sampleError,
      insertTest: insertTest,
      insertError: insertError,
      testData: testInsert
    })

  } catch (error) {
    console.error('Error in schema debug:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
