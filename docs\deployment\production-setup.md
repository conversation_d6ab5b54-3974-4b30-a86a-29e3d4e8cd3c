# 🚀 Production Setup Guide

Comprehensive guide for deploying the BPO Training Platform to production environments, covering infrastructure, security, monitoring, and best practices.

## 🎯 Production Architecture

### Recommended Infrastructure

```
Production Environment:
├── Frontend (Vercel/Netlify)
│   ├── Next.js Application
│   ├── Global CDN
│   ├── Edge Functions
│   └── SSL Termination
├── Backend (Supabase)
│   ├── PostgreSQL Database
│   ├── Authentication Service
│   ├── Storage Service
│   ├── Edge Functions
│   └── Real-time Subscriptions
├── Monitoring & Analytics
│   ├── Application Monitoring (Sentry)
│   ├── Performance Monitoring (Vercel Analytics)
│   ├── Uptime Monitoring (Pingdom)
│   └── Log Aggregation (LogRocket)
└── Security
    ├── WAF (Cloudflare)
    ├── DDoS Protection
    ├── SSL Certificates
    └── Security Scanning
```

## 🔧 Deployment Platforms

### Vercel Deployment (Recommended)

#### 1. Vercel Configuration

```json
// vercel.json
{
  "framework": "nextjs",
  "buildCommand": "npm run build",
  "devCommand": "npm run dev",
  "installCommand": "npm install",
  "outputDirectory": ".next",
  "functions": {
    "app/api/**/*.ts": {
      "maxDuration": 30
    }
  },
  "headers": [
    {
      "source": "/(.*)",
      "headers": [
        {
          "key": "X-Frame-Options",
          "value": "DENY"
        },
        {
          "key": "X-Content-Type-Options",
          "value": "nosniff"
        },
        {
          "key": "Referrer-Policy",
          "value": "origin-when-cross-origin"
        },
        {
          "key": "Permissions-Policy",
          "value": "camera=(), microphone=(), geolocation=()"
        }
      ]
    },
    {
      "source": "/api/(.*)",
      "headers": [
        {
          "key": "Cache-Control",
          "value": "no-store, max-age=0"
        }
      ]
    }
  ],
  "redirects": [
    {
      "source": "/admin",
      "destination": "/admin/dashboard",
      "permanent": true
    }
  ],
  "rewrites": [
    {
      "source": "/health",
      "destination": "/api/health"
    }
  ]
}
```

#### 2. Environment Variables Setup

```bash
# Production environment variables for Vercel
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
vercel env add SUPABASE_SERVICE_ROLE_KEY production
vercel env add NEXT_PUBLIC_APP_URL production
vercel env add ENCRYPTION_KEY production
vercel env add FIELD_ENCRYPTION_KEY production
vercel env add NEXT_PUBLIC_ANALYTICS_ID production
vercel env add SENTRY_DSN production
```

#### 3. Deployment Script

```bash
#!/bin/bash
# deploy.sh

set -e

echo "🚀 Starting production deployment..."

# Build and test
echo "📦 Building application..."
npm run build

echo "🧪 Running tests..."
npm run test:ci

echo "🔍 Running security audit..."
npm audit --audit-level high

echo "📊 Analyzing bundle..."
npm run analyze

echo "🚀 Deploying to Vercel..."
vercel --prod

echo "✅ Deployment complete!"
echo "🔗 Production URL: https://your-domain.com"
```

### Alternative: Netlify Deployment

```toml
# netlify.toml
[build]
  command = "npm run build"
  publish = ".next"

[build.environment]
  NODE_VERSION = "18"

[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "origin-when-cross-origin"

[[redirects]]
  from = "/admin"
  to = "/admin/dashboard"
  status = 301

[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/:splat"
  status = 200
```

## 🗄️ Database Production Setup

### Supabase Production Configuration

#### 1. Database Optimization

```sql
-- Production database optimizations
-- Connection pooling
ALTER SYSTEM SET max_connections = 200;
ALTER SYSTEM SET shared_buffers = '256MB';
ALTER SYSTEM SET effective_cache_size = '1GB';
ALTER SYSTEM SET work_mem = '4MB';
ALTER SYSTEM SET maintenance_work_mem = '64MB';

-- Query optimization
ALTER SYSTEM SET random_page_cost = 1.1;
ALTER SYSTEM SET effective_io_concurrency = 200;

-- Logging for monitoring
ALTER SYSTEM SET log_min_duration_statement = 1000; -- Log slow queries
ALTER SYSTEM SET log_checkpoints = on;
ALTER SYSTEM SET log_connections = on;
ALTER SYSTEM SET log_disconnections = on;

-- Apply changes
SELECT pg_reload_conf();
```

#### 2. Backup Strategy

```sql
-- Automated backup configuration
-- Point-in-time recovery enabled by default in Supabase

-- Manual backup script
CREATE OR REPLACE FUNCTION create_manual_backup()
RETURNS void AS $$
BEGIN
  -- Create backup of critical tables
  COPY (SELECT * FROM users) TO '/tmp/users_backup.csv' CSV HEADER;
  COPY (SELECT * FROM training_modules) TO '/tmp/training_backup.csv' CSV HEADER;
  COPY (SELECT * FROM job_postings) TO '/tmp/jobs_backup.csv' CSV HEADER;
  
  -- Log backup creation
  INSERT INTO system_logs (event_type, message, created_at)
  VALUES ('backup_created', 'Manual backup completed', NOW());
END;
$$ LANGUAGE plpgsql;

-- Schedule daily backups (configure in Supabase dashboard)
```

#### 3. Monitoring Queries

```sql
-- Performance monitoring views
CREATE VIEW slow_queries AS
SELECT 
  query,
  calls,
  total_time,
  mean_time,
  rows
FROM pg_stat_statements
WHERE mean_time > 100
ORDER BY mean_time DESC;

-- Connection monitoring
CREATE VIEW connection_stats AS
SELECT 
  state,
  COUNT(*) as connection_count,
  MAX(now() - state_change) as max_duration
FROM pg_stat_activity
WHERE state IS NOT NULL
GROUP BY state;

-- Table size monitoring
CREATE VIEW table_sizes AS
SELECT 
  schemaname,
  tablename,
  pg_size_pretty(pg_total_relation_size(schemaname||'.'||tablename)) as size,
  pg_total_relation_size(schemaname||'.'||tablename) as size_bytes
FROM pg_tables
WHERE schemaname = 'public'
ORDER BY size_bytes DESC;
```

## 🔒 Security Hardening

### SSL/TLS Configuration

```javascript
// next.config.mjs - Security headers
const securityHeaders = [
  {
    key: 'Strict-Transport-Security',
    value: 'max-age=63072000; includeSubDomains; preload'
  },
  {
    key: 'X-Frame-Options',
    value: 'DENY'
  },
  {
    key: 'X-Content-Type-Options',
    value: 'nosniff'
  },
  {
    key: 'X-XSS-Protection',
    value: '1; mode=block'
  },
  {
    key: 'Referrer-Policy',
    value: 'origin-when-cross-origin'
  },
  {
    key: 'Content-Security-Policy',
    value: [
      "default-src 'self'",
      "script-src 'self' 'unsafe-eval' 'unsafe-inline' *.vercel.app",
      "style-src 'self' 'unsafe-inline'",
      "img-src 'self' data: https:",
      "font-src 'self'",
      "connect-src 'self' *.supabase.co wss://*.supabase.co",
      "frame-ancestors 'none'"
    ].join('; ')
  }
]

const nextConfig = {
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: securityHeaders
      }
    ]
  }
}
```

### Environment Security

```bash
# Production environment security checklist

# 1. Secure environment variables
export NODE_ENV=production
export NEXT_PUBLIC_APP_URL=https://your-domain.com

# 2. Generate secure keys
export ENCRYPTION_KEY=$(openssl rand -hex 32)
export FIELD_ENCRYPTION_KEY=$(openssl rand -hex 32)
export JWT_SECRET=$(openssl rand -hex 64)

# 3. Database security
export SUPABASE_SERVICE_ROLE_KEY="your-secure-service-role-key"
export DATABASE_URL="postgresql://user:password@host:port/database?sslmode=require"

# 4. Third-party service keys
export SENTRY_DSN="your-sentry-dsn"
export ANALYTICS_ID="your-analytics-id"
```

## 📊 Monitoring & Observability

### Application Monitoring

```typescript
// lib/monitoring/sentry.ts
import * as Sentry from '@sentry/nextjs'

Sentry.init({
  dsn: process.env.SENTRY_DSN,
  environment: process.env.NODE_ENV,
  tracesSampleRate: process.env.NODE_ENV === 'production' ? 0.1 : 1.0,
  beforeSend(event) {
    // Filter out sensitive data
    if (event.request?.data) {
      delete event.request.data.password
      delete event.request.data.token
    }
    return event
  },
  integrations: [
    new Sentry.Integrations.Http({ tracing: true }),
    new Sentry.Integrations.Express({ app: undefined })
  ]
})

// Custom error tracking
export const trackError = (error: Error, context?: Record<string, any>) => {
  Sentry.withScope(scope => {
    if (context) {
      scope.setContext('additional_info', context)
    }
    Sentry.captureException(error)
  })
}

// Performance monitoring
export const trackPerformance = (name: string, duration: number) => {
  Sentry.addBreadcrumb({
    message: `Performance: ${name}`,
    level: 'info',
    data: { duration }
  })
}
```

### Health Checks

```typescript
// app/api/health/route.ts
import { NextResponse } from 'next/server'
import { supabase } from '@/lib/supabase'

export async function GET() {
  const checks = {
    timestamp: new Date().toISOString(),
    status: 'healthy',
    version: process.env.npm_package_version || '1.0.0',
    environment: process.env.NODE_ENV,
    checks: {
      database: await checkDatabase(),
      auth: await checkAuth(),
      storage: await checkStorage(),
      cache: await checkCache()
    }
  }

  const allHealthy = Object.values(checks.checks).every(check => check.status === 'healthy')
  
  return NextResponse.json(checks, {
    status: allHealthy ? 200 : 503,
    headers: {
      'Cache-Control': 'no-cache, no-store, must-revalidate'
    }
  })
}

async function checkDatabase() {
  try {
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)

    return {
      status: error ? 'unhealthy' : 'healthy',
      responseTime: Date.now(),
      error: error?.message
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    }
  }
}

async function checkAuth() {
  try {
    const { data, error } = await supabase.auth.getSession()
    return {
      status: 'healthy',
      responseTime: Date.now()
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    }
  }
}

async function checkStorage() {
  try {
    const { data, error } = await supabase.storage.listBuckets()
    return {
      status: error ? 'unhealthy' : 'healthy',
      responseTime: Date.now(),
      error: error?.message
    }
  } catch (error) {
    return {
      status: 'unhealthy',
      error: error.message
    }
  }
}

async function checkCache() {
  // Check cache system health
  return {
    status: 'healthy',
    responseTime: Date.now()
  }
}
```

### Uptime Monitoring

```javascript
// scripts/uptime-monitor.js
const https = require('https')

const endpoints = [
  'https://your-domain.com/health',
  'https://your-domain.com/api/health',
  'https://your-domain.com/login'
]

async function checkEndpoint(url) {
  return new Promise((resolve) => {
    const start = Date.now()
    
    https.get(url, (res) => {
      const duration = Date.now() - start
      resolve({
        url,
        status: res.statusCode,
        duration,
        healthy: res.statusCode >= 200 && res.statusCode < 400
      })
    }).on('error', (error) => {
      resolve({
        url,
        status: 0,
        duration: Date.now() - start,
        healthy: false,
        error: error.message
      })
    })
  })
}

async function runHealthChecks() {
  console.log('🔍 Running health checks...')
  
  const results = await Promise.all(
    endpoints.map(endpoint => checkEndpoint(endpoint))
  )
  
  results.forEach(result => {
    const status = result.healthy ? '✅' : '❌'
    console.log(`${status} ${result.url} - ${result.status} (${result.duration}ms)`)
    
    if (!result.healthy) {
      // Send alert (implement your alerting logic)
      console.error(`🚨 Alert: ${result.url} is down!`)
    }
  })
  
  return results
}

// Run checks every 5 minutes
setInterval(runHealthChecks, 5 * 60 * 1000)
runHealthChecks() // Initial check
```

## 🔄 CI/CD Pipeline

### GitHub Actions Workflow

```yaml
# .github/workflows/production.yml
name: Production Deployment

on:
  push:
    branches: [main]

jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run lint
      - run: npm run type-check
      - run: npm run test:unit
      - run: npm run test:integration
      
      - name: Security audit
        run: npm audit --audit-level high

  build:
    needs: test
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: '18'
          cache: 'npm'
      
      - run: npm ci
      - run: npm run build
      
      - name: Upload build artifacts
        uses: actions/upload-artifact@v3
        with:
          name: build-files
          path: .next/

  deploy:
    needs: [test, build]
    runs-on: ubuntu-latest
    environment: production
    steps:
      - uses: actions/checkout@v3
      
      - name: Deploy to Vercel
        uses: amondnet/vercel-action@v25
        with:
          vercel-token: ${{ secrets.VERCEL_TOKEN }}
          vercel-org-id: ${{ secrets.VERCEL_ORG_ID }}
          vercel-project-id: ${{ secrets.VERCEL_PROJECT_ID }}
          vercel-args: '--prod'
      
      - name: Run smoke tests
        run: |
          sleep 30 # Wait for deployment
          curl -f https://your-domain.com/health || exit 1
      
      - name: Notify deployment
        uses: 8398a7/action-slack@v3
        with:
          status: ${{ job.status }}
          channel: '#deployments'
          webhook_url: ${{ secrets.SLACK_WEBHOOK }}
```

## 📈 Performance Optimization

### Production Optimizations

```javascript
// next.config.mjs - Production optimizations
const nextConfig = {
  // Compression
  compress: true,
  
  // Image optimization
  images: {
    domains: ['supabase.co', 'your-cdn.com'],
    formats: ['image/avif', 'image/webp'],
    minimumCacheTTL: 60 * 60 * 24 * 30, // 30 days
  },
  
  // Bundle optimization
  webpack: (config, { dev, isServer }) => {
    if (!dev && !isServer) {
      // Bundle analyzer in production builds
      config.plugins.push(
        new (require('webpack-bundle-analyzer').BundleAnalyzerPlugin)({
          analyzerMode: 'static',
          openAnalyzer: false,
          reportFilename: 'bundle-report.html'
        })
      )
    }
    
    return config
  },
  
  // Experimental optimizations
  experimental: {
    optimizeCss: true,
    scrollRestoration: true,
  },
  
  // Output optimization
  output: 'standalone',
  
  // Power by header
  poweredByHeader: false
}
```

### CDN Configuration

```javascript
// lib/cdn.ts
export const cdnConfig = {
  // Static assets
  staticAssets: {
    domain: 'cdn.your-domain.com',
    cacheTTL: 31536000, // 1 year
    gzipCompression: true,
    brotliCompression: true
  },
  
  // API responses
  apiCache: {
    publicEndpoints: ['/api/jobs/postings', '/api/training/modules'],
    cacheTTL: 300, // 5 minutes
    staleWhileRevalidate: 600 // 10 minutes
  },
  
  // Image optimization
  images: {
    formats: ['avif', 'webp', 'jpg'],
    sizes: [320, 640, 768, 1024, 1280, 1920],
    quality: 75
  }
}
```

---

**Next**: Configure [Environment Variables](environment-variables.md) for different deployment environments.
