import { NextRequest, NextResponse } from 'next/server';
import { createAdminClient } from '@/lib/supabase';

export async function GET(request: NextRequest) {
  try {
    console.log('[DB-TEST] 🚀 Starting database connection test...');
    
    const supabase = createAdminClient();
    console.log('[DB-TEST] ✅ Admin client created');

    // Test 1: Basic connection
    console.log('[DB-TEST] 🔍 Testing basic connection...');
    const { data: connectionTest, error: connectionError } = await supabase
      .from('users')
      .select('count')
      .limit(1);
    
    console.log('[DB-TEST] 📊 Connection test result:', {
      success: !connectionError,
      error: connectionError?.message,
      data: connectionTest
    });

    // Test 2: Check if users table exists and has data
    console.log('[DB-TEST] 🔍 Testing users table...');
    const { data: usersTest, error: usersError } = await supabase
      .from('users')
      .select('id, email, role')
      .limit(5);
    
    console.log('[DB-TEST] 📊 Users table test result:', {
      success: !usersError,
      error: usersError?.message,
      userCount: usersTest?.length || 0,
      users: usersTest
    });

    // Test 3: Check organizations table
    console.log('[DB-TEST] 🔍 Testing organizations table...');
    const { data: orgsTest, error: orgsError } = await supabase
      .from('organizations')
      .select('id, name, slug')
      .limit(5);
    
    console.log('[DB-TEST] 📊 Organizations table test result:', {
      success: !orgsError,
      error: orgsError?.message,
      orgCount: orgsTest?.length || 0,
      orgs: orgsTest
    });

    // Test 4: Check organization_memberships table
    console.log('[DB-TEST] 🔍 Testing organization_memberships table...');
    const { data: membershipsTest, error: membershipsError } = await supabase
      .from('organization_memberships')
      .select('id, user_id, organization_id, role')
      .limit(5);
    
    console.log('[DB-TEST] 📊 Memberships table test result:', {
      success: !membershipsError,
      error: membershipsError?.message,
      membershipCount: membershipsTest?.length || 0,
      memberships: membershipsTest
    });

    // Test 5: Check user_contexts table
    console.log('[DB-TEST] 🔍 Testing user_contexts table...');
    const { data: contextsTest, error: contextsError } = await supabase
      .from('user_contexts')
      .select('id, user_id, active_context')
      .limit(5);
    
    console.log('[DB-TEST] 📊 User contexts table test result:', {
      success: !contextsError,
      error: contextsError?.message,
      contextCount: contextsTest?.length || 0,
      contexts: contextsTest
    });

    // Test 6: Check individuals table
    console.log('[DB-TEST] 🔍 Testing individuals table...');
    const { data: individualsTest, error: individualsError } = await supabase
      .from('individuals')
      .select('id, user_id')
      .limit(5);
    
    console.log('[DB-TEST] 📊 Individuals table test result:', {
      success: !individualsError,
      error: individualsError?.message,
      individualCount: individualsTest?.length || 0,
      individuals: individualsTest
    });

    return NextResponse.json({
      success: true,
      message: 'Database test completed',
      results: {
        connection: { success: !connectionError, error: connectionError?.message },
        users: { success: !usersError, count: usersTest?.length || 0, error: usersError?.message },
        organizations: { success: !orgsError, count: orgsTest?.length || 0, error: orgsError?.message },
        memberships: { success: !membershipsError, count: membershipsTest?.length || 0, error: membershipsError?.message },
        contexts: { success: !contextsError, count: contextsTest?.length || 0, error: contextsError?.message },
        individuals: { success: !individualsError, count: individualsTest?.length || 0, error: individualsError?.message }
      },
      timestamp: new Date().toISOString()
    });

  } catch (error) {
    console.error('[DB-TEST] ❌ Unexpected error during database test:', error);
    return NextResponse.json({
      success: false,
      error: 'Unexpected error during database test',
      details: error instanceof Error ? error.message : 'Unknown error',
      timestamp: new Date().toISOString()
    }, { status: 500 });
  }
}
