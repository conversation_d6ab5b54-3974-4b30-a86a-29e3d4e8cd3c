# 🔧 Environment Variables Reference

Complete reference for all environment variables used in the BPO Training Platform across different environments.

## 📋 Environment Variable Categories

### 🔐 Required Variables

These variables are **required** for the application to function:

```env
# =============================================================================
# SUPABASE CONFIGURATION (Required)
# =============================================================================

# Your Supabase project URL
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co

# Supabase anonymous key (safe to expose in browser)
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# Supabase service role key (server-side only, keep secret)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...

# =============================================================================
# APPLICATION CONFIGURATION (Required)
# =============================================================================

# Application URL (used for redirects and API calls)
NEXT_PUBLIC_APP_URL=http://localhost:3000

# Environment (development, staging, production)
NODE_ENV=development
```

### 🔒 Security Variables

```env
# =============================================================================
# ENCRYPTION & SECURITY (Required for Production)
# =============================================================================

# Master encryption key for sensitive data (32 bytes hex)
ENCRYPTION_KEY=your-32-byte-hex-encryption-key

# Field-specific encryption key (32 bytes hex)
FIELD_ENCRYPTION_KEY=your-32-byte-field-encryption-key

# JWT secret for custom tokens (64 bytes hex)
JWT_SECRET=your-64-byte-jwt-secret

# Session secret for server-side sessions
SESSION_SECRET=your-session-secret

# CSRF token secret
CSRF_SECRET=your-csrf-secret
```

### 📧 Email Configuration

```env
# =============================================================================
# EMAIL SERVICE (Optional)
# =============================================================================

# SMTP configuration for email notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_SECURE=false
SMTP_USER=<EMAIL>
SMTP_PASS=your-app-password

# Email service provider (smtp, sendgrid, mailgun)
EMAIL_PROVIDER=smtp

# SendGrid configuration (if using SendGrid)
SENDGRID_API_KEY=your-sendgrid-api-key
SENDGRID_FROM_EMAIL=<EMAIL>

# Mailgun configuration (if using Mailgun)
MAILGUN_API_KEY=your-mailgun-api-key
MAILGUN_DOMAIN=your-mailgun-domain
```

### 📊 Analytics & Monitoring

```env
# =============================================================================
# ANALYTICS & MONITORING (Optional)
# =============================================================================

# Google Analytics
NEXT_PUBLIC_ANALYTICS_ID=G-XXXXXXXXXX

# Sentry error tracking
SENTRY_DSN=https://<EMAIL>/project-id
NEXT_PUBLIC_SENTRY_DSN=https://<EMAIL>/project-id

# LogRocket session recording
NEXT_PUBLIC_LOGROCKET_ID=your-logrocket-id

# Hotjar analytics
NEXT_PUBLIC_HOTJAR_ID=your-hotjar-id

# Mixpanel analytics
NEXT_PUBLIC_MIXPANEL_TOKEN=your-mixpanel-token
```

### 🗄️ Database & Cache

```env
# =============================================================================
# DATABASE & CACHE (Optional)
# =============================================================================

# Redis configuration (for caching and sessions)
REDIS_URL=redis://localhost:6379
REDIS_PASSWORD=your-redis-password

# Database connection pool settings
DB_POOL_MIN=2
DB_POOL_MAX=10
DB_POOL_IDLE_TIMEOUT=30000

# Cache configuration
CACHE_TTL_DEFAULT=300
CACHE_TTL_TRAINING=300
CACHE_TTL_USER=600
CACHE_TTL_STATIC=3600
```

### 📁 File Storage

```env
# =============================================================================
# FILE STORAGE (Optional)
# =============================================================================

# Maximum file upload size (in bytes)
NEXT_PUBLIC_MAX_FILE_SIZE=10485760

# Allowed file types for uploads
NEXT_PUBLIC_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.jpg,.jpeg,.png,.mp4,.mov

# AWS S3 configuration (if using S3 for additional storage)
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1
AWS_S3_BUCKET=your-s3-bucket

# Cloudinary configuration (if using Cloudinary)
CLOUDINARY_CLOUD_NAME=your-cloud-name
CLOUDINARY_API_KEY=your-api-key
CLOUDINARY_API_SECRET=your-api-secret
```

### 🔄 Rate Limiting

```env
# =============================================================================
# RATE LIMITING (Optional)
# =============================================================================

# Rate limiting configuration
RATE_LIMIT_MAX=100
RATE_LIMIT_WINDOW=900000

# Auth-specific rate limits
AUTH_RATE_LIMIT_MAX=5
AUTH_RATE_LIMIT_WINDOW=60000

# Upload rate limits
UPLOAD_RATE_LIMIT_MAX=10
UPLOAD_RATE_LIMIT_WINDOW=60000

# Search rate limits
SEARCH_RATE_LIMIT_MAX=50
SEARCH_RATE_LIMIT_WINDOW=60000
```

## 🌍 Environment-Specific Configurations

### Development Environment

```env
# .env.local (Development)
NODE_ENV=development
NEXT_PUBLIC_APP_URL=http://localhost:3000
DEBUG=true
LOG_LEVEL=debug

# Supabase (Development project)
NEXT_PUBLIC_SUPABASE_URL=https://dev-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=dev-anon-key
SUPABASE_SERVICE_ROLE_KEY=dev-service-role-key

# Development-specific settings
DISABLE_RATE_LIMITING=true
ENABLE_MOCK_DATA=true
SKIP_EMAIL_VERIFICATION=true
```

### Staging Environment

```env
# .env.staging
NODE_ENV=staging
NEXT_PUBLIC_APP_URL=https://staging.your-domain.com
DEBUG=false
LOG_LEVEL=info

# Supabase (Staging project)
NEXT_PUBLIC_SUPABASE_URL=https://staging-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=staging-anon-key
SUPABASE_SERVICE_ROLE_KEY=staging-service-role-key

# Staging-specific settings
ENABLE_PERFORMANCE_MONITORING=true
SENTRY_ENVIRONMENT=staging
```

### Production Environment

```env
# .env.production
NODE_ENV=production
NEXT_PUBLIC_APP_URL=https://your-domain.com
DEBUG=false
LOG_LEVEL=error

# Supabase (Production project)
NEXT_PUBLIC_SUPABASE_URL=https://prod-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=prod-anon-key
SUPABASE_SERVICE_ROLE_KEY=prod-service-role-key

# Production security
ENCRYPTION_KEY=production-encryption-key
FIELD_ENCRYPTION_KEY=production-field-key
JWT_SECRET=production-jwt-secret

# Production monitoring
SENTRY_DSN=production-sentry-dsn
NEXT_PUBLIC_ANALYTICS_ID=production-analytics-id
```

## 🔧 Platform-Specific Setup

### Vercel Environment Variables

```bash
# Set environment variables in Vercel
vercel env add NEXT_PUBLIC_SUPABASE_URL production
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY production
vercel env add SUPABASE_SERVICE_ROLE_KEY production
vercel env add ENCRYPTION_KEY production
vercel env add SENTRY_DSN production

# Preview environment
vercel env add NEXT_PUBLIC_SUPABASE_URL preview
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY preview
vercel env add SUPABASE_SERVICE_ROLE_KEY preview

# Development environment
vercel env add NEXT_PUBLIC_SUPABASE_URL development
vercel env add NEXT_PUBLIC_SUPABASE_ANON_KEY development
vercel env add SUPABASE_SERVICE_ROLE_KEY development
```

### Netlify Environment Variables

```bash
# Set environment variables in Netlify
netlify env:set NEXT_PUBLIC_SUPABASE_URL "your-supabase-url"
netlify env:set NEXT_PUBLIC_SUPABASE_ANON_KEY "your-anon-key"
netlify env:set SUPABASE_SERVICE_ROLE_KEY "your-service-role-key"
netlify env:set ENCRYPTION_KEY "your-encryption-key"
```

### Docker Environment Variables

```dockerfile
# Dockerfile environment variables
ENV NODE_ENV=production
ENV NEXT_PUBLIC_APP_URL=https://your-domain.com
ENV NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
ENV NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
ENV SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
```

## 🔍 Environment Variable Validation

### Validation Schema

```typescript
// lib/env-validation.ts
import { z } from 'zod'

const envSchema = z.object({
  // Required variables
  NODE_ENV: z.enum(['development', 'staging', 'production']),
  NEXT_PUBLIC_SUPABASE_URL: z.string().url(),
  NEXT_PUBLIC_SUPABASE_ANON_KEY: z.string().min(1),
  SUPABASE_SERVICE_ROLE_KEY: z.string().min(1),
  NEXT_PUBLIC_APP_URL: z.string().url(),

  // Security variables (required in production)
  ENCRYPTION_KEY: z.string().min(32).optional(),
  FIELD_ENCRYPTION_KEY: z.string().min(32).optional(),
  JWT_SECRET: z.string().min(32).optional(),

  // Optional variables
  SENTRY_DSN: z.string().url().optional(),
  NEXT_PUBLIC_ANALYTICS_ID: z.string().optional(),
  REDIS_URL: z.string().url().optional(),
  
  // Email configuration
  SMTP_HOST: z.string().optional(),
  SMTP_PORT: z.string().transform(Number).optional(),
  SMTP_USER: z.string().email().optional(),
  SMTP_PASS: z.string().optional(),

  // Rate limiting
  RATE_LIMIT_MAX: z.string().transform(Number).default('100'),
  RATE_LIMIT_WINDOW: z.string().transform(Number).default('900000'),

  // File upload
  NEXT_PUBLIC_MAX_FILE_SIZE: z.string().transform(Number).default('10485760'),
  NEXT_PUBLIC_ALLOWED_FILE_TYPES: z.string().default('.pdf,.doc,.docx,.jpg,.jpeg,.png')
})

// Validate environment variables
export function validateEnv() {
  try {
    const env = envSchema.parse(process.env)
    
    // Additional production checks
    if (env.NODE_ENV === 'production') {
      if (!env.ENCRYPTION_KEY) {
        throw new Error('ENCRYPTION_KEY is required in production')
      }
      if (!env.FIELD_ENCRYPTION_KEY) {
        throw new Error('FIELD_ENCRYPTION_KEY is required in production')
      }
      if (!env.JWT_SECRET) {
        throw new Error('JWT_SECRET is required in production')
      }
    }
    
    return env
  } catch (error) {
    console.error('❌ Environment validation failed:')
    console.error(error.message)
    process.exit(1)
  }
}

// Export validated environment
export const env = validateEnv()
```

### Runtime Validation

```typescript
// lib/env-check.ts
export function checkRequiredEnvVars() {
  const required = [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'SUPABASE_SERVICE_ROLE_KEY',
    'NEXT_PUBLIC_APP_URL'
  ]

  const missing = required.filter(key => !process.env[key])

  if (missing.length > 0) {
    throw new Error(
      `Missing required environment variables: ${missing.join(', ')}\n` +
      'Please check your .env.local file or deployment configuration.'
    )
  }
}

// Check on application startup
if (typeof window === 'undefined') {
  checkRequiredEnvVars()
}
```

## 🔒 Security Best Practices

### Environment Variable Security

```typescript
// lib/env-security.ts
export const securityGuidelines = {
  // Never expose these in client-side code
  serverOnly: [
    'SUPABASE_SERVICE_ROLE_KEY',
    'ENCRYPTION_KEY',
    'FIELD_ENCRYPTION_KEY',
    'JWT_SECRET',
    'SMTP_PASS',
    'AWS_SECRET_ACCESS_KEY',
    'REDIS_PASSWORD'
  ],

  // Safe to expose in client-side code (prefixed with NEXT_PUBLIC_)
  clientSafe: [
    'NEXT_PUBLIC_SUPABASE_URL',
    'NEXT_PUBLIC_SUPABASE_ANON_KEY',
    'NEXT_PUBLIC_APP_URL',
    'NEXT_PUBLIC_ANALYTICS_ID',
    'NEXT_PUBLIC_MAX_FILE_SIZE'
  ],

  // Generate secure keys
  generateSecureKey: (length = 32) => {
    return require('crypto').randomBytes(length).toString('hex')
  },

  // Validate key strength
  validateKeyStrength: (key: string, minLength = 32) => {
    if (key.length < minLength) {
      throw new Error(`Key must be at least ${minLength} characters long`)
    }
    
    if (!/^[a-f0-9]+$/i.test(key)) {
      throw new Error('Key must be a valid hexadecimal string')
    }
    
    return true
  }
}
```

### Key Generation Script

```bash
#!/bin/bash
# scripts/generate-keys.sh

echo "🔐 Generating secure keys for production..."

echo "ENCRYPTION_KEY=$(openssl rand -hex 32)"
echo "FIELD_ENCRYPTION_KEY=$(openssl rand -hex 32)"
echo "JWT_SECRET=$(openssl rand -hex 64)"
echo "SESSION_SECRET=$(openssl rand -hex 32)"
echo "CSRF_SECRET=$(openssl rand -hex 32)"

echo ""
echo "⚠️  Store these keys securely and never commit them to version control!"
echo "💡 Add them to your deployment platform's environment variable settings."
```

## 📝 Environment File Templates

### Development Template

```env
# .env.local.example
# Copy this file to .env.local and fill in your values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://your-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NEXT_PUBLIC_APP_URL=http://localhost:3000
NODE_ENV=development

# =============================================================================
# OPTIONAL CONFIGURATION
# =============================================================================

# Analytics (optional)
# NEXT_PUBLIC_ANALYTICS_ID=G-XXXXXXXXXX

# Error tracking (optional)
# SENTRY_DSN=https://<EMAIL>/project-id

# Email service (optional)
# SMTP_HOST=smtp.gmail.com
# SMTP_PORT=587
# SMTP_USER=<EMAIL>
# SMTP_PASS=your-app-password

# Rate limiting (optional)
# RATE_LIMIT_MAX=100
# RATE_LIMIT_WINDOW=900000

# File upload limits (optional)
# NEXT_PUBLIC_MAX_FILE_SIZE=10485760
# NEXT_PUBLIC_ALLOWED_FILE_TYPES=.pdf,.doc,.docx,.jpg,.jpeg,.png
```

### Production Template

```env
# .env.production.example
# Production environment variables template

# =============================================================================
# SUPABASE CONFIGURATION (Required)
# =============================================================================
NEXT_PUBLIC_SUPABASE_URL=https://prod-project-ref.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=prod-anon-key
SUPABASE_SERVICE_ROLE_KEY=prod-service-role-key

# =============================================================================
# APPLICATION CONFIGURATION (Required)
# =============================================================================
NEXT_PUBLIC_APP_URL=https://your-domain.com
NODE_ENV=production

# =============================================================================
# SECURITY CONFIGURATION (Required)
# =============================================================================
ENCRYPTION_KEY=your-32-byte-hex-encryption-key
FIELD_ENCRYPTION_KEY=your-32-byte-field-encryption-key
JWT_SECRET=your-64-byte-jwt-secret

# =============================================================================
# MONITORING & ANALYTICS (Recommended)
# =============================================================================
SENTRY_DSN=https://<EMAIL>/project-id
NEXT_PUBLIC_ANALYTICS_ID=G-XXXXXXXXXX

# =============================================================================
# EMAIL SERVICE (Optional)
# =============================================================================
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-secure-app-password

# =============================================================================
# PERFORMANCE & CACHING (Optional)
# =============================================================================
REDIS_URL=redis://your-redis-instance:6379
CACHE_TTL_DEFAULT=300
CACHE_TTL_TRAINING=300
CACHE_TTL_USER=600
```

---

**Next**: Learn about [Troubleshooting](troubleshooting.md) common deployment and runtime issues.
