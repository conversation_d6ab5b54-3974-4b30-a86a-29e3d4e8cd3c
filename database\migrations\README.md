# Database Migrations

This directory contains database migration files for the BPO Training Platform. Migrations are used to evolve the database schema over time.

## Migration Files

Migration files follow a naming convention of `XX_descriptive_name.sql` where `XX` is a sequential number to ensure migrations are applied in the correct order.

Current migrations:
- `01_initial_schema.sql` - Initial database schema
- `02_validation_triggers.sql` - Validation trigger functions
- `03_fix_bpo_teams_rls.sql` - Row-level security fixes for BPO teams
- `04_secure_bpo_teams_rls.sql` - Enhanced security for BPO teams access
- `05_fix_users_rls.sql` - Row-level security fixes for users
- `06_update_lessons_schema.sql` - Added columns for lesson types, video_url, and media_url

## How Migrations Are Applied

There are several ways to apply migrations:

### 1. Through Supabase Interface

Migrations can be applied directly through the Supabase web interface when deploying schema changes.

### 2. Using the CLI Script

We've created a script to apply migrations programmatically:

```bash
node database/scripts/apply-migrations.js
```

This script requires environment variables:
- `NEXT_PUBLIC_SUPABASE_URL`
- `SUPABASE_SERVICE_ROLE_KEY`

### 3. Automatic Application

The application includes a mechanism to automatically check and apply missing schema elements at startup.

## Adding a New Migration

To add a new migration:

1. Create a new SQL file in this directory following the naming convention
2. Add the necessary SQL commands to make your changes
3. If needed, update corresponding TypeScript types in `types/supabase.ts`
4. For critical migrations, consider adding a JavaScript helper in `database/scripts/`

## Best Practices

1. Always make migrations idempotent (safe to run multiple times)
2. Use `IF EXISTS` and `IF NOT EXISTS` clauses
3. Include comments explaining what the migration does
4. Keep individual migrations focused on a specific change
5. Update TypeScript types to match schema changes
