# Prevention Checklist - BPO Training Platform

Use this checklist before making changes to prevent common errors.

## 🚀 Pre-Development Checklist

### Before Starting New Features
- [ ] Review similar existing implementations
- [ ] Check error database for related issues
- [ ] Understand data flow and dependencies
- [ ] Plan error handling strategy

## 🔧 Development Checklist

### Supabase & Database Operations
- [ ] **Correct Imports**: 
  - `createRouteHandlerClient` from `@supabase/auth-helpers-nextjs`
  - `createClient` (service role) from `@supabase/supabase-js`
- [ ] **Authentication**: Verify user session before operations
- [ ] **Authorization**: Check user permissions and roles
- [ ] **Error Handling**: Wrap database operations in try-catch
- [ ] **Null Checks**: Validate data exists before using
- [ ] **Service Role**: Use service role for admin operations

### API Routes
- [ ] **Input Validation**: Validate all request parameters
- [ ] **Error Responses**: Return consistent error format
- [ ] **Status Codes**: Use appropriate HTTP status codes
- [ ] **Logging**: Add comprehensive logging for debugging
- [ ] **Environment Variables**: Check all required env vars exist
- [ ] **CORS**: Handle cross-origin requests if needed

### Frontend Components
- [ ] **Function Order**: Define helper functions before use
- [ ] **Null Safety**: Add null checks for props and data
- [ ] **Error Boundaries**: Implement error boundaries for critical components
- [ ] **Loading States**: Handle loading and error states
- [ ] **Type Safety**: Use TypeScript properly
- [ ] **JSX Structure**: Ensure all tags are properly closed

### File Operations
- [ ] **File Validation**: Check file type, size, and format
- [ ] **Storage Paths**: Use consistent file naming conventions
- [ ] **Cleanup**: Remove files if database operations fail
- [ ] **Permissions**: Verify storage bucket permissions
- [ ] **Error Handling**: Handle upload/download failures gracefully

## 🧪 Testing Checklist

### Before Committing
- [ ] **Local Build**: Run `npm run build` successfully
- [ ] **Type Check**: Run `npm run type-check` (if available)
- [ ] **Lint**: Run `npm run lint` and fix issues
- [ ] **Manual Testing**: Test the feature manually
- [ ] **Error Scenarios**: Test error conditions
- [ ] **Browser Console**: Check for console errors

### CRUD Operations Testing
- [ ] **Create**: Test file upload with various file types
- [ ] **Read**: Test file listing and preview
- [ ] **Update**: Test file renaming and metadata updates
- [ ] **Delete**: Test file deletion and cleanup
- [ ] **Error Cases**: Test with invalid data, large files, etc.

### Authentication Testing
- [ ] **Logged In**: Test with authenticated user
- [ ] **Logged Out**: Test behavior when not authenticated
- [ ] **Wrong Role**: Test with different user roles
- [ ] **Session Expiry**: Test with expired sessions

## 🚀 Deployment Checklist

### Before Pushing to Production
- [ ] **Environment Variables**: Verify all env vars in production
- [ ] **Database Schema**: Ensure schema changes are applied
- [ ] **Storage Buckets**: Verify bucket permissions and policies
- [ ] **API Endpoints**: Test all new API routes
- [ ] **Error Monitoring**: Ensure error logging is working
- [ ] **Rollback Plan**: Have a rollback strategy ready

### Post-Deployment
- [ ] **Smoke Test**: Test critical user flows
- [ ] **Error Monitoring**: Check for new errors in logs
- [ ] **Performance**: Monitor response times
- [ ] **User Feedback**: Monitor for user-reported issues

## 🔍 Code Review Checklist

### For Reviewers
- [ ] **Import Statements**: Check Supabase imports are correct
- [ ] **Error Handling**: Verify comprehensive error handling
- [ ] **Security**: Check for potential security issues
- [ ] **Performance**: Look for potential performance issues
- [ ] **Consistency**: Ensure code follows project patterns
- [ ] **Documentation**: Check if documentation needs updates

### Common Red Flags
- [ ] **Mixed Imports**: Supabase imports from wrong packages
- [ ] **Missing Error Handling**: Operations without try-catch
- [ ] **Hardcoded Values**: URLs, IDs, or credentials in code
- [ ] **Temporal Dead Zone**: Functions used before definition
- [ ] **Missing Null Checks**: Accessing properties without validation
- [ ] **Inconsistent Patterns**: Different error handling approaches

## 📚 Quick Reference

### Supabase Import Patterns
```javascript
// ✅ CORRECT
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'

// ❌ WRONG
import { createRouteHandlerClient, createClient } from '@supabase/auth-helpers-nextjs'
```

### Error Response Pattern
```javascript
// ✅ CONSISTENT ERROR FORMAT
return NextResponse.json({
  error: 'Brief error message',
  details: 'Detailed error information',
  step: 'where_error_occurred'
}, { status: 500 })
```

### Component Function Order
```javascript
// ✅ CORRECT ORDER
export function MyComponent() {
  // 1. State and refs
  const [state, setState] = useState()
  
  // 2. Helper functions
  const helperFunction = () => { ... }
  
  // 3. Effect hooks
  useEffect(() => { ... }, [])
  
  // 4. Event handlers
  const handleClick = () => { ... }
  
  // 5. Render
  return <div>...</div>
}
```

## 🎯 Remember

- **Prevention is better than debugging**
- **Document as you go**
- **Test early and often**
- **Learn from every error**
- **Share knowledge with the team**
