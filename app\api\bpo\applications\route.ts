import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

export async function GET(request: NextRequest) {
  try {
    // Get authenticated user
    const supabase = createRouteHandlerClient({ cookies })
    const { data: { session }, error: authError } = await supabase.auth.getSession()
    
    if (authError || !session) {
      return NextResponse.json(
        { error: 'Authentication required' },
        { status: 401 }
      )
    }

    // Create admin client to bypass RLS
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL || '',
      process.env.SUPABASE_SERVICE_ROLE_KEY || '',
      {
        auth: {
          autoRefreshToken: false,
          persistSession: false,
        },
      }
    )

    // Get BPO team membership for current user
    const { data: bpoTeam, error: bpoTeamError } = await adminClient
      .from('bpo_teams')
      .select('bpo_id')
      .eq('user_id', session.user.id)
      .single()

    if (bpoTeamError || !bpoTeam) {
      return NextResponse.json(
        { error: 'BPO team membership not found' },
        { status: 404 }
      )
    }

    // Get job postings for this BPO
    const { data: jobPostings, error: jobPostingsError } = await adminClient
      .from('job_postings')
      .select('id')
      .eq('bpo_id', bpoTeam.bpo_id)

    if (jobPostingsError) {
      console.error('Error fetching job postings:', jobPostingsError)
      return NextResponse.json(
        { error: 'Failed to fetch job postings' },
        { status: 500 }
      )
    }

    if (!jobPostings || jobPostings.length === 0) {
      return NextResponse.json({
        success: true,
        applications: [],
        stats: {
          total: 0,
          submitted: 0,
          reviewing: 0,
          accepted: 0,
          rejected: 0,
          interview_scheduled: 0
        }
      })
    }

    const jobIds = jobPostings.map(job => job.id)

    // Get applications for BPO's job postings
    const { data: applications, error: applicationsError } = await adminClient
      .from('applications')
      .select(`
        id,
        status,
        submitted_at,
        reviewed_at,
        job_id,
        prospect_id,
        prospects!inner(
          id,
          user_id,
          users!prospects_user_id_fkey(
            full_name,
            email
          )
        ),
        job_postings!inner(
          id,
          title
        )
      `)
      .in('job_id', jobIds)
      .order('submitted_at', { ascending: false })

    if (applicationsError) {
      console.error('Error fetching applications:', applicationsError)
      return NextResponse.json(
        { error: 'Failed to fetch applications' },
        { status: 500 }
      )
    }

    // Calculate stats
    const stats = {
      total: applications?.length || 0,
      submitted: applications?.filter(app => app.status === 'submitted').length || 0,
      reviewing: applications?.filter(app => app.status === 'reviewing').length || 0,
      accepted: applications?.filter(app => app.status === 'accepted').length || 0,
      rejected: applications?.filter(app => app.status === 'rejected').length || 0,
      interview_scheduled: applications?.filter(app => app.status === 'interview_scheduled').length || 0
    }

    return NextResponse.json({
      success: true,
      applications: applications || [],
      stats
    })

  } catch (error: any) {
    console.error('BPO applications API error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
