# Import Errors - BPO Training Platform

Common import-related errors and their solutions.

## 🎯 Common Patterns

### Supabase Import Issues

#### Pattern: Wrong Package for createClient
**Error**: `(0 , l.createClient) is not a function`
**Cause**: Importing `createClient` from wrong package
**Solution**: Use correct package for each function

```javascript
// ❌ WRONG
import { createRouteHandlerClient, createClient } from '@supabase/auth-helpers-nextjs'

// ✅ CORRECT
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
```

#### Pattern: Missing Supabase Dependencies
**Error**: `Module not found: Can't resolve '@supabase/...'`
**Cause**: Missing package installation
**Solution**: Install required packages

```bash
npm install @supabase/supabase-js @supabase/auth-helpers-nextjs
```

### Next.js Import Issues

#### Pattern: Server vs Client Imports
**Error**: `Module not found` or `Cannot use import statement outside a module`
**Cause**: Using server-side imports in client components or vice versa
**Solution**: Use appropriate imports for context

```javascript
// ✅ Server-side (API routes)
import { cookies } from 'next/headers'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'

// ✅ Client-side (components)
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
```

### TypeScript Import Issues

#### Pattern: Type Import Errors
**Error**: `Cannot find module '@/types/...' or its corresponding type declarations`
**Cause**: Missing type definitions or incorrect paths
**Solution**: Verify type files exist and paths are correct

```javascript
// ✅ CORRECT
import { Database } from '@/types/database.types'
import type { User } from '@supabase/auth-helpers-nextjs'
```

## 🔧 Quick Fixes

### Supabase Client Creation

```javascript
// ✅ USER SESSION CLIENT (for authenticated operations)
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
const supabase = createRouteHandlerClient({ cookies })

// ✅ SERVICE ROLE CLIENT (for admin operations)
import { createClient } from '@supabase/supabase-js'
const supabaseAdmin = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
)

// ✅ CLIENT COMPONENT (for frontend)
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
const supabase = createClientComponentClient()
```

### Common Import Patterns

```javascript
// ✅ API ROUTE IMPORTS
import { NextRequest, NextResponse } from 'next/server'
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'

// ✅ CLIENT COMPONENT IMPORTS
import { useState, useEffect } from 'react'
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs'
import { useRouter } from 'next/navigation'

// ✅ SERVER COMPONENT IMPORTS
import { createServerComponentClient } from '@supabase/auth-helpers-nextjs'
import { cookies } from 'next/headers'
```

## 🛡️ Prevention

1. **Use Import Templates**: Create snippets for common import patterns
2. **Check Documentation**: Always verify imports against official docs
3. **Test Imports**: Test new imports in development
4. **Code Review**: Have imports reviewed by team members
5. **IDE Setup**: Configure IDE to catch import errors early

## 📚 Reference

### Supabase Packages
- `@supabase/supabase-js`: Core Supabase client
- `@supabase/auth-helpers-nextjs`: Next.js specific helpers

### When to Use Each
- **createClient**: Service role operations, admin tasks
- **createRouteHandlerClient**: API routes with user sessions
- **createServerComponentClient**: Server components
- **createClientComponentClient**: Client components

### Environment Variables Required
```env
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
```
