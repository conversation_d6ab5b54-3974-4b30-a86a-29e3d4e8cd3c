# 🏗️ Architecture Overview

This document provides a comprehensive overview of the BPO Training Platform's architecture, including system design, data flow, and key components.

## 🎯 System Overview

The BPO Training Platform is a modern, scalable web application built with a **JAMstack architecture** using Next.js, Supabase, and TypeScript. It follows **microservices principles** with clear separation of concerns.

### High-Level Architecture

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│   (Next.js)     │◄──►│   (Supabase)    │◄──►│   (PostgreSQL)  │
│                 │    │                 │    │                 │
│ • React 18      │    │ • Auth          │    │ • RLS Policies  │
│ • TypeScript    │    │ • API           │    │ • Triggers      │
│ • Tailwind CSS  │    │ • Edge Functions│    │ • Indexes       │
│ • State Mgmt    │    │ • Storage       │    │ • Migrations    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │   External      │
                    │   Services      │
                    │                 │
                    │ • Email (SMTP)  │
                    │ • Analytics     │
                    │ • Monitoring    │
                    │ • CDN           │
                    └─────────────────┘
```

## 🏛️ Frontend Architecture

### Next.js App Router Structure

```
app/
├── (auth)/                 # Auth-related pages
│   ├── login/
│   ├── register/
│   └── layout.tsx
├── admin/                  # Platform admin pages
│   ├── dashboard/
│   ├── users/
│   ├── training/
│   └── layout.tsx
├── bpo/                    # BPO company pages
│   ├── dashboard/
│   ├── jobs/
│   ├── candidates/
│   └── layout.tsx
├── prospect/               # Job seeker pages
│   ├── dashboard/
│   ├── training/
│   ├── jobs/
│   └── layout.tsx
├── api/                    # API routes
│   ├── auth/
│   ├── training/
│   ├── jobs/
│   └── health/
├── globals.css
├── layout.tsx
└── page.tsx
```

### Component Architecture

```
components/
├── ui/                     # Reusable UI components
│   ├── button.tsx
│   ├── card.tsx
│   ├── form-components.tsx
│   ├── data-display.tsx
│   ├── layout-components.tsx
│   ├── module-card.tsx
│   ├── progress-indicator.tsx
│   ├── status-badge.tsx
│   └── optimized-image.tsx
├── auth/                   # Authentication components
│   ├── login-form.tsx
│   ├── register-form.tsx
│   └── auth-guard.tsx
├── training/               # Training-specific components
│   ├── module-list.tsx
│   ├── lesson-player.tsx
│   └── progress-tracker.tsx
├── admin/                  # Admin-specific components
│   ├── user-management.tsx
│   ├── analytics-dashboard.tsx
│   └── content-editor.tsx
└── shared/                 # Shared components
    ├── navigation.tsx
    ├── sidebar.tsx
    └── footer.tsx
```

### State Management Strategy

```typescript
// Global State (React Context + Zustand)
├── AuthContext              # User authentication state
├── ThemeContext            # UI theme and preferences
├── NotificationContext     # Toast notifications
└── PerformanceContext      # Performance monitoring

// Local State (React hooks)
├── useState                # Component-level state
├── useReducer             # Complex state logic
├── useOptimizedState      # Performance-optimized state
└── useDebouncedState      # Debounced state updates

// Server State (React Query/SWR)
├── useQuery               # Data fetching
├── useMutation           # Data mutations
├── useInfiniteQuery      # Paginated data
└── useOptimizedQuery     # Cached queries
```

## 🔧 Backend Architecture

### Supabase Services

```
Supabase Backend
├── Authentication
│   ├── JWT-based auth
│   ├── Row Level Security (RLS)
│   ├── Multi-factor authentication
│   └── Social login providers
├── Database (PostgreSQL)
│   ├── Tables with RLS policies
│   ├── Functions and triggers
│   ├── Indexes for performance
│   └── Real-time subscriptions
├── Storage
│   ├── File uploads (resumes, videos)
│   ├── Image optimization
│   └── CDN integration
├── Edge Functions
│   ├── Custom business logic
│   ├── Third-party integrations
│   └── Background jobs
└── Real-time
    ├── Live updates
    ├── Presence tracking
    └── Collaborative features
```

### API Design

```typescript
// RESTful API Structure
/api/
├── auth/
│   ├── login              # POST - User authentication
│   ├── logout             # POST - User logout
│   ├── register           # POST - User registration
│   └── refresh            # POST - Token refresh
├── users/
│   ├── [id]               # GET, PUT, DELETE - User CRUD
│   ├── profile            # GET, PUT - User profile
│   └── preferences        # GET, PUT - User settings
├── training/
│   ├── modules/           # GET, POST - Training modules
│   ├── modules/[id]       # GET, PUT, DELETE - Module CRUD
│   ├── lessons/[id]       # GET, PUT - Lesson content
│   ├── progress/          # GET, POST, PUT - Progress tracking
│   └── assessments/       # GET, POST - Assessments
├── jobs/
│   ├── postings/          # GET, POST - Job listings
│   ├── postings/[id]      # GET, PUT, DELETE - Job CRUD
│   ├── applications/      # GET, POST - Applications
│   └── applications/[id]  # GET, PUT - Application status
└── admin/
    ├── analytics/         # GET - Platform analytics
    ├── users/            # GET, POST, PUT, DELETE - User management
    └── content/          # GET, POST, PUT, DELETE - Content management
```

## 🗄️ Database Architecture

### Entity Relationship Diagram

```
Users (1) ──────────── (1) Prospects
  │                         │
  │                         │
  │                    (1) ─┴─ (M) Applications
  │                              │
  │                              │
  │                         (M) ─┴─ (1) Job Postings
  │                                      │
  │                                      │
  │                                 (M) ─┴─ (1) BPOs
  │                                              │
  │                                              │
  └─ (M) BPO Team Members (M) ──────────────────┘

Training Modules (1) ──── (M) Lessons (1) ──── (M) Activities
       │                                              │
       │                                              │
       └─ (M) Progress Records (M) ──────────────────┘
```

### Core Tables

```sql
-- Users table (authentication and basic info)
users (
  id UUID PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT NOT NULL,
  role user_role NOT NULL,
  status user_status NOT NULL,
  created_at TIMESTAMP DEFAULT NOW()
)

-- Prospects table (job seekers)
prospects (
  id UUID PRIMARY KEY,
  user_id UUID REFERENCES users(id),
  training_status training_status,
  profile_visibility BOOLEAN DEFAULT false,
  resume_url TEXT,
  intro_video_url TEXT
)

-- BPOs table (companies)
bpos (
  id UUID PRIMARY KEY,
  name TEXT NOT NULL,
  description TEXT,
  industry TEXT,
  website_url TEXT,
  created_by UUID REFERENCES users(id)
)

-- Training modules
training_modules (
  id UUID PRIMARY KEY,
  title TEXT NOT NULL,
  description TEXT,
  duration_minutes INTEGER,
  status TEXT DEFAULT 'draft',
  created_by UUID REFERENCES users(id)
)

-- Progress tracking
progress (
  id UUID PRIMARY KEY,
  prospect_id UUID REFERENCES prospects(id),
  activity_id UUID REFERENCES activities(id),
  status TEXT NOT NULL,
  score INTEGER,
  completed_at TIMESTAMP
)
```

### Row Level Security (RLS) Policies

```sql
-- Users can only see their own data
CREATE POLICY "Users can view own profile" ON users
  FOR SELECT USING (auth.uid() = id);

-- Prospects can view published training modules
CREATE POLICY "Prospects can view published modules" ON training_modules
  FOR SELECT USING (
    status = 'published' AND 
    EXISTS (SELECT 1 FROM prospects WHERE user_id = auth.uid())
  );

-- BPO admins can manage their company's data
CREATE POLICY "BPO admins manage company data" ON job_postings
  FOR ALL USING (
    bpo_id IN (
      SELECT bpo_id FROM bpo_team_members 
      WHERE user_id = auth.uid() AND role = 'admin'
    )
  );
```

## 🔄 Data Flow

### Authentication Flow

```
1. User Login Request
   ├── Frontend validates input
   ├── Supabase Auth verifies credentials
   ├── JWT token generated
   ├── User data fetched with RLS
   └── Client state updated

2. Authenticated Requests
   ├── JWT token in Authorization header
   ├── Supabase validates token
   ├── RLS policies applied
   ├── Data filtered by user permissions
   └── Response returned
```

### Training Module Flow

```
1. Module Creation (Admin)
   ├── Admin creates module
   ├── Lessons and activities added
   ├── Content validation
   ├── Module published
   └── Cache invalidated

2. Module Consumption (Prospect)
   ├── Prospect views modules
   ├── Progress tracking initiated
   ├── Lesson completion recorded
   ├── Assessments taken
   ├── Progress calculated
   └── Certificates generated
```

### Job Application Flow

```
1. Job Posting (BPO)
   ├── BPO creates job posting
   ├── Requirements specified
   ├── Posting published
   └── Notifications sent

2. Application Process (Prospect)
   ├── Prospect views job
   ├── Application submitted
   ├── Documents uploaded
   ├── Status tracking enabled
   └── BPO notified

3. Review Process (BPO)
   ├── Applications reviewed
   ├── Candidates shortlisted
   ├── Interviews scheduled
   ├── Decisions made
   └── Notifications sent
```

## ⚡ Performance Architecture

### Caching Strategy

```typescript
// Multi-layer caching system
Cache Layers:
├── Browser Cache (Service Worker)
│   ├── Static assets (1 year)
│   ├── API responses (5 minutes)
│   └── Images (30 days)
├── Application Cache (LRU)
│   ├── Training modules (5 minutes)
│   ├── User data (10 minutes)
│   ├── API responses (2 minutes)
│   └── Static content (1 hour)
├── CDN Cache (Vercel/Cloudflare)
│   ├── Static files
│   ├── Images
│   └── API responses
└── Database Cache (Supabase)
    ├── Query results
    ├── Connection pooling
    └── Read replicas
```

### Performance Optimizations

```typescript
// Code splitting and lazy loading
const TrainingModule = lazy(() => import('./TrainingModule'))
const AdminDashboard = lazy(() => import('./AdminDashboard'))

// Image optimization
<OptimizedImage
  src="/training-cover.jpg"
  alt="Training Module"
  width={400}
  height={300}
  priority={false}
  lazy={true}
/>

// Database query optimization
const optimizedQuery = useOptimizedQuery(
  ['training-modules', userId],
  () => getTrainingModules(userId),
  {
    staleTime: 5 * 60 * 1000, // 5 minutes
    cacheTime: 10 * 60 * 1000, // 10 minutes
  }
)
```

## 🔐 Security Architecture

### Authentication & Authorization

```
Security Layers:
├── Frontend Security
│   ├── Input validation
│   ├── XSS protection
│   ├── CSRF tokens
│   └── Secure headers
├── API Security
│   ├── JWT validation
│   ├── Rate limiting
│   ├── Request validation
│   └── Audit logging
├── Database Security
│   ├── Row Level Security (RLS)
│   ├── Encrypted connections
│   ├── Backup encryption
│   └── Access logging
└── Infrastructure Security
    ├── HTTPS enforcement
    ├── Security headers
    ├── DDoS protection
    └── Vulnerability scanning
```

### Data Protection

```sql
-- Encryption at rest
ALTER TABLE users ALTER COLUMN email 
SET DEFAULT encrypt(email, 'encryption_key');

-- Audit logging
CREATE TABLE audit_logs (
  id UUID PRIMARY KEY DEFAULT uuid_generate_v4(),
  user_id UUID REFERENCES users(id),
  action TEXT NOT NULL,
  table_name TEXT NOT NULL,
  old_values JSONB,
  new_values JSONB,
  created_at TIMESTAMP DEFAULT NOW()
);
```

## 📊 Monitoring & Analytics

### Performance Monitoring

```typescript
// Web Vitals tracking
import { getCLS, getFID, getFCP, getLCP, getTTFB } from 'web-vitals'

// Custom metrics
performanceMonitor.recordMetric('page-load-time', duration)
performanceMonitor.recordMetric('api-response-time', responseTime)
performanceMonitor.recordMetric('user-interaction', interactionType)
```

### Error Tracking

```typescript
// Error boundary with reporting
class ErrorBoundary extends Component {
  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    // Log to monitoring service
    errorTracker.captureException(error, {
      extra: errorInfo,
      user: getCurrentUser(),
      tags: { component: 'training-module' }
    })
  }
}
```

## 🚀 Deployment Architecture

### Production Environment

```
Production Stack:
├── Frontend (Vercel)
│   ├── Next.js application
│   ├── Static asset optimization
│   ├── Edge functions
│   └── Global CDN
├── Backend (Supabase)
│   ├── Database (PostgreSQL)
│   ├── Authentication service
│   ├── Storage service
│   └── Edge functions
├── Monitoring
│   ├── Application monitoring
│   ├── Error tracking
│   ├── Performance metrics
│   └── Uptime monitoring
└── Security
    ├── SSL certificates
    ├── DDoS protection
    ├── WAF rules
    └── Security scanning
```

### CI/CD Pipeline

```yaml
# GitHub Actions workflow
Deploy Pipeline:
├── Code Quality Checks
│   ├── ESLint
│   ├── TypeScript
│   ├── Prettier
│   └── Tests
├── Build Process
│   ├── Next.js build
│   ├── Asset optimization
│   ├── Bundle analysis
│   └── Performance audit
├── Deployment
│   ├── Staging deployment
│   ├── Integration tests
│   ├── Production deployment
│   └── Health checks
└── Post-deployment
    ├── Performance monitoring
    ├── Error tracking
    ├── User analytics
    └── Rollback capability
```

---

**Next**: Explore the [Database Schema](database-schema.md) for detailed database documentation.
