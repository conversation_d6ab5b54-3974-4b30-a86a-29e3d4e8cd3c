-- Create BPO schedules table for managing team member availability
-- This is a simplified version without complex GIST indexes

CREATE TABLE IF NOT EXISTS bpo_schedules (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    day_of_week INTEGER NOT NULL CHECK (day_of_week >= 0 AND day_of_week <= 6), -- 0 = Sunday, 6 = Saturday
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_available BOOLEAN DEFAULT true,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT timezone('utc'::text, now()) NOT NULL,
    
    -- Ensure end time is after start time
    CONSTRAINT valid_time_range CHECK (end_time > start_time)
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_user_id ON bpo_schedules(user_id);
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_day_of_week ON bpo_schedules(day_of_week);
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_availability ON bpo_schedules(is_available);
CREATE INDEX IF NOT EXISTS idx_bpo_schedules_user_day ON bpo_schedules(user_id, day_of_week);

-- Create function to prevent overlapping time slots
CREATE OR REPLACE FUNCTION check_schedule_overlap()
RETURNS TRIGGER AS $$
BEGIN
    -- Only check for overlaps if the slot is available
    IF NEW.is_available = true THEN
        -- Check if there are any overlapping slots for the same user and day
        IF EXISTS (
            SELECT 1 FROM bpo_schedules 
            WHERE user_id = NEW.user_id 
            AND day_of_week = NEW.day_of_week 
            AND is_available = true
            AND id != COALESCE(NEW.id, '00000000-0000-0000-0000-000000000000'::uuid)
            AND (
                (NEW.start_time >= start_time AND NEW.start_time < end_time) OR
                (NEW.end_time > start_time AND NEW.end_time <= end_time) OR
                (NEW.start_time <= start_time AND NEW.end_time >= end_time)
            )
        ) THEN
            RAISE EXCEPTION 'Time slot overlaps with existing schedule for this day';
        END IF;
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create updated_at trigger function
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = timezone('utc'::text, now());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers
CREATE TRIGGER check_bpo_schedules_overlap
    BEFORE INSERT OR UPDATE ON bpo_schedules
    FOR EACH ROW
    EXECUTE FUNCTION check_schedule_overlap();

CREATE TRIGGER update_bpo_schedules_updated_at 
    BEFORE UPDATE ON bpo_schedules 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Enable RLS
ALTER TABLE bpo_schedules ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own schedules" ON bpo_schedules
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own schedules" ON bpo_schedules
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own schedules" ON bpo_schedules
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own schedules" ON bpo_schedules
    FOR DELETE USING (auth.uid() = user_id);

-- Grant permissions
GRANT ALL ON bpo_schedules TO authenticated;

-- Add helpful comments
COMMENT ON TABLE bpo_schedules IS 'Stores weekly availability schedules for BPO team members';
COMMENT ON COLUMN bpo_schedules.day_of_week IS '0 = Sunday, 1 = Monday, ..., 6 = Saturday';
COMMENT ON COLUMN bpo_schedules.start_time IS 'Start time of availability slot in HH:MM format';
COMMENT ON COLUMN bpo_schedules.end_time IS 'End time of availability slot in HH:MM format';
COMMENT ON COLUMN bpo_schedules.is_available IS 'Whether this time slot is available for scheduling interviews';
