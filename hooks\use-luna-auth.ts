'use client';

import { useState, useEffect, useCallback } from 'react';
import { createClientComponentClient } from '@supabase/auth-helpers-nextjs';
import { AuthUser, getAuthenticatedUser, switchUserContext } from '@/lib/auth';

interface LunaAuthState {
  user: AuthUser | null;
  loading: boolean;
  error: string | null;
}

interface LunaAuthActions {
  refreshUser: () => Promise<void>;
  switchContext: (contextType: 'individual' | 'organization', organizationId?: string) => Promise<boolean>;
  signOut: () => Promise<void>;
}

export function useLunaAuth(): LunaAuthState & LunaAuthActions {
  const [state, setState] = useState<LunaAuthState>({
    user: null,
    loading: true,
    error: null
  });

  const supabase = createClientComponentClient();

  const refreshUser = useCallback(async () => {
    try {
      setState(prev => ({ ...prev, loading: true, error: null }));
      
      const authResult = await getAuthenticatedUser();
      
      if (authResult.user) {
        setState({
          user: authResult.user,
          loading: false,
          error: null
        });
      } else {
        setState({
          user: null,
          loading: false,
          error: authResult.error || 'Authentication failed'
        });
      }
    } catch (error) {
      console.error('Auth refresh error:', error);
      setState({
        user: null,
        loading: false,
        error: 'Failed to refresh authentication'
      });
    }
  }, []);

  const switchContext = useCallback(async (
    contextType: 'individual' | 'organization',
    organizationId?: string
  ): Promise<boolean> => {
    if (!state.user) return false;

    try {
      const result = await switchUserContext(state.user.id, contextType, organizationId);
      
      if (result.success) {
        // Refresh user data to get updated context
        await refreshUser();
        return true;
      } else {
        setState(prev => ({ ...prev, error: result.error || 'Context switch failed' }));
        return false;
      }
    } catch (error) {
      console.error('Context switch error:', error);
      setState(prev => ({ ...prev, error: 'Context switch failed' }));
      return false;
    }
  }, [state.user, refreshUser]);

  const signOut = useCallback(async () => {
    try {
      await supabase.auth.signOut();
      setState({
        user: null,
        loading: false,
        error: null
      });
    } catch (error) {
      console.error('Sign out error:', error);
      setState(prev => ({ ...prev, error: 'Sign out failed' }));
    }
  }, [supabase]);

  // Initialize auth state and listen for changes
  useEffect(() => {
    // Initial auth check
    refreshUser();

    // Listen for auth state changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (event === 'SIGNED_IN' || event === 'TOKEN_REFRESHED') {
          await refreshUser();
        } else if (event === 'SIGNED_OUT') {
          setState({
            user: null,
            loading: false,
            error: null
          });
        }
      }
    );

    return () => {
      subscription.unsubscribe();
    };
  }, [refreshUser, supabase]);

  return {
    ...state,
    refreshUser,
    switchContext,
    signOut
  };
}

// Utility hooks for specific access patterns
export function useRequirePlatformAdmin() {
  const auth = useLunaAuth();
  
  return {
    ...auth,
    hasAccess: auth.user?.isPlatformAdmin || false
  };
}

export function useRequireOrgAccess(organizationId?: string) {
  const auth = useLunaAuth();
  
  const hasAccess = auth.user ? (
    auth.user.isPlatformAdmin ||
    auth.user.organizationMemberships.some(m => 
      !organizationId || m.organization_id === organizationId
    )
  ) : false;

  return {
    ...auth,
    hasAccess
  };
}

export function useRequireIndividualAccess() {
  const auth = useLunaAuth();
  
  const hasAccess = auth.user ? (
    auth.user.role === 'individual' || 
    auth.user.isPlatformAdmin
  ) : false;

  return {
    ...auth,
    hasAccess
  };
}

// Context-aware hooks
export function useCurrentContext() {
  const auth = useLunaAuth();
  
  return {
    context: auth.user?.currentContext,
    isIndividualContext: auth.user?.currentContext?.type === 'individual',
    isOrganizationContext: auth.user?.currentContext?.type === 'organization',
    currentOrganizationId: auth.user?.currentContext?.organization_id,
    availableOrganizations: auth.user?.organizationMemberships || []
  };
}
